"""
Celery应用配置文件
用于创建和配置Celery应用实例
"""
import os
from celery import Celery
from celery.signals import task_prerun, task_postrun, worker_process_init, worker_process_shutdown
from app.core.config import settings
from app.logging_config import logger

# 显示连接信息，方便调试
logger.info(f"Celery Broker URL: {settings.CELERY_BROKER_URL}")
logger.info(f"Celery Result Backend URL: {settings.CELERY_RESULT_BACKEND}")
logger.info(f"Redis URI: {settings.REDIS_URI}")

# 获取环境变量中的RedBeat Redis URL，如果存在则优先使用
# 如果环境变量中没有设置，则使用相同的Redis数据库作为RedBeat调度器存储
redbeat_redis_url = os.environ.get('REDBEAT_REDIS_URL')
if not redbeat_redis_url:
    # 获取配置中的Redis信息，确保使用正确的数据库编号
    host = settings.REDIS_HOST
    port = settings.REDIS_PORT
    password = settings.REDIS_PASSWORD or ""
    db = settings.REDIS_DB  # 使用配置的Redis数据库编号
    
    # 构建RedBeat Redis URL
    if password:
        redbeat_redis_url = f"redis://:{password}@{host}:{port}/{db}"
    else:
        redbeat_redis_url = f"redis://{host}:{port}/{db}"

    logger.info(f"使用配置的Redis数据库 DB={db} 作为RedBeat存储")

logger.info(f"RedBeat Redis URL: {redbeat_redis_url}")

# 创建Celery实例
celery_app = Celery(
    "kol_worker",  # 应用名称
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    # 包含所有任务模块
    include=[
        "app.worker.tasks.example",
        "app.worker.tasks.kol_data",
        # 可以在此处添加更多任务模块
    ],
)

# Celery配置
celery_app.conf.update(
    # 序列化
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    
    # 时区设置
    timezone="Asia/Shanghai",
    enable_utc=False,
    
    # 任务执行设置
    worker_concurrency=settings.CELERY_WORKER_CONCURRENCY,
    task_track_started=settings.CELERY_TASK_TRACK_STARTED,
    result_expires=settings.CELERY_RESULT_EXPIRES,
    
    # 连接设置
    broker_connection_retry=True,  # 连接断开时重试
    broker_connection_retry_on_startup=True,  # 启动时连接失败也重试（解决警告）
    broker_connection_max_retries=5,  # 最大重试次数
    
    # 任务路由配置 - 可以按需定义不同队列
    task_routes={
        'app.worker.tasks.kol_data.*': {'queue': 'kol_data'},  # KOL数据采集任务路由到kol_data队列
        'kol.collect_kol_data': {'queue': 'kol_data'},         # 通过任务名称路由
        'kol.check_status': {'queue': 'kol_data'},             # 状态检查任务
        'kol.cleanup_zombie_tasks': {'queue': 'maintenance'},  # 僵尸任务清理路由到maintenance队列
        # 'app.worker.tasks.heavy_task': {'queue': 'heavy'},      # 重任务路由到heavy队列
    },
    
    # 任务默认配置
    task_default_queue="default",  # 默认队列名
    
    # 任务重试设置
    task_acks_late=True,  # 任务完成后才确认，允许重试
    task_reject_on_worker_lost=True,  # worker异常终止时，任务会被重新加入队列
    
    # 监控设置
    worker_send_task_events=settings.CELERY_MONITORING_ENABLED,
    task_send_sent_event=settings.CELERY_MONITORING_ENABLED,
    
    # 工作进程设置
    worker_max_tasks_per_child=200,  # 工作进程处理多少任务后自动重启，防止内存泄漏
    worker_prefetch_multiplier=4,    # 预取任务数量，调低可减少单个worker过载
    
    # RedBeat调度器配置 - 使用Redis作为存储
    redbeat_redis_url=redbeat_redis_url,  # 显式设置RedBeat使用的Redis URL
    redbeat_key_prefix=settings.REDBEAT_KEY_PREFIX,  # 使用配置的键前缀
    redbeat_lock_key=settings.REDBEAT_LOCK_KEY,     # 使用配置的锁键
    redbeat_lock_timeout=settings.REDBEAT_LOCK_TIMEOUT,  # 使用配置的锁超时
)

# 记录RedBeat配置
logger.info(f"RedBeat配置:")
logger.info(f"- redbeat_redis_url: {celery_app.conf.get('redbeat_redis_url')}")
logger.info(f"- redbeat_key_prefix: {celery_app.conf.get('redbeat_key_prefix')}")
logger.info(f"- redbeat_lock_timeout: {celery_app.conf.get('redbeat_lock_timeout')}秒")

# 导入Beat定时任务配置
try:
    from app.worker.beat_schedule import beat_schedule, timezone, enable_utc
    
    # 更新Beat配置
    celery_app.conf.update(
        beat_schedule=beat_schedule,
        timezone=timezone,
        enable_utc=enable_utc,
    )
    logger.info("已加载Beat定时任务配置")
except ImportError:
    logger.warning("未找到Beat定时任务配置文件，跳过加载")
except Exception as e:
    logger.error(f"加载Beat定时任务配置失败: {e}")


# 创建基础Task类，用于提供数据库会话等共享资源
class BaseTask(celery_app.Task):
    """
    基础任务类，可以为所有任务提供共享功能
    例如数据库会话、错误处理等
    """
    abstract = True  # 抽象类，不会被注册为任务
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败回调"""
        logger.error(
            f"任务失败 | Task: {self.name} | ID: {task_id} | "
            f"Args: {args} | Kwargs: {kwargs} | Error: {exc}"
        )
        super().on_failure(exc, task_id, args, kwargs, einfo)
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功回调"""
        logger.info(
            f"任务成功 | Task: {self.name} | ID: {task_id} | "
            f"Args: {args} | Result: {retval if len(str(retval)) < 100 else 'Large Result'}"
        )
        super().on_success(retval, task_id, args, kwargs)
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """任务重试回调"""
        logger.warning(
            f"任务重试 | Task: {self.name} | ID: {task_id} | "
            f"Args: {args} | Kwargs: {kwargs} | Error: {exc}"
        )
        super().on_retry(exc, task_id, args, kwargs, einfo)
        
    def is_aborted(self):
        """
        检查任务是否被取消
        
        Returns:
            bool: 如果任务被取消则返回True，否则返回False
        """
        # 使用self.request.id获取任务ID
        task_id = self.request.id
        
        try:
            # 检查任务状态是否为REVOKED
            from celery.result import AsyncResult
            task_result = AsyncResult(task_id)
            return task_result.state == 'REVOKED'
        except Exception as e:
            # 如果检查失败，则假设任务未被取消
            logger.warning(f"检查任务取消状态失败: {str(e)}")
            return False


# 任务运行前信号处理
@task_prerun.connect
def task_prerun_handler(task_id, task, *args, **kwargs):
    """任务开始前的预处理"""
    logger.info(f"任务开始 | Task: {task.name} | ID: {task_id}")


# 任务运行后信号处理
@task_postrun.connect
def task_postrun_handler(task_id, task, retval, state, *args, **kwargs):
    """任务结束后的清理"""
    logger.info(f"任务结束 | Task: {task.name} | ID: {task_id} | State: {state}")
    
    # 强制触发垃圾回收
    try:
        import gc
        gc.collect()
    except Exception as e:
        logger.warning(f"触发垃圾回收失败: {str(e)}")


# 工作进程初始化信号
@worker_process_init.connect
def worker_process_init_handler(**kwargs):
    """工作进程初始化时的处理"""
    logger.info("工作进程已初始化")


# 工作进程关闭信号
@worker_process_shutdown.connect
def worker_process_shutdown_handler(**kwargs):
    """工作进程关闭时的处理"""
    logger.info("工作进程准备关闭")
    
    # 执行额外的清理工作
    try:
        import gc
        gc.collect()
    except Exception as e:
        logger.warning(f"工作进程关闭时清理资源失败: {str(e)}")


# 导出Celery应用实例和基础任务类供其他模块使用
__all__ = ["celery_app", "BaseTask"] 