"""
Celery Beat 定时任务配置
用于设置周期性任务的执行计划
"""
from celery.schedules import crontab

# 定时任务配置
beat_schedule = {
    # 每5分钟执行一次的示例任务
    'periodic-task-every-5-min': {
        'task': 'example.periodic_task',  # 任务名称
        'schedule': 300.0,  # 每300秒执行一次
        'args': (),
        'kwargs': {},
        'options': {'queue': 'periodic'},  # 使用专门的周期性任务队列
    },
    
    # 每天零点清理过期的KOL任务结果
    'daily-clean-expired-kol-results': {
        'task': 'kol.clean_expired_results',  # 与任务定义一致
        'schedule': crontab(hour=0, minute=0),  # 每天零点
        'args': [],
        'kwargs': {'expire_days': 30},  # 30天前的任务结果将被清理
        'options': {'queue': 'kol_data'},
    },
    
    # 每周一生成KOL任务统计报告
    'weekly-kol-task-report': {
        'task': 'kol.generate_report',  # 与任务定义一致
        'schedule': crontab(hour=7, minute=0, day_of_week=1),  # 每周一早上7点
        'args': [],
        'kwargs': {'report_type': 'weekly'},
        'options': {'queue': 'kol_data'},
    },
    
    # 每15分钟清理僵尸任务，确保任务状态正常
    'cleanup-zombie-tasks': {
        'task': 'kol.cleanup_zombie_tasks',  # 新添加的僵尸任务清理任务
        'schedule': 900.0,  # 每15分钟执行一次
        'args': [],
        'kwargs': {},
        'options': {'queue': 'maintenance'},  # 使用maintenance队列
    },
}

# 时区设置
timezone = 'Asia/Shanghai'

# 启用 UTC
enable_utc = False 