"""
KOL数据采集任务模块
负责执行长时间运行的KOL数据采集任务
"""
import time
from typing import Dict, List, Any, Optional, Tuple
from celery import shared_task
from celery.utils.log import get_task_logger
from app.worker.celery_app import BaseTask, celery_app
from app.db.session import SessionLocal
from app.services.tools import send_webhook
from app.services.kol_tools.tools import (
    _validate_params,
    _collect_source_data,
    _standardize_data,
    _collect_platform_data,
    _store_data_to_db,
    _store_high_potential_data,
    _send_to_feishu,
    _parse_table_url
)
from app.services.feishu_lark_service import Lark
from app.core.config import settings
from celery.result import AsyncResult
from celery import states

# 创建任务专用日志记录器
task_logger = get_task_logger(__name__)


def collect_platform_data_single(platform: str, item: Dict, query_params: Dict) -> Optional[Dict]:
    """
    采集单个KOL的平台详细数据

    Args:
        platform: 平台名称（TikTok/Instagram/YouTube）
        item: 单个KOL的标准化数据
        query_params: 查询参数

    Returns:
        采集到的平台详细数据，失败则返回None
    """
    task_logger.info(f"开始采集平台 {platform} 的KOL详细数据: {item.get('kol_name', '')}")
    
    try:
        # 构建一个只包含当前项的列表，利用现有的_collect_platform_data函数
        single_item_list = [item]
        result = _collect_platform_data(platform, single_item_list, query_params)
        
        # 如果采集成功，返回第一个项
        if result and len(result) > 0:
            return result[0]
        else:
            task_logger.error(f"获取 {item.get('kol_name', '')} 的平台数据失败")
            return None
            
    except Exception as e:
        task_logger.exception(f"采集单条KOL数据时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return None


def store_data_to_db_single(db: Any, item: Dict, query_params: Dict) -> Optional[Dict]:
    """
    将单个KOL数据存储到数据库

    Args:
        db: 数据库会话
        item: 单个KOL的详细数据
        query_params: 查询参数

    Returns:
        存储后的数据，失败则返回None
    """
    task_logger.info(f"开始将KOL数据存储到数据库: {item.get('kol_name', '')}")
    
    try:
        # 构建一个只包含当前项的列表，利用现有的_store_data_to_db函数
        single_item_list = [item]
        result = _store_data_to_db(db, single_item_list, query_params)
        
        # 如果存储成功，返回第一个项
        if result and len(result) > 0:
            return result[0]
        else:
            task_logger.error(f"存储 {item.get('kol_name', '')} 的数据到数据库失败")
            return None
            
    except Exception as e:
        task_logger.exception(f"存储单条KOL数据到数据库时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return None


def store_high_potential_data_single(filter_name: str, project_code: str, item: Dict, 
                                    table_save: str, table_duplicate: str) -> Tuple[bool, Optional[Dict]]:
    """
    将单个高潜力KOL数据存储到指定飞书表

    Args:
        filter_name: 筛选条件名称
        project_code: 项目代码
        item: 单个KOL的详细数据
        table_save: 存储表ID或URL
        table_duplicate: 去重表ID或URL列表，以逗号分隔

    Returns:
        (是否成功存储, 存储的记录)
    """
    task_logger.info(f"开始将高潜力KOL数据存储到飞书表: {item.get('kol_name', '')}")
    
    try:
        # 初始化Lark客户端
        lark_client = Lark()

        # 解析表格URL参数
        save_node_token, save_table_id = _parse_table_url(table_save)
        app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
        if not app_token:
            task_logger.error(f"查询飞书 app_token 失败; \n{table_save}\n{save_node_token}")
            return False, None
        else:
            app_token = app_token.get("obj_token")

        if not save_table_id:
            task_logger.error(f"解析目标存储表URL失败: {table_save}")
            return False, None
            
        # 获取要检查的KOL ID
        kol_id = item.get("kol_name", "")
        if not kol_id:
            task_logger.error("KOL ID为空，无法存储数据")
            return False, None
            
        # 处理查重表格列表
        duplicate_tables = []
        if table_duplicate:
            duplicate_tables = table_duplicate.split(',')

        # 往duplicate_tables中添加目标存储表
        if table_save:
            duplicate_tables.append(table_save)
            
        # 检查是否已存在该KOL ID
        for dup_table in duplicate_tables:
            dup_node_token, dup_table_id = _parse_table_url(dup_table)
            if not dup_table_id:
                task_logger.warning(f"解析查重表URL失败，将跳过此表: {dup_table}")
                continue
                
            # 构建精确查询条件，只查询特定KOL ID
            search_params = {
                "filter": {
                    "conjunction": "and",
                    "conditions": [
                        {
                            "field_name": "KOL ID", 
                            "operator": "is", 
                            "value": [kol_id]
                        }
                    ]
                }
            }
            
            # 精确查询特定KOL ID的记录
            matching_records = lark_client.search_table_record(app_token, dup_table_id, search_params)
            
            # 检查是否找到匹配记录
            if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
                task_logger.info(f"KOL ID '{kol_id}' 已存在于表格 {dup_table}，跳过存储")
                return True, None  # 已存在，返回成功但不创建新记录
        
        # 创建飞书表记录格式
        record = {
            "KOL ID": kol_id,  # 写入飞书表格不能有 TK_ 前缀
            "Email": item.get("email", ""),
            "Account link": {"text": item.get("account_link", ""), "link": item.get("account_link", "")},
            "KOL Name": item.get("username", ""),
            "Bio": item.get("bio", ""),
            "Source": item.get("source", ""),
            "Filter": filter_name,
            "Followers(K)": item.get('followers_k', 0),
            "Mean Views(K)": item.get('mean_views_k', 0),
            "Median Views(K)": item.get('median_views_k', 0),
            "Keywords-AI": ", ".join(str(keyword) for keyword in item.get('keywords_ai', []) if keyword),
            "Level": item.get('level'),
            "Project": project_code,
            "Engagement Rate(%)": f"{item.get('engagement_rate', 0) * 100:.2f} %",
            "Average Views(K)": item.get('average_views_k', 0),
        }
        
        # KOL ID不存在于任何表格中，创建新记录
        records = [record]
        result = lark_client.batch_create_table_records(app_token, save_table_id, records)
        
        if result:
            task_logger.info(f"成功将KOL '{kol_id}' 存储到高潜力飞书表")
            return True, record
        else:
            task_logger.error(f"存储KOL '{kol_id}' 到高潜力飞书表失败")
            return False, None
            
    except Exception as e:
        task_logger.exception(f"存储单条高潜力KOL数据到飞书表时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return False, None


def send_to_feishu_single(item: Dict, query_params: Dict, hp_records: Optional[List[Dict]] = None) -> bool:
    """
    将单个KOL数据发送到飞书邮箱表

    Args:
        item: 单个KOL的详细数据
        query_params: 查询参数
        hp_records: 高潜力记录列表，用于检查KOL是否为高潜力KOL

    Returns:
        是否成功发送
    """
    task_logger.info(f"开始将KOL数据发送到邮箱表: {item.get('kol_name', '')}")
    
    try:
        # 检查是否有邮箱
        if not item.get("email"):
            task_logger.warning(f"KOL没有邮箱，跳过发送: {item.get('kol_name', '')}")
            return False
            
        # 检查是否在高潜力列表中（如果提供了高潜力列表）
        if hp_records:
            is_high_potential = False
            for record in hp_records:
                if record.get("KOL ID") == item.get("kol_name"):
                    is_high_potential = True
                    break
                    
            if not is_high_potential:
                task_logger.warning(f"KOL不在高潜力列表中，跳过发送: {item.get('kol_name', '')}")
                return False
        
        # 初始化Lark客户端
        lark_client = Lark()

        # 获取飞书配置
        save_node_token = settings.FEISHU_EMAIL_APP_TOKEN
        table_id = settings.FEISHU_EMAIL_TABLE_ID

        app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
        if not app_token:
            task_logger.error(f"查询飞书 app_token 失败; \n{table_id}\n{save_node_token}")
            return False
        else:
            app_token = app_token.get("obj_token")

        if not save_node_token or not table_id:
            task_logger.error("缺少必要的飞书配置: FEISHU_EMAIL_APP_TOKEN 或 FEISHU_EMAIL_TABLE_ID")
            return False
            
        # 获取KOL ID
        kol_id = item.get("kol_name", "")
        if not kol_id:
            task_logger.error("KOL ID为空，无法发送数据")
            return False
            
        # 构建精确查询条件，只查询特定KOL ID
        search_params = {
            "filter": {
                "conjunction": "and",
                "conditions": [
                    {
                        "field_name": "KOL ID", 
                        "operator": "is", 
                        "value": [kol_id]
                    }
                ]
            }
        }
        
        # 精确查询特定KOL ID的记录
        matching_records = lark_client.search_table_record(app_token, table_id, search_params)
        
        # 检查是否找到匹配记录
        if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
            task_logger.info(f"KOL ID '{kol_id}' 已存在于邮件表，跳过发送")
            return True  # 已存在，返回成功
        
        # 格式化数据为飞书表格所需格式
        record = {
            "KOL ID": kol_id,
            "Email": item.get("email", ""),
            "Account link": {"text": item.get("account_link", ""), "link": item.get("account_link", "")},
            "KOL Name": item.get("username", ""),
            "Template": query_params.get("template", ""),
            "App Code": query_params.get("project_code", ""),
        }
        
        # 创建新记录
        result = lark_client.batch_create_table_records(app_token, table_id, [record])
        
        if result:
            task_logger.info(f"成功将KOL '{kol_id}' 发送到自动发邮件飞书表")
            return True
        else:
            task_logger.error(f"发送KOL '{kol_id}' 到自动发邮件飞书表失败")
            return False
            
    except Exception as e:
        task_logger.exception(f"发送单条KOL数据到飞书邮箱表时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return False


def search_feishu_data(filter_name: str, project_code: str, item: Dict, 
                       table_save: str, table_duplicate: str) -> Tuple[bool, Optional[Dict]]:
    """
    查询飞书表中是否已存在该KOL数据

    Args:
        filter_name: 筛选条件名称
        project_code: 项目代码
        item: 单个KOL的详细数据
        table_save: 存储表ID或URL
        table_duplicate: 去重表ID或URL列表, 以逗号分隔

    Returns:
        (是否存在, 存在的记录信息)
    """
    task_logger.info(f"查询飞书表中是否存在KOL: {item.get('kol_name', '')}")
    
    try:
        # 初始化Lark客户端
        lark_client = Lark()

        # 获取要检查的KOL ID
        kol_id = item.get("kol_name", "")
        if not kol_id:
            task_logger.error("KOL ID为空，无法查询数据")
            return False, None
            
        # 处理查重表格列表
        duplicate_tables = []
        
        # 先处理主表
        if table_save:
            # 解析主表URL参数
            save_node_token, save_table_id = _parse_table_url(table_save)
            app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
            if not app_token:
                task_logger.error(f"查询飞书 app_token 失败; \n{table_save}\n{save_node_token}")
                return False, None
            else:
                app_token = app_token.get("obj_token")

            if not save_table_id:
                task_logger.error(f"解析目标存储表URL失败: {table_save}")
                return False, None
                
            duplicate_tables.append(table_save)
        
        # 处理其他去重表
        if table_duplicate:
            other_tables = table_duplicate.split(',')
            duplicate_tables.extend(other_tables)
            
        # 检查是否已存在该KOL ID
        for dup_table in duplicate_tables:
            dup_node_token, dup_table_id = _parse_table_url(dup_table)
            if not dup_table_id:
                task_logger.warning(f"解析查重表URL失败，将跳过此表: {dup_table}")
                continue
                
            # 获取该表的app_token
            if dup_table != table_save:
                dup_app_token = lark_client.get_wiki_node_info(node_token=dup_node_token)
                if not dup_app_token:
                    task_logger.warning(f"查询飞书 app_token 失败，跳过此表: {dup_table}")
                    continue
                current_app_token = dup_app_token.get("obj_token")
            else:
                current_app_token = app_token
                
            # 构建精确查询条件，只查询特定KOL ID
            search_params = {
                "filter": {
                    "conjunction": "and",
                    "conditions": [
                        {
                            "field_name": "KOL ID", 
                            "operator": "is", 
                            "value": [kol_id]
                        }
                    ]
                }
            }
            
            # 精确查询特定KOL ID的记录
            matching_records = lark_client.search_table_record(current_app_token, dup_table_id, search_params)
            
            # 检查是否找到匹配记录
            if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
                task_logger.info(f"KOL ID '{kol_id}' 已存在于表格 {dup_table}")
                record_data = matching_records["items"][0].get("fields", {})
                return True, record_data
        
        # 所有表格都没找到匹配记录
        task_logger.info(f"KOL ID '{kol_id}' 未在任何表格中找到")
        return False, None
            
    except Exception as e:
        task_logger.exception(f"查询飞书表数据时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        # 如果查询过程出错，为安全起见返回False，让程序继续处理该数据
        return False, None


@celery_app.task(
    base=BaseTask,
    name="kol.collect_kol_data",
    bind=True,
    max_retries=2,
    default_retry_delay=300,  # 5分钟后重试
    soft_time_limit=86400,    # 24小时软时间限制
    # 使用priority队列配置
    queue="kol_data",
)
def collect_kol_data_task(self, query_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    异步执行KOL数据采集任务 (流式处理版本)
    
    Args:
        query_params: 查询参数，包含所需的数据源、平台等信息
            - source: 数据源（如 "creable"）
            - platform: 平台（如 "Tiktok"）
            - filter_name: 筛选器名称
            - filter_body: 筛选条件正文
            - cookie: 认证cookie
            - project_code: 项目代码
            - auto_send: 是否自动发送（"yes"/"no"）
            - template: 模板名称
            - high_potential: 是否为高潜力KOL（"yes"/"no"）
            - table_save: 保存表格的URL
            - table_duplicate: 重复表格的URL
            
    Returns:
        Dict: 包含任务结果的字典
    """
    start_time = time.time()
    task_id = self.request.id
    filter_name = query_params.get("filter_name", "未命名任务")
    
    try:
        task_logger.info(f"任务 [{task_id}] 开始采集平台 {query_params.get('platform')} 的KOL数据（流式处理版本）| 筛选名称: {filter_name}")
        
        # 参数校验
        source = query_params.get("source", "").lower()
        platform = query_params.get("platform", "").lower()
        filter_name = query_params.get("filter_name", "")
        project_code = query_params.get("project_code", "")
        auto_send = query_params.get("auto_send", "no")
        high_potential = query_params.get("high_potential", "no")
        table_save = query_params.get("table_save", "")
        table_duplicate = query_params.get("table_duplicate", "")

        # 参数验证
        _validate_params(source, platform)
        
        # 发送开始通知
        send_webhook(filter_name=filter_name, status="进行中: 0%", task_id=task_id)

        # 创建数据库会话
        db = SessionLocal()
        
        # 初始化Lark客户端，在循环外创建一个实例，避免重复创建
        lark_client = Lark()
        
        # 在循环外预先获取所需的app_token
        app_tokens = {}
        duplicate_tables_info = {}
        
        # 预先处理高潜力KOL存储相关的表格和token
        if high_potential.lower() == "yes" and table_save:
            # 解析主表URL参数
            save_node_token, save_table_id = _parse_table_url(table_save)
            if save_node_token and save_table_id:
                app_token_info = lark_client.get_wiki_node_info(node_token=save_node_token)
                if app_token_info:
                    app_tokens["save_table"] = app_token_info.get("obj_token")
                    duplicate_tables_info["save"] = {
                        "node_token": save_node_token,
                        "table_id": save_table_id,
                        "app_token": app_tokens["save_table"]
                    }
                else:
                    task_logger.error(f"查询飞书 app_token 失败; \n{table_save}\n{save_node_token}")
        
        # 处理查重表格列表
        if high_potential.lower() == "yes" and table_duplicate:
            duplicate_tables = table_duplicate.split(',')
            for idx, dup_table in enumerate(duplicate_tables):
                dup_node_token, dup_table_id = _parse_table_url(dup_table)
                if dup_node_token and dup_table_id:
                    if f"duplicate_{idx}" not in app_tokens:
                        dup_app_token_info = lark_client.get_wiki_node_info(node_token=dup_node_token)
                        if dup_app_token_info:
                            app_tokens[f"duplicate_{idx}"] = dup_app_token_info.get("obj_token")
                            duplicate_tables_info[f"duplicate_{idx}"] = {
                                "node_token": dup_node_token,
                                "table_id": dup_table_id,
                                "app_token": app_tokens[f"duplicate_{idx}"],
                                "url": dup_table
                            }
                        else:
                            task_logger.warning(f"查询飞书 app_token 失败，可能会影响去重检查: {dup_table}")
                else:
                    task_logger.warning(f"解析查重表URL失败，将跳过此表: {dup_table}")
        
        # 预先获取自动发送邮件相关的表格和token
        if auto_send.lower() == "yes":
            email_save_node_token = settings.FEISHU_EMAIL_APP_TOKEN
            email_table_id = settings.FEISHU_EMAIL_TABLE_ID
            if email_save_node_token and email_table_id:
                email_app_token_info = lark_client.get_wiki_node_info(node_token=email_save_node_token)
                if email_app_token_info:
                    app_tokens["email_table"] = email_app_token_info.get("obj_token")
                else:
                    task_logger.error("查询飞书邮件表 app_token 失败")

        try:
            # 1. 采集原始数据 (批量)
            task_logger.info(f"任务 [{task_id}] [1/4] 从数据源 {source} 采集原始数据")
            raw_data = _collect_source_data(source, query_params)
            if not raw_data:
                error_msg = f"从数据源 {source} 获取数据失败"
                task_logger.error(f"任务 [{task_id}] {error_msg}")
                send_webhook(filter_name=filter_name, status="失败", task_id=task_id)
                return {"status": "error", "message": error_msg, "task_id": task_id}
            
            # 更新进度
            self.update_state(state="PROGRESS", meta={"current": 10, "total": 100})
            send_webhook(filter_name=filter_name, status=f"进行中: 10%", task_id=task_id)

            # 2. 数据标准化处理 (批量)
            task_logger.info(f"任务 [{task_id}] [2/4] 数据标准化处理")
            standardized_data = _standardize_data(raw_data, source, platform)
            if not standardized_data:
                error_msg = "数据标准化处理失败"
                task_logger.error(f"任务 [{task_id}] {error_msg}")
                send_webhook(filter_name=filter_name, status="失败", task_id=task_id)
                return {"status": "error", "message": error_msg, "task_id": task_id}

            total_count = len(standardized_data)
            task_logger.info(f"任务 [{task_id}] 标准化后数据大小: {total_count}条")
            
            # 更新进度
            self.update_state(state="PROGRESS", meta={"current": 20, "total": 100})
            send_webhook(filter_name=filter_name, status=f"进行中: 20%", task_id=task_id)

            # 3. 逐条处理每一条KOL数据 (流式)
            task_logger.info(f"任务 [{task_id}] [3/4] 开始流式处理每一条KOL数据")
            
            # 统计数据
            processed_count = 0
            success_count = 0
            failed_count = 0
            high_potential_records = []
            email_count = 0
            skipped_count = 0  # 新增跳过计数
            
            # 记录上次更新状态的时间
            last_update_time = time.time()
            # 状态更新的最小间隔时间(秒)
            update_interval = 5
            
            # 流式处理每条数据
            for item in standardized_data:
                try:
                    # 检查任务是否被取消
                    if self.is_aborted():
                        task_logger.warning(f"任务 [{task_id}] 已被取消，停止处理")
                        return {
                            "status": "cancelled",
                            "message": "任务已被取消",
                            "data_count": total_count,
                            "processed_count": processed_count,
                            "success_count": success_count,
                            "failed_count": failed_count,
                            "skipped_count": skipped_count,
                            "task_id": task_id
                        }
                    
                    # 3.0 查询飞书表中是否存在数据
                    kol_name = item.get('kol_name', '')
                    if not settings.UPDATE:
                        # 如果不需要更新，先查询数据是否存在
                        exists = False
                        # 使用预先获取的token检查是否存在
                        if duplicate_tables_info:
                            # 获取要检查的KOL ID
                            kol_id = item.get("kol_name", "")
                            if kol_id:
                                # 检查是否已存在该KOL ID
                                for table_key, table_info in duplicate_tables_info.items():
                                    current_app_token = table_info.get("app_token")
                                    current_table_id = table_info.get("table_id")
                                    
                                    if not current_app_token or not current_table_id:
                                        continue
                                    
                                    # 构建精确查询条件，只查询特定KOL ID
                                    search_params = {
                                        "filter": {
                                            "conjunction": "and",
                                            "conditions": [
                                                {
                                                    "field_name": "KOL ID", 
                                                    "operator": "is", 
                                                    "value": [kol_id]
                                                }
                                            ]
                                        }
                                    }
                                    
                                    # 精确查询特定KOL ID的记录
                                    matching_records = lark_client.search_table_record(current_app_token, current_table_id, search_params)
                                    
                                    # 检查是否找到匹配记录
                                    if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
                                        task_logger.info(f"KOL ID '{kol_id}' 已存在于表格 {table_info.get('url', '')}")
                                        exists = True
                                        break
                        
                        if exists:
                            task_logger.info(f"任务 [{task_id}] KOL '{kol_name}' 已存在且不需要更新，跳过处理")
                            processed_count += 1  # 只在这里增加计数，不要在finally块中重复增加
                            skipped_count += 1
                            
                            # 更新进度 - 按时间间隔更新状态
                            current_time = time.time()
                            if current_time - last_update_time >= update_interval or processed_count == total_count:
                                progress = min(90, 20 + int((processed_count / total_count) * 70))
                                self.update_state(
                                    state="PROGRESS", 
                                    meta={
                                        "current": progress, 
                                        "total": 100,
                                        "filter_name": filter_name,
                                        "processed": processed_count,
                                        "total_count": total_count,
                                        "last_heartbeat": current_time
                                    }
                                )
                                send_webhook(filter_name=filter_name, status=f"进行中: {progress}%", task_id=task_id)
                                last_update_time = current_time
                                task_logger.info(f"任务 [{task_id}] 当前总进度为:{progress}%，已处理: {processed_count}/{total_count}条")
                            
                            continue

                    # 3.1 采集单个KOL的平台详细数据
                    task_logger.info(f"任务 [{task_id}] 正在处理KOL: {item.get('kol_name', '')}")
                    item_with_detail = collect_platform_data_single(platform, item, query_params)
                    
                    # 再次检查任务是否被取消
                    if self.is_aborted():
                        task_logger.warning(f"任务 [{task_id}] 已被取消，停止处理")
                        return {"status": "cancelled", "message": "任务已被取消", "task_id": task_id}
                    
                    if item_with_detail:
                        # 3.2 将单个KOL数据存储到数据库
                        stored_item = store_data_to_db_single(db, item_with_detail, query_params)
                        
                        if stored_item:
                            success_count += 1
                            
                            # 3.3 处理高潜力KOL (如果需要)
                            if high_potential.lower() == "yes" and duplicate_tables_info.get("save"):
                                save_info = duplicate_tables_info.get("save")
                                kol_id = stored_item.get("kol_name", "")
                                
                                if kol_id and save_info and save_info.get("app_token") and save_info.get("table_id"):
                                    # 创建飞书表记录格式
                                    record = {
                                        "KOL ID": kol_id,  # 写入飞书表格不能有 TK_ 前缀
                                        "Email": stored_item.get("email", ""),
                                        "Account link": {"text": stored_item.get("account_link", ""), "link": stored_item.get("account_link", "")},
                                        "KOL Name": stored_item.get("username", ""),
                                        "Bio": stored_item.get("bio", ""),
                                        "Source": stored_item.get("source", ""),
                                        "Filter": filter_name,
                                        "Followers(K)": stored_item.get('followers_k', 0),
                                        "Mean Views(K)": stored_item.get('mean_views_k', 0),
                                        "Median Views(K)": stored_item.get('median_views_k', 0),
                                        "Keywords-AI": ", ".join(str(keyword) for keyword in stored_item.get('keywords_ai', []) if keyword),
                                        "Level": stored_item.get('level'),
                                        "Project": project_code,
                                        "Engagement Rate(%)": f"{stored_item.get('engagement_rate', 0) * 100:.2f} %",
                                        "Average Views(K)": stored_item.get('average_views_k', 0),
                                    }
                                    
                                    # 判断是否需要创建记录
                                    should_create_record = True
                                    
                                    # KOL ID不存在于任何表格中，创建新记录
                                    if should_create_record:
                                        records = [record]
                                        result = lark_client.batch_create_table_records(save_info.get("app_token"), save_info.get("table_id"), records)
                                        
                                        if result:
                                            task_logger.info(f"成功将KOL '{kol_id}' 存储到高潜力飞书表")
                                            high_potential_records.append(record)
                                        else:
                                            task_logger.error(f"存储KOL '{kol_id}' 到高潜力飞书表失败")
                            
                            # 3.4 自动发送邮件处理 (如果需要)
                            if auto_send.lower() == "yes" and stored_item.get("email") and app_tokens.get("email_table"):
                                # 检查是否在高潜力列表中
                                is_high_potential = True
                                if high_potential.lower() == "yes" and high_potential_records:
                                    is_high_potential = False
                                    for record in high_potential_records:
                                        if record.get("KOL ID") == stored_item.get("kol_name"):
                                            is_high_potential = True
                                            break
                                            
                                if is_high_potential:
                                    kol_id = stored_item.get("kol_name", "")
                                    
                                    # 判断是否已存在于邮件表
                                    should_create_email_record = True
                                    if kol_id:
                                        search_params = {
                                            "filter": {
                                                "conjunction": "and",
                                                "conditions": [
                                                    {
                                                        "field_name": "KOL ID", 
                                                        "operator": "is", 
                                                        "value": [kol_id]
                                                    }
                                                ]
                                            }
                                        }
                                        
                                        # 精确查询特定KOL ID的记录
                                        matching_records = lark_client.search_table_record(
                                            app_tokens.get("email_table"), 
                                            settings.FEISHU_EMAIL_TABLE_ID, 
                                            search_params
                                        )
                                        
                                        # 检查是否找到匹配记录
                                        if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
                                            task_logger.info(f"KOL ID '{kol_id}' 已存在于邮件表，跳过发送")
                                            should_create_email_record = False
                                    
                                    if should_create_email_record:
                                        # 格式化数据为飞书表格所需格式
                                        record = {
                                            "KOL ID": kol_id,
                                            "Email": stored_item.get("email", ""),
                                            "Account link": {"text": stored_item.get("account_link", ""), "link": stored_item.get("account_link", "")},
                                            "KOL Name": stored_item.get("username", ""),
                                            "Template": query_params.get("template", ""),
                                            "App Code": query_params.get("project_code", ""),
                                        }
                                        
                                        # 创建新记录
                                        result = lark_client.batch_create_table_records(
                                            app_tokens.get("email_table"), 
                                            settings.FEISHU_EMAIL_TABLE_ID, 
                                            [record]
                                        )
                                        
                                        if result:
                                            task_logger.info(f"成功将KOL '{kol_id}' 发送到自动发邮件飞书表")
                                            email_count += 1
                                        else:
                                            task_logger.error(f"发送KOL '{kol_id}' 到自动发邮件飞书表失败")
                        else:
                            failed_count += 1
                    else:
                        failed_count += 1
                    
                    # 成功处理完这条数据，增加处理计数
                    processed_count += 1
                
                except Exception as e:
                    task_logger.exception(f"任务 [{task_id}] 处理单条KOL数据时发生错误: {str(e)}")
                    failed_count += 1
                    # 处理失败也计入已处理数量
                    processed_count += 1
                
                finally:
                    # 确保进度不会超过90%，且处理数量不超过总数量
                    processed_count = min(processed_count, total_count)  # 确保处理数不超过总数
                    
                    # 基于时间间隔更新状态，而不是每处理固定数量的数据
                    current_time = time.time()
                    if current_time - last_update_time >= update_interval or processed_count == total_count:
                        progress = min(90, 20 + int((processed_count / total_count) * 70))
                        self.update_state(
                            state="PROGRESS", 
                            meta={
                                "current": progress, 
                                "total": 100,
                                "filter_name": filter_name,
                                "processed": processed_count,
                                "total_count": total_count,
                                "last_heartbeat": current_time
                            }
                        )
                        send_webhook(filter_name=filter_name, status=f"进行中: {progress}%", task_id=task_id)
                        last_update_time = current_time
                        task_logger.info(f"任务 [{task_id}] 当前总进度为:{progress}%，已处理: {processed_count}/{total_count}条")
            
            # 4. 完成处理
            execution_time = time.time() - start_time
            task_logger.info(
                f"任务 [{task_id}] [4/4] 完成平台 {platform} 的KOL数据采集"
                f" | 总数据: {total_count}, 成功: {success_count}, 失败: {failed_count}, 跳过: {skipped_count}"
                f" | 耗时: {execution_time:.2f}秒"
            )
            
            # 更新进度并发送完成通知（确保进度为100%）
            self.update_state(state="SUCCESS", meta={"current": 100, "total": 100})
            send_webhook(
                filter_name=filter_name, 
                status="已完成",
                msg=f"总采集数:{total_count}条，成功:{success_count}条，失败:{failed_count}条，跳过:{skipped_count}条", 
                unique_count=len(high_potential_records), 
                email_count=email_count,
                task_id=task_id
            )

            # 确保其他资源被释放
            try:
                # 尝试主动清理Lark客户端等占用资源
                lark_client = None
                # 触发垃圾回收
                import gc
                gc.collect()
            except:
                pass

            # 返回结果
            result_data = {
                "status": "success",
                "message": f"成功采集数据 {success_count} 条，失败 {failed_count} 条，跳过 {skipped_count} 条",
                "data_count": total_count,
                "success_count": success_count,
                "failed_count": failed_count,
                "skipped_count": skipped_count,
                "unique_count": len(high_potential_records),
                "email_count": email_count,
                "task_id": task_id,
                "execution_time": f"{execution_time:.2f}秒",
                "filter_name": filter_name,  # 确保包含filter_name
                "completed_at": time.time(),  # 添加完成时间戳
            }
            
            # 确保状态清晰标记为SUCCESS
            self.update_state(state=states.SUCCESS, meta=result_data)
            
            return result_data

        except Exception as e:
            # 记录错误并重试
            task_logger.exception(f"任务 [{task_id}] 采集KOL数据时发生错误: {str(e)}")
            send_webhook(filter_name=filter_name, status="失败", msg=str(e), task_id=task_id)
            
            # 检查重试次数
            retries = self.request.retries
            max_retries = self.max_retries
            
            if retries < max_retries:
                task_logger.info(f"任务 [{task_id}] 将在 {self.default_retry_delay} 秒后重试 ({retries+1}/{max_retries})")
                raise self.retry(exc=e)
            
            return {
                "status": "error", 
                "message": str(e), 
                "task_id": task_id,
                "attempted_retries": retries
            }

        finally:
            # 确保关闭数据库会话
            db.close()

    except Exception as e:
        # 处理任务执行前的错误
        execution_time = time.time() - start_time
        task_logger.exception(f"任务 [{task_id}] 执行任务前发生错误: {str(e)}")
        return {
            "status": "error", 
            "message": f"任务初始化失败: {str(e)}",
            "task_id": task_id,
            "execution_time": f"{execution_time:.2f}秒"
        }


@shared_task(
    name="kol.check_status",
    queue="kol_data",
    soft_time_limit=60,  # 60秒
)
def check_kol_task_status(task_id: str) -> Dict[str, Any]:
    """
    检查KOL数据采集任务的状态
    
    Args:
        task_id: 要检查的任务ID
        
    Returns:
        任务状态信息
    """
    result = collect_kol_data_task.AsyncResult(task_id)
    
    # 获取任务信息以检查是否为僵尸任务
    inspector = celery_app.control.inspect()
    active_tasks = inspector.active() or {}
    
    # 准备状态信息
    status_info = {
        "task_id": task_id,
        "status": result.status,
        "is_zombie": False,  # 默认非僵尸任务
    }
    
    # 获取任务开始运行时间
    task_start_time = None
    for worker, tasks in active_tasks.items():
        for task in tasks:
            if task.get('id') == task_id:
                task_start_time = task.get('time_start', 0)
                break
        if task_start_time:
            break
    
    # 如果任务已完成但仍在活动列表中，可能是僵尸任务
    is_active = False
    for worker, tasks in active_tasks.items():
        if any(task.get('id') == task_id for task in tasks):
            is_active = True
            break
    
    # 如果任务已完成但仍在活动列表中，标记为僵尸任务
    if is_active and result.successful():
        status_info["is_zombie"] = True
        # 如果确认是僵尸任务，尝试终止它
        try:
            celery_app.control.revoke(task_id, terminate=True)
            status_info["action"] = "已终止僵尸任务"
        except Exception as e:
            status_info["action_error"] = str(e)
    
    # 如果任务处于进行中且有进度信息
    if result.status == "PROGRESS" and result.info:
        current = min(100, result.info.get('current', 0))  # 确保不超过100%
        total = result.info.get('total', 100)
        status_info["progress"] = f"{current}/{total}"
        status_info["percent"] = current
        status_info["filter_name"] = result.info.get('filter_name', '')
        
        # 添加心跳检测
        last_heartbeat = result.info.get('last_heartbeat', 0)
        if last_heartbeat:
            time_since_heartbeat = time.time() - last_heartbeat
            status_info["time_since_heartbeat"] = f"{time_since_heartbeat:.2f}秒"
            # 如果超过10分钟没有心跳，可能任务已经卡住
            if time_since_heartbeat > 600:
                status_info["warning"] = "任务可能已卡住，超过10分钟无心跳"
    
    # 如果任务成功
    if result.successful():
        result_data = result.get() if isinstance(result.get(), dict) else {}
        status_info["result"] = result_data
        status_info["filter_name"] = result_data.get('filter_name', '')
    
    # 如果任务失败
    if result.failed():
        status_info["error"] = str(result.info)
    
    return status_info


@shared_task(
    name="kol.generate_report",
    queue="kol_data",
    soft_time_limit=1800,  # 30分钟
)
def generate_kol_report(report_type: str = "weekly") -> Dict[str, Any]:
    """
    生成KOL数据采集任务的统计报告
    
    Args:
        report_type: 报告类型，可以是 "daily", "weekly", "monthly"
        
    Returns:
        包含报告结果的字典
    """
    try:
        task_logger.info(f"开始生成 {report_type} KOL数据采集报告")
        start_time = time.time()
        
        # 获取所有任务
        inspector = celery_app.control.inspect()
        active_tasks = inspector.active() or {}
        reserved_tasks = inspector.reserved() or {}
        
        # 统计信息
        stats = {
            "total_completed": 0,
            "total_failed": 0,
            "total_active": sum(len(tasks) for worker, tasks in active_tasks.items()),
            "total_reserved": sum(len(tasks) for worker, tasks in reserved_tasks.items()),
            "by_platform": {},
            "by_source": {},
            "average_execution_time": 0,
            "total_records_collected": 0,
        }
        
        # 这里需要实现实际的统计逻辑
        # 例如，从数据库或Redis中查询最近的任务结果
        # 并统计成功/失败数量、平均执行时间、采集记录数等
        
        # 模拟统计数据
        stats["total_completed"] = 120
        stats["total_failed"] = 5
        stats["by_platform"] = {
            "tiktok": 60,
            "instagram": 35,
            "youtube": 30,
        }
        stats["by_source"] = {
            "creable": 70,
            "modash": 55,
        }
        stats["average_execution_time"] = 1250  # 秒
        stats["total_records_collected"] = 45600
        
        # 发送报告到管理员邮箱或消息系统
        # 这里应该实现实际的通知逻辑
        
        execution_time = time.time() - start_time
        task_logger.info(f"KOL {report_type} 报告生成完毕，耗时 {execution_time:.2f}秒")
        
        return {
            "status": "success",
            "report_type": report_type,
            "stats": stats,
            "generation_time": f"{execution_time:.2f}秒",
        }
        
    except Exception as e:
        task_logger.exception(f"生成KOL报告失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
        }


@shared_task(
    name="kol.clean_expired_results",
    queue="kol_data",
    soft_time_limit=1800,  # 30分钟
)
def clean_expired_kol_results(expire_days: int = 30) -> Dict[str, Any]:
    """
    清理过期的KOL任务结果
    
    Args:
        expire_days: 过期天数，超过此天数的任务结果将被清理
        
    Returns:
        清理结果
    """
    try:
        task_logger.info(f"开始清理 {expire_days} 天前的KOL任务结果")
        start_time = time.time()
        
        # 计算过期时间点
        expire_timestamp = time.time() - (expire_days * 86400)  # 86400秒 = 1天
        
        # 需要实现实际的清理逻辑
        # 例如，从Redis中删除过期的任务结果，或从数据库中删除过期记录
        
        # 模拟清理结果
        cleaned_count = 287
        
        execution_time = time.time() - start_time
        task_logger.info(f"清理完成，共清理 {cleaned_count} 条过期任务记录，耗时 {execution_time:.2f}秒")
        
        return {
            "status": "success",
            "cleaned_count": cleaned_count,
            "expire_days": expire_days,
            "execution_time": f"{execution_time:.2f}秒",
        }
        
    except Exception as e:
        task_logger.exception(f"清理过期KOL任务结果失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
        }


@shared_task(
    name="kol.cleanup_zombie_tasks",
    queue="maintenance",
    soft_time_limit=600,  # 10分钟
)
def cleanup_zombie_tasks() -> Dict[str, Any]:
    """
    清理僵尸任务
    
    检测并终止那些显示为活跃但实际已完成的任务
    
    Returns:
        清理结果信息
    """
    try:
        task_logger.info("开始检测并清理僵尸任务")
        start_time = time.time()
        
        # 获取所有任务
        inspector = celery_app.control.inspect()
        active_tasks = inspector.active() or {}
        
        cleaned_count = 0
        zombie_tasks = []
        
        # 检查活动任务
        for worker, tasks in active_tasks.items():
            for task in tasks:
                task_id = task.get('id')
                if not task_id:
                    continue
                    
                # 跳过当前清理任务自身
                if task_id == cleanup_zombie_tasks.request.id:
                    continue
                    
                # 获取任务开始时间和运行时间
                started = task.get('time_start', 0)
                if isinstance(started, str):
                    try:
                        # 如果是字符串，尝试转换为timestamp
                        import dateutil.parser
                        started = dateutil.parser.parse(started).timestamp()
                    except:
                        # 无法解析时间，使用当前时间减去1小时作为估计
                        started = time.time() - 3600
                
                run_time = time.time() - started
                
                # 检查任务状态
                result = AsyncResult(task_id)
                actual_state = result.state
                
                # 如果任务已成功或失败，但仍显示为活动，则认为是僵尸任务
                if actual_state in [states.SUCCESS, states.FAILURE] or run_time > 86400:  # 24小时
                    task_logger.warning(f"发现僵尸任务: {task_id}, 状态: {actual_state}, 运行时间: {run_time:.2f}秒")
                    zombie_tasks.append({
                        "task_id": task_id,
                        "state": actual_state,
                        "run_time": f"{run_time:.2f}秒",
                        "worker": worker
                    })
                    
                    # 终止任务
                    celery_app.control.revoke(task_id, terminate=True)
                    cleaned_count += 1
        
        execution_time = time.time() - start_time
        task_logger.info(f"僵尸任务清理完成，共清理 {cleaned_count} 个任务，耗时 {execution_time:.2f}秒")
        
        return {
            "status": "success",
            "cleaned_count": cleaned_count,
            "zombie_tasks": zombie_tasks,
            "execution_time": f"{execution_time:.2f}秒",
        }
        
    except Exception as e:
        task_logger.exception(f"清理僵尸任务时发生错误: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
        } 