"""
示例任务模块
展示不同类型任务的实现方式
"""
import time
import random
from celery import shared_task
from celery.utils.log import get_task_logger
from app.worker.celery_app import BaseTask, celery_app

# 任务专用日志记录器
task_logger = get_task_logger(__name__)


@shared_task(name="example.add")
def add(x: int, y: int) -> int:
    """
    简单任务示例: 两个数字相加
    使用shared_task装饰器，简单任务的推荐方式
    """
    task_logger.info(f"执行加法任务: {x} + {y}")
    return x + y


@shared_task(
    bind=True,  # 将任务实例作为第一个参数(self)传入
    name="example.long_running_task",
    max_retries=3,  # 最大重试次数
    default_retry_delay=60,  # 默认重试延迟 (秒)
)
def long_running_task(self, task_time: int = 10) -> dict:
    """
    长时间运行任务示例
    带有重试机制
    """
    try:
        task_logger.info(f"开始执行长时间任务，预计耗时 {task_time} 秒")
        
        # 模拟长时间操作
        time.sleep(task_time)
        
        # 模拟可能的随机错误
        if random.random() < 0.3:  # 30%概率失败
            raise Exception("随机任务错误模拟")
            
        task_logger.info("长时间任务执行完成")
        return {
            "status": "success",
            "task_id": self.request.id,
            "execution_time": task_time,
        }
        
    except Exception as e:
        task_logger.error(f"长时间任务执行失败: {str(e)}")
        # 重试任务
        raise self.retry(exc=e, countdown=5)  # 5秒后重试


# 使用自定义的基础任务类
@celery_app.task(
    base=BaseTask, 
    name="example.db_operation_task",
    bind=True,
    autoretry_for=(Exception,),  # 自动重试的异常类型
    retry_kwargs={"max_retries": 3, "countdown": 5},  # 重试参数
)
def db_operation_task(self, item_id: int) -> dict:
    """
    数据库操作任务示例
    使用BaseTask基类提供额外功能
    
    注意: 这是一个模拟实现，实际项目中需要添加真实的数据库会话处理
    """
    task_logger.info(f"开始执行数据库操作任务，item_id: {item_id}")
    
    try:
        # 模拟数据库操作
        time.sleep(2)
        
        # 这里应该是实际的数据库查询和更新逻辑
        # 如: db.query(Item).filter(Item.id == item_id).update(...)
        # db.commit()
        
        task_logger.info(f"数据库操作完成: item_id={item_id}")
        return {
            "status": "success", 
            "item_id": item_id,
            "updated_at": time.time(),
        }
        
    except Exception as e:
        task_logger.error(f"数据库操作失败: {str(e)}")
        # 自定义错误处理逻辑，autoretry_for会自动处理重试
        raise


# 定义一个周期性任务，实际周期设置在Celery Beat配置中
@shared_task(name="example.periodic_task")
def periodic_task() -> dict:
    """
    周期性任务示例
    可通过Celery Beat定时执行
    """
    task_logger.info("执行周期性任务")
    
    # 模拟一些周期性清理或检查操作
    time.sleep(1)
    
    return {
        "status": "success",
        "executed_at": time.time(),
        "message": "周期性任务执行成功",
    } 