from fastapi import APIRouter

from app.api.v1.endpoints import filter_data, filter_kol_association, kol_info, send_data, tasks, video_info, candidate_data, collaboration_performance, tag, kol_tag_association, project_tag_association

api_router = APIRouter()
api_router.include_router(filter_data.router, prefix="/filter-data", tags=["filter_data 管理"])
api_router.include_router(kol_info.router, prefix="/kol", tags=["kol_info 管理"])
api_router.include_router(video_info.router, prefix="/videos", tags=["video_info 管理"])
api_router.include_router(send_data.router, prefix="/send-data", tags=["send_data 管理"])
api_router.include_router(filter_kol_association.router, prefix="/filter-kol-associations", tags=["filter_name 和 kol_info 关联"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks 管理"])
api_router.include_router(candidate_data.router, prefix="/candidate-data", tags=["candidate_data 管理"])
api_router.include_router(collaboration_performance.router, prefix="/collaboration-performance", tags=["collaboration_performance 管理"])
api_router.include_router(tag.router, prefix="/tags", tags=["tag 管理"])
api_router.include_router(kol_tag_association.router, prefix="/kol-tag-associations", tags=["kol_info 和 tag 关联"])
api_router.include_router(project_tag_association.router, prefix="/project-tag-associations", tags=["project 和 tag 关联"])
