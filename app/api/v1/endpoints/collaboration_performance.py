from typing import Any, List, Optional
from datetime import datetime
import math
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app import crud, schemas
from app.db.session import get_async_db, get_db

router = APIRouter()


@router.get("/", response_model=schemas.CollaborationPerformancePaginatedResponse)
def read_performances(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
    kol_id: Optional[str] = Query(None, description="KOL ID筛选"),
    project: Optional[str] = Query(None, description="项目名称筛选"),
) -> Any:
    """
    获取多个合作表现数据（分页）
    """
    skip = (page - 1) * size
    
    # 获取总数
    if kol_id and project:
        total = crud.collaboration_performance.count_by_kol_id_and_project(
            db, kol_id=kol_id, project=project
        )
        performances = crud.collaboration_performance.get_by_kol_id_and_project(
            db, kol_id=kol_id, project=project, skip=skip, limit=size
        )
    elif kol_id:
        total = crud.collaboration_performance.count_by_kol_id(
            db, kol_id=kol_id
        )
        performances = crud.collaboration_performance.get_by_kol_id(
            db, kol_id=kol_id, skip=skip, limit=size
        )
    elif project:
        total = crud.collaboration_performance.count_by_project(
            db, project=project
        )
        performances = crud.collaboration_performance.get_by_project(
            db, project=project, skip=skip, limit=size
        )
    else:
        total = crud.collaboration_performance.count(db)
        performances = crud.collaboration_performance.get_multi(db, skip=skip, limit=size)
    
    # 计算总页数
    pages = math.ceil(total / size) if total > 0 else 1
    
    return schemas.CollaborationPerformancePaginatedResponse(
        items=performances,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.post("/", response_model=schemas.CollaborationPerformanceResponse)
def create_performance(
    *,
    db: Session = Depends(get_db),
    performance_in: schemas.CollaborationPerformanceCreate,
) -> Any:
    """创建新的合作表现数据
    
    前端必要字段：
    - KOL ID
    - Project（下拉菜单）
    - Paypal Accounts（文字输入框）
    - Collab Status（下拉菜单）
    - Follow Up（下拉菜单）
    - Post Link（文字输入框）
    - Payment（默认$，保留两位小数，只需要填入数字）
    - Payout Date（日期格式）
    - Fund Source（下拉菜单）
    """
    # 验证KOL是否存在
    kol = crud.kol_info.get(db, id=performance_in.kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在"
        )
    
    # 处理Payment字段，添加$符号和保留两位小数
    if performance_in.payment and not performance_in.payment.startswith('$'):
        try:
            # 尝试将输入转换为浮点数
            payment_value = float(performance_in.payment)
            # 格式化为带$符号的字符串，保留两位小数
            performance_in.payment = f"{payment_value:.2f}"
        except ValueError:
            # 如果无法转换为浮点数，报错
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payment必须是有效的数字"
            )
    
    performance = crud.collaboration_performance.create(db, obj_in=performance_in)
    return performance


@router.get("/{id}", response_model=schemas.CollaborationPerformanceResponse)
def read_performance(
    *,
    db: Session = Depends(get_db),
    id: int,
) -> Any:
    """
    通过ID获取合作表现数据
    """
    performance = crud.collaboration_performance.get(db, id=id)
    if not performance:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="合作表现数据不存在"
        )
    return performance


@router.put("/{id}", response_model=schemas.CollaborationPerformanceResponse)
def update_performance(
    *,
    db: Session = Depends(get_db),
    id: int,
    performance_in: schemas.CollaborationPerformanceUpdate,
) -> Any:
    """
    更新合作表现数据
    """
    performance = crud.collaboration_performance.get(db, id=id)
    if not performance:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="合作表现数据不存在"
        )
    
    # 处理Payment字段，添加$符号和保留两位小数
    if performance_in.payment is not None and not (isinstance(performance_in.payment, str) and performance_in.payment.startswith('$')):
        try:
            # 尝试将输入转换为浮点数
            payment_value = float(performance_in.payment)
            # 格式化为带$符号的字符串，保留两位小数
            performance_in.payment = f"{payment_value:.2f}"
        except ValueError:
            # 如果无法转换为浮点数，报错
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payment必须是有效的数字"
            )
    
    performance = crud.collaboration_performance.update(db, db_obj=performance, obj_in=performance_in)
    return performance


@router.patch("/kol/{kol_id}", response_model=List[schemas.CollaborationPerformanceResponse])
def update_performance_by_kol_id(
    *,
    db: Session = Depends(get_db),
    kol_id: str,
    performance_in: schemas.CollaborationPerformanceUpdate,
) -> Any:
    """
    根据KOL ID更新所有相关的合作表现数据
    注意：未传入的字段不会被更新
    """
    # 验证KOL是否存在
    kol = crud.kol_info.get(db, id=kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在"
        )
    
    # 检查是否有该KOL的合作表现数据
    existing_performances = crud.collaboration_performance.get_by_kol_id(db, kol_id=kol_id, limit=1)
    if not existing_performances:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到KOL ID为 {kol_id} 的合作表现数据"
        )
    
    # 处理Payment字段，添加$符号和保留两位小数
    if performance_in.payment is not None and not (isinstance(performance_in.payment, str) and performance_in.payment.startswith('$')):
        try:
            # 尝试将输入转换为浮点数
            payment_value = float(performance_in.payment)
            # 格式化为带$符号的字符串，保留两位小数
            performance_in.payment = f"{payment_value:.2f}"
        except ValueError:
            # 如果无法转换为浮点数，报错
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payment必须是有效的数字"
            )
    
    # 更新所有匹配的合作表现数据
    updated_performances = crud.collaboration_performance.update_by_kol_id(
        db, kol_id=kol_id, obj_in=performance_in
    )
    
    return updated_performances


@router.delete("/{id}", response_model=schemas.CollaborationPerformanceResponse)
def delete_performance(
    *,
    db: Session = Depends(get_db),
    id: int,
) -> Any:
    """
    删除合作表现数据
    """
    performance = crud.collaboration_performance.get(db, id=id)
    if not performance:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="合作表现数据不存在"
        )
    
    performance = crud.collaboration_performance.remove(db, id=id)
    return performance


@router.get("/by-kol/{kol_id}", response_model=schemas.CollaborationPerformancePaginatedResponse)
def get_performances_by_kol(
    *,
    db: Session = Depends(get_db),
    kol_id: str,
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
) -> Any:
    """
    获取特定KOL的所有合作表现数据（分页）
    """
    skip = (page - 1) * size
    
    total = crud.collaboration_performance.count_by_kol_id(db, kol_id=kol_id)
    performances = crud.collaboration_performance.get_by_kol_id(
        db, kol_id=kol_id, skip=skip, limit=size
    )
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return schemas.CollaborationPerformancePaginatedResponse(
        items=performances,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/by-project/{project}", response_model=schemas.CollaborationPerformancePaginatedResponse)
def get_performances_by_project(
    *,
    db: Session = Depends(get_db),
    project: str,
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
) -> Any:
    """
    获取特定项目的所有合作表现数据（分页）
    """
    skip = (page - 1) * size
    
    total = crud.collaboration_performance.count_by_project(db, project=project)
    performances = crud.collaboration_performance.get_by_project(
        db, project=project, skip=skip, limit=size
    )
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return schemas.CollaborationPerformancePaginatedResponse(
        items=performances,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/by-date-range/", response_model=schemas.CollaborationPerformancePaginatedResponse)
def get_performances_by_date_range(
    *,
    db: Session = Depends(get_db),
    start_date: datetime = Query(..., description="开始日期"),
    end_date: datetime = Query(..., description="结束日期"),
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
) -> Any:
    """
    获取特定日期范围的合作表现数据（分页）
    """
    skip = (page - 1) * size
    
    total = crud.collaboration_performance.count_by_post_date_range(
        db, start_date=start_date, end_date=end_date
    )
    performances = crud.collaboration_performance.get_by_post_date_range(
        db, start_date=start_date, end_date=end_date, skip=skip, limit=size
    )
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return schemas.CollaborationPerformancePaginatedResponse(
        items=performances,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


# 异步API端点示例
@router.get("/async/", response_model=schemas.CollaborationPerformancePaginatedResponse)
async def read_performances_async(
    db: AsyncSession = Depends(get_async_db),
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
) -> Any:
    """
    异步获取多个合作表现数据（分页）
    """
    skip = (page - 1) * size
    
    total = await crud.collaboration_performance.async_count(db)
    performances = await crud.collaboration_performance.async_get_multi(
        db, skip=skip, limit=size
    )
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return schemas.CollaborationPerformancePaginatedResponse(
        items=performances,
        total=total,
        page=page,
        size=size,
        pages=pages
    )