from typing import Any, List, Optional
from math import ceil

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app import crud, schemas
from app.db.session import get_async_db, get_db

router = APIRouter()


@router.get("/", response_model=schemas.FilterDataPaginatedResponse)
def read_filter_data(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    kol_id: Optional[str] = None,
    project_code: Optional[str] = None,
    page: int = 1,
) -> Any:
    """
    获取多个筛选条件，包含分页信息
    
    - **page**: 页码，从1开始
    - **limit**: 每页数量
    - **skip**: 跳过的记录数，优先级低于page参数
    - **kol_id**: 可选，按KOL ID筛选
    - **project_code**: 可选，按项目代码筛选
    
    返回:
        - **items**: 当前页的数据项
        - **total**: 总记录数
        - **page**: 当前页码
        - **size**: 每页大小
        - **pages**: 总页数
    """
    # 如果提供了page参数，则根据page和limit计算skip
    if page > 1:
        skip = (page - 1) * limit
    
    items = []
    total = 0
    
    if kol_id:
        items, total = crud.filter_data.get_multi_by_kol_id_with_count(
            db, kol_id=kol_id, skip=skip, limit=limit
        )
    elif project_code:
        items, total = crud.filter_data.get_multi_by_project_code_with_count(
            db, project_code=project_code, skip=skip, limit=limit
        )
    else:
        items, total = crud.filter_data.get_multi_with_count(
            db, skip=skip, limit=limit
        )
    
    # 计算总页数
    pages = ceil(total / limit) if limit > 0 else 0
    
    return {
        "items": items,
        "total": total,
        "page": page,
        "size": limit,
        "pages": pages,
    }


@router.post("/", response_model=schemas.FilterDataResponse)
def create_filter_data(
    *,
    db: Session = Depends(get_db),
    filter_data_in: schemas.FilterDataCreate,
) -> Any:
    """
    创建新的 filter_data 数据
    """
    filter_data = crud.filter_data.create(db, obj_in=filter_data_in)
    return filter_data


@router.get("/simple", response_model=schemas.FilterArraysResponse)
def read_simple_filter_data(
    db: Session = Depends(get_db),
) -> Any:
    """
    获取所有不同的筛选条件名称和项目代码，以两个数组形式返回
    
    返回数据结构:
        {
            "filter_names": ["筛选条件1", "筛选条件2", ...],
            "project_codes": ["项目1", "项目2", ...]
        }
    
    说明:
        - 返回的是两个去重后的数组，而不是数组对象
        - filter_names 包含所有不同的筛选条件名称
        - project_codes 包含所有不同的项目代码
        - 两个数组的元素不一定有一一对应关系
    """
    return crud.filter_data.get_filter_names_and_project_codes_as_arrays(db)


@router.get("/async", response_model=schemas.FilterDataPaginatedResponse)
async def read_filter_data_async(
    db: AsyncSession = Depends(get_async_db),
    skip: int = 0,
    limit: int = 100,
    page: int = 1,
    kol_id: Optional[str] = None,
    project_code: Optional[str] = None,
) -> Any:
    """
    异步获取多个筛选条件，包含分页信息
    
    - **page**: 页码，从1开始
    - **limit**: 每页数量
    - **skip**: 跳过的记录数，优先级低于page参数
    - **kol_id**: 可选，按KOL ID筛选
    - **project_code**: 可选，按项目代码筛选
    
    返回:
        - **items**: 当前页的数据项
        - **total**: 总记录数
        - **page**: 当前页码
        - **size**: 每页大小
        - **pages**: 总页数
    """
    # 如果提供了page参数，则根据page和limit计算skip
    if page > 1:
        skip = (page - 1) * limit
    
    items = []
    total = 0
    
    if kol_id:
        items, total = await crud.filter_data.async_get_multi_by_kol_id_with_count(
            db, kol_id=kol_id, skip=skip, limit=limit
        )
    elif project_code:
        items, total = await crud.filter_data.async_get_multi_by_project_code_with_count(
            db, project_code=project_code, skip=skip, limit=limit
        )
    else:
        items, total = await crud.filter_data.async_get_multi_with_count(
            db, skip=skip, limit=limit
        )
    
    # 计算总页数
    pages = ceil(total / limit) if limit > 0 else 0
    
    return {
        "items": items,
        "total": total,
        "page": page,
        "size": limit,
        "pages": pages,
    }


@router.get("/async/simple", response_model=schemas.FilterArraysResponse)
async def read_simple_filter_data_async(
    db: AsyncSession = Depends(get_async_db),
) -> Any:
    """
    异步获取所有不同的筛选条件名称和项目代码，以两个数组形式返回
    
    返回数据结构:
        {
            "filter_names": ["筛选条件1", "筛选条件2", ...],
            "project_codes": ["项目1", "项目2", ...]
        }
    
    说明:
        - 返回的是两个去重后的数组，而不是数组对象
        - filter_names 包含所有不同的筛选条件名称
        - project_codes 包含所有不同的项目代码
        - 两个数组的元素不一定有一一对应关系
        - 此接口是异步版本
    """
    return await crud.filter_data.async_get_filter_names_and_project_codes_as_arrays(db)


@router.get("/{id}", response_model=schemas.FilterDataResponse)
def read_filter_data_by_id(
    *,
    db: Session = Depends(get_db),
    id: int,
) -> Any:
    """
    通过ID获取筛选条件
    """
    filter_data = crud.filter_data.get(db, id=id)
    if not filter_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="筛选条件不存在",
        )
    return filter_data


@router.put("/{id}", response_model=schemas.FilterDataResponse)
def update_filter_data(
    *,
    db: Session = Depends(get_db),
    id: int,
    filter_data_in: schemas.FilterDataUpdate,
) -> Any:
    """
    更新筛选条件
    """
    filter_data = crud.filter_data.get(db, id=id)
    if not filter_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="筛选条件不存在",
        )
    filter_data = crud.filter_data.update(db, db_obj=filter_data, obj_in=filter_data_in)
    return filter_data


@router.delete("/{id}", response_model=schemas.FilterDataResponse)
def delete_filter_data(
    *,
    db: Session = Depends(get_db),
    id: int,
) -> Any:
    """
    删除筛选条件
    """
    filter_data = crud.filter_data.get(db, id=id)
    if not filter_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="筛选条件不存在",
        )
    filter_data = crud.filter_data.remove(db, id=id)
    return filter_data


@router.post("/{filter_id}/kols/{kol_id}", response_model=schemas.FilterKolAssociationResponse)
def associate_filter_with_kol(
    *,
    db: Session = Depends(get_db),
    filter_id: int,
    kol_id: str,
    project_code: Optional[str] = None,
) -> Any:
    """
    将筛选条件与KOL关联起来
    """
    # 检查筛选条件是否存在
    filter_data = crud.filter_data.get(db, id=filter_id)
    if not filter_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="筛选条件不存在",
        )
    
    # 检查KOL是否存在
    kol = crud.kol_info.get(db, id=kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在",
        )
    
    # 检查关联是否已存在
    existing = crud.filter_kol_association.get_by_filter_id_and_kol_id(
        db, filter_id=filter_id, kol_id=kol_id
    )
    if existing:
        # 如果关联已存在但项目代码不同，则更新项目代码
        if project_code and existing.project_code != project_code:
            association_update = schemas.FilterKolAssociationUpdate(project_code=project_code)
            return crud.filter_kol_association.update(db, db_obj=existing, obj_in=association_update)
        return existing
    
    # 创建新关联
    association_in = schemas.FilterKolAssociationCreate(
        filter_id=filter_id,
        kol_id=kol_id,
        project_code=project_code
    )
    return crud.filter_kol_association.create(db, obj_in=association_in)


@router.get("/{filter_id}/kols", response_model=List[schemas.KOLInfoResponse])
def get_kols_by_filter(
    *,
    db: Session = Depends(get_db),
    filter_id: int,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取与特定筛选条件关联的所有KOL
    """
    # 检查筛选条件是否存在
    filter_data = crud.filter_data.get(db, id=filter_id)
    if not filter_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="筛选条件不存在",
        )
    
    return crud.kol_info.get_multi_by_filter_id(db, filter_id=filter_id, skip=skip, limit=limit)


@router.get("/by_name/{filter_name}", response_model=schemas.FilterDataResponse)
def read_filter_data_by_name(
    *,
    db: Session = Depends(get_db),
    filter_name: str,
) -> Any:
    """
    通过筛选条件名称获取筛选条件
    """
    filter_data = crud.filter_data.get_by_name(db, name=filter_name)
    if not filter_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="筛选条件不存在",
        )
    return filter_data 