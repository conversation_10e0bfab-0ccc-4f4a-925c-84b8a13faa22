from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
import math

from app import crud, schemas
from app.api import deps

router = APIRouter()


@router.post("/", response_model=schemas.KolTagAssociationResponse, status_code=status.HTTP_201_CREATED)
def create_kol_tag_association(
    *,
    db: Session = Depends(deps.get_db),
    association_in: schemas.KolTagAssociationCreate
) -> Any:
    """
    创建KOL标签关联
    """
    # 检查KOL是否存在
    kol = crud.kol_info.get(db, id=association_in.kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在"
        )
    
    # 检查标签是否存在
    tag = crud.tag.get(db, id=association_in.tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    # 检查关联是否已存在
    existing = crud.kol_tag_association.get_by_kol_and_tag(
        db, kol_id=association_in.kol_id, tag_id=association_in.tag_id
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="KOL标签关联已存在"
        )
    
    association = crud.kol_tag_association.create(db, obj_in=association_in)
    return association


@router.get("/", response_model=schemas.KolTagAssociationPaginatedResponse)
def read_kol_tag_associations(
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    kol_id: str = Query(None, description="按KOL ID筛选"),
    tag_id: int = Query(None, description="按标签ID筛选"),
    project_code: str = Query(None, description="按项目编码筛选")
) -> Any:
    """
    获取KOL标签关联列表
    """
    if kol_id:
        associations = crud.kol_tag_association.get_by_kol_id(db, kol_id=kol_id, skip=skip, limit=limit)
        total = crud.kol_tag_association.count_by_kol_id(db, kol_id=kol_id)
    elif tag_id:
        associations = crud.kol_tag_association.get_by_tag_id(db, tag_id=tag_id, skip=skip, limit=limit)
        total = crud.kol_tag_association.count_by_tag_id(db, tag_id=tag_id)
    elif project_code:
        associations = crud.kol_tag_association.get_by_project_code(db, project_code=project_code, skip=skip, limit=limit)
        total = crud.kol_tag_association.count_by_project_code(db, project_code=project_code)
    else:
        associations = crud.kol_tag_association.get_multi(db, skip=skip, limit=limit)
        total = db.query(crud.kol_tag_association.model).count()
    
    return schemas.KolTagAssociationPaginatedResponse(
        items=associations,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=math.ceil(total / limit) if total > 0 else 0
    )


@router.get("/{association_id}", response_model=schemas.KolTagAssociationResponse)
def read_kol_tag_association(
    *,
    db: Session = Depends(deps.get_db),
    association_id: int
) -> Any:
    """
    根据ID获取KOL标签关联
    """
    association = crud.kol_tag_association.get(db, id=association_id)
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL标签关联不存在"
        )
    return association


@router.put("/{association_id}", response_model=schemas.KolTagAssociationResponse)
def update_kol_tag_association(
    *,
    db: Session = Depends(deps.get_db),
    association_id: int,
    association_in: schemas.KolTagAssociationUpdate
) -> Any:
    """
    更新KOL标签关联
    """
    association = crud.kol_tag_association.get(db, id=association_id)
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL标签关联不存在"
        )
    
    association = crud.kol_tag_association.update(db, db_obj=association, obj_in=association_in)
    return association


@router.delete("/{association_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_kol_tag_association(
    *,
    db: Session = Depends(deps.get_db),
    association_id: int
) -> None:
    """
    删除KOL标签关联
    """
    association = crud.kol_tag_association.get(db, id=association_id)
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL标签关联不存在"
        )
    
    crud.kol_tag_association.remove(db, id=association_id)
    return None


@router.post("/batch", response_model=List[schemas.KolTagAssociationResponse])
def create_batch_kol_tag_associations(
    *,
    db: Session = Depends(deps.get_db),
    batch_in: schemas.BatchKolTagAssociationCreate
) -> Any:
    """
    批量创建KOL标签关联
    """
    # 检查KOL是否存在
    kol = crud.kol_info.get(db, id=batch_in.kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在"
        )
    
    # 检查标签是否都存在
    existing_tags = crud.tag.get_multi_by_ids(db, ids=batch_in.tag_ids)
    existing_tag_ids = {tag.id for tag in existing_tags}
    missing_tag_ids = set(batch_in.tag_ids) - existing_tag_ids
    
    if missing_tag_ids:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"标签不存在: {list(missing_tag_ids)}"
        )
    
    associations = crud.kol_tag_association.create_batch(
        db, kol_id=batch_in.kol_id, tag_ids=batch_in.tag_ids, project_code=batch_in.project_code
    )
    return associations


@router.delete("/batch", status_code=status.HTTP_200_OK)
def delete_batch_kol_tag_associations(
    *,
    db: Session = Depends(deps.get_db),
    batch_in: schemas.BatchKolTagAssociationDelete
) -> Any:
    """
    批量删除KOL标签关联
    """
    deleted_count = crud.kol_tag_association.delete_batch(
        db, kol_id=batch_in.kol_id, tag_ids=batch_in.tag_ids
    )
    return {"message": f"成功删除 {deleted_count} 个关联"}


@router.get("/kol/{kol_id}/tags", response_model=List[schemas.TagResponse])
def get_kol_tags(
    *,
    db: Session = Depends(deps.get_db),
    kol_id: str
) -> Any:
    """
    获取KOL关联的所有标签
    """
    # 检查KOL是否存在
    kol = crud.kol_info.get(db, id=kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在"
        )
    
    tags = crud.kol_tag_association.get_kol_tags(db, kol_id=kol_id)
    return tags


@router.get("/tag/{tag_id}/kols", response_model=List[str])
def get_tag_kols(
    *,
    db: Session = Depends(deps.get_db),
    tag_id: int
) -> Any:
    """
    获取标签关联的所有KOL ID
    """
    # 检查标签是否存在
    tag = crud.tag.get(db, id=tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    kol_ids = crud.kol_tag_association.get_tag_kols(db, tag_id=tag_id)
    return kol_ids