from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app import crud, schemas
from app.db.session import get_db

router = APIRouter()


@router.get("/", response_model=List[schemas.VideoInfoResponse])
def read_videos(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    platform: Optional[str] = None,
    kol_id: Optional[str] = None,
) -> Any:
    """
    获取 video_info 信息
    """
    if kol_id:
        videos = crud.video_info.get_multi_by_kol_id(
            db, kol_id=kol_id, skip=skip, limit=limit
        )
    elif platform:
        videos = crud.video_info.get_multi_by_platform(
            db, platform=platform, skip=skip, limit=limit
        )
    else:
        videos = crud.video_info.get_multi(db, skip=skip, limit=limit)
    return videos


@router.post("/", response_model=schemas.VideoInfoResponse)
def create_video(
    *,
    db: Session = Depends(get_db),
    video_in: schemas.VideoInfoCreate,
) -> Any:
    """
    创建新的视频信息
    """
    video = crud.video_info.get(db, id=video_in.video_id)
    if video:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"视频已存在，ID: {video_in.video_id}",
        )
    
    # 检查KOL是否存在，如果不存在则创建一个新的KOL
    kol = crud.kol_info.get(db, id=video_in.kol_id)
    if not kol:
        # 创建一个新的KOL，只填写必填字段
        from app.schemas.kol_info import KOLInfoCreate
        new_kol_data = KOLInfoCreate(
            kol_id=video_in.kol_id,
            kol_name=f"未知KOL_{video_in.kol_id}",  # 使用kol_id作为名称的一部分
            username=None,
            email=None,
            bio=None,
            account_link=None,
            followers_k=None,
            likes_k=None,
            platform=None,
            source="auto_created",  # 标记为自动创建
            slug=None,
            creator_id=None,
            mean_views_k=None,
            median_views_k=None,
            engagement_rate=None,
            average_views_k=None,
            average_likes_k=None,
            average_comments_k=None,
            most_used_hashtags=None,
            level=None,
            keywords_ai=None
        )
        crud.kol_info.create(db, obj_in=new_kol_data)
    
    video = crud.video_info.create(db, obj_in=video_in)
    return video


@router.get("/{video_id}", response_model=schemas.VideoInfoResponse)
def read_video(
    *,
    db: Session = Depends(get_db),
    video_id: str,
) -> Any:
    """
    通过ID获取视频信息
    """
    video = crud.video_info.get(db, id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在",
        )
    return video


@router.put("/{video_id}", response_model=schemas.VideoInfoResponse)
def update_video(
    *,
    db: Session = Depends(get_db),
    video_id: str,
    video_in: schemas.VideoInfoUpdate,
) -> Any:
    """
    更新视频信息
    """
    video = crud.video_info.get(db, id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在",
        )
    video = crud.video_info.update(db, db_obj=video, obj_in=video_in)
    return video


@router.delete("/{video_id}", response_model=schemas.VideoInfoResponse)
def delete_video(
    *,
    db: Session = Depends(get_db),
    video_id: str,
) -> Any:
    """
    删除视频信息
    """
    video = crud.video_info.get(db, id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在",
        )
    video = crud.video_info.remove(db, id=video_id)
    return video


@router.get("/kol/{kol_id}", response_model=List[schemas.VideoInfoResponse])
def read_videos_by_kol(
    *,
    db: Session = Depends(get_db),
    kol_id: str,
) -> Any:
    """
    获取指定KOL的所有视频
    """
    # 验证KOL是否存在
    kol = crud.kol_info.get(db, id=kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"KOL不存在，ID: {kol_id}",
        )
    videos = crud.video_info.get_multi_by_kol_id(
        db, kol_id=kol_id
    )
    return videos


@router.delete("/kol/{kol_id}", response_model=List[schemas.VideoInfoResponse])
def delete_videos_by_kol(
    *,
    db: Session = Depends(get_db),
    kol_id: str,
) -> Any:
    """
    删除指定KOL的所有视频
    """
    # 验证KOL是否存在
    kol = crud.kol_info.get(db, id=kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"KOL不存在，ID: {kol_id}",
        )
    
    # 获取要删除的视频列表
    videos = crud.video_info.get_multi_by_kol_id(
        db, kol_id=kol_id
    )
    
    if not videos:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"该KOL没有关联的视频，ID: {kol_id}",
        )
    
    # 删除所有关联的视频
    deleted_videos = crud.video_info.remove_by_kol_id(
        db, kol_id=kol_id
    )
    
    return deleted_videos