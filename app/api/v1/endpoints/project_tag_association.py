from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
import math

from app import crud, schemas
from app.api import deps

router = APIRouter()


@router.post("/", response_model=schemas.ProjectTagAssociationResponse, status_code=status.HTTP_201_CREATED)
def create_project_tag_association(
    *,
    db: Session = Depends(deps.get_db),
    association_in: schemas.ProjectTagAssociationCreate
) -> Any:
    """
    创建项目标签关联
    """
    # 注意：这里假设项目编码是有效的，如果需要验证项目存在性，
    # 可以根据实际的项目管理模块进行验证
    
    # 检查标签是否存在
    tag = crud.tag.get(db, id=association_in.tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    # 检查关联是否已存在
    existing = crud.project_tag_association.get_by_project_and_tag(
        db, project_code=association_in.project_code, tag_id=association_in.tag_id
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="项目标签关联已存在"
        )
    
    association = crud.project_tag_association.create(db, obj_in=association_in)
    return association


@router.get("/", response_model=schemas.ProjectTagAssociationPaginatedResponse)
def read_project_tag_associations(
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    project_code: str = Query(None, description="按项目编码筛选"),
    tag_id: int = Query(None, description="按标签ID筛选")
) -> Any:
    """
    获取项目标签关联列表
    """
    if project_code:
        associations = crud.project_tag_association.get_by_project_code(db, project_code=project_code, skip=skip, limit=limit)
        total = crud.project_tag_association.count_by_project_code(db, project_code=project_code)
    elif tag_id:
        associations = crud.project_tag_association.get_by_tag_id(db, tag_id=tag_id, skip=skip, limit=limit)
        total = crud.project_tag_association.count_by_tag_id(db, tag_id=tag_id)
    else:
        associations = crud.project_tag_association.get_multi(db, skip=skip, limit=limit)
        total = db.query(crud.project_tag_association.model).count()
    
    return schemas.ProjectTagAssociationPaginatedResponse(
        items=associations,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=math.ceil(total / limit) if total > 0 else 0
    )


@router.get("/{association_id}", response_model=schemas.ProjectTagAssociationResponse)
def read_project_tag_association(
    *,
    db: Session = Depends(deps.get_db),
    association_id: int
) -> Any:
    """
    根据ID获取项目标签关联
    """
    association = crud.project_tag_association.get(db, id=association_id)
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目标签关联不存在"
        )
    return association


@router.put("/{association_id}", response_model=schemas.ProjectTagAssociationResponse)
def update_project_tag_association(
    *,
    db: Session = Depends(deps.get_db),
    association_id: int,
    association_in: schemas.ProjectTagAssociationUpdate
) -> Any:
    """
    更新项目标签关联
    """
    association = crud.project_tag_association.get(db, id=association_id)
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目标签关联不存在"
        )
    
    association = crud.project_tag_association.update(db, db_obj=association, obj_in=association_in)
    return association


@router.delete("/{association_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_project_tag_association(
    *,
    db: Session = Depends(deps.get_db),
    association_id: int
) -> None:
    """
    删除项目标签关联
    """
    association = crud.project_tag_association.get(db, id=association_id)
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目标签关联不存在"
        )
    
    crud.project_tag_association.remove(db, id=association_id)
    return None


@router.post("/batch", response_model=List[schemas.ProjectTagAssociationResponse])
def create_batch_project_tag_associations(
    *,
    db: Session = Depends(deps.get_db),
    batch_in: schemas.BatchProjectTagAssociationCreate
) -> Any:
    """
    批量创建项目标签关联
    """
    # 注意：这里假设项目编码是有效的，如果需要验证项目存在性，
    # 可以根据实际的项目管理模块进行验证
    
    # 检查标签是否都存在
    existing_tags = crud.tag.get_multi_by_ids(db, ids=batch_in.tag_ids)
    existing_tag_ids = {tag.id for tag in existing_tags}
    missing_tag_ids = set(batch_in.tag_ids) - existing_tag_ids
    
    if missing_tag_ids:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"标签不存在: {list(missing_tag_ids)}"
        )
    
    associations = crud.project_tag_association.create_batch(
        db, project_code=batch_in.project_code, tag_ids=batch_in.tag_ids
    )
    return associations


@router.delete("/batch", status_code=status.HTTP_200_OK)
def delete_batch_project_tag_associations(
    *,
    db: Session = Depends(deps.get_db),
    batch_in: schemas.BatchProjectTagAssociationDelete
) -> Any:
    """
    批量删除项目标签关联
    """
    deleted_count = crud.project_tag_association.delete_batch(
        db, project_code=batch_in.project_code, tag_ids=batch_in.tag_ids
    )
    return {"message": f"成功删除 {deleted_count} 个关联"}


@router.get("/project/{project_code}/tags", response_model=schemas.ProjectTagsResponse)
def get_project_tags(
    *,
    db: Session = Depends(deps.get_db),
    project_code: str
) -> Any:
    """
    获取项目关联的所有标签
    """
    # 注意：这里假设项目编码是有效的，如果需要验证项目存在性，
    # 可以根据实际的项目管理模块进行验证
    
    tags = crud.project_tag_association.get_project_tags(db, project_code=project_code)
    return schemas.ProjectTagsResponse(
        project_code=project_code,
        tags=tags
    )


@router.get("/tag/{tag_id}/projects", response_model=List[str])
def get_tag_projects(
    *,
    db: Session = Depends(deps.get_db),
    tag_id: int
) -> Any:
    """
    获取标签关联的所有项目编码
    """
    # 检查标签是否存在
    tag = crud.tag.get(db, id=tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    project_codes = crud.project_tag_association.get_tag_projects(db, tag_id=tag_id)
    return project_codes


@router.put("/project/{project_code}/tags", response_model=schemas.ProjectTagsResponse)
def replace_project_tags(
    *,
    db: Session = Depends(deps.get_db),
    project_code: str,
    tag_ids: List[int]
) -> Any:
    """
    替换项目的所有标签
    """
    # 注意：这里假设项目编码是有效的，如果需要验证项目存在性，
    # 可以根据实际的项目管理模块进行验证
    
    # 检查标签是否都存在
    if tag_ids:
        existing_tags = crud.tag.get_multi_by_ids(db, ids=tag_ids)
        existing_tag_ids = {tag.id for tag in existing_tags}
        missing_tag_ids = set(tag_ids) - existing_tag_ids
        
        if missing_tag_ids:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"标签不存在: {list(missing_tag_ids)}"
            )
    
    # 替换标签
    tags = crud.project_tag_association.replace_project_tags(
        db, project_code=project_code, tag_ids=tag_ids
    )
    
    return schemas.ProjectTagsResponse(
        project_code=project_code,
        tags=tags
    )