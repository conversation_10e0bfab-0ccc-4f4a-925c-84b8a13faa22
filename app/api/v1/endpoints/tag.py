from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
import math

from app import crud, schemas
from app.api import deps

router = APIRouter()


@router.post("/", response_model=schemas.TagResponse, status_code=status.HTTP_201_CREATED)
def create_tag(
    *,
    db: Session = Depends(deps.get_db),
    tag_in: schemas.TagCreate
) -> Any:
    """
    创建新标签
    """
    # 检查标签名称是否已存在
    if crud.tag.get_by_name(db, name=tag_in.name):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="标签名称已存在"
        )
    
    tag = crud.tag.create(db, obj_in=tag_in)
    return tag


@router.get("/", response_model=schemas.TagPaginatedResponse)
def read_tags(
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    search: str = Query(None, description="按标签名称搜索")
) -> Any:
    """
    获取标签列表
    """
    if search:
        tags = crud.tag.search_by_name(db, name=search, skip=skip, limit=limit)
        total = crud.tag.count_search_by_name(db, name=search)
    else:
        tags = crud.tag.get_multi(db, skip=skip, limit=limit)
        total = db.query(crud.tag.model).count()
    
    return schemas.TagPaginatedResponse(
        items=tags,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=math.ceil(total / limit) if total > 0 else 0
    )


@router.get("/{tag_id}", response_model=schemas.TagResponse)
def read_tag(
    *,
    db: Session = Depends(deps.get_db),
    tag_id: int
) -> Any:
    """
    根据ID获取标签
    """
    tag = crud.tag.get(db, id=tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    return tag


@router.put("/{tag_id}", response_model=schemas.TagResponse)
def update_tag(
    *,
    db: Session = Depends(deps.get_db),
    tag_id: int,
    tag_in: schemas.TagUpdate
) -> Any:
    """
    更新标签
    """
    tag = crud.tag.get(db, id=tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    # 如果更新名称，检查是否与其他标签重复
    if tag_in.name and crud.tag.check_name_exists(db, name=tag_in.name, exclude_id=tag_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="标签名称已存在"
        )
    
    tag = crud.tag.update(db, db_obj=tag, obj_in=tag_in)
    return tag


@router.delete("/{tag_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_tag(
    *,
    db: Session = Depends(deps.get_db),
    tag_id: int
) -> None:
    """
    删除标签
    """
    tag = crud.tag.get(db, id=tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    # 检查是否有关联的KOL或项目
    kol_associations_count = crud.kol_tag_association.count_by_tag_id(db, tag_id=tag_id)
    project_associations_count = crud.project_tag_association.count_by_tag_id(db, tag_id=tag_id)
    
    if kol_associations_count > 0 or project_associations_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无法删除标签，存在 {kol_associations_count} 个KOL关联和 {project_associations_count} 个项目关联"
        )
    
    crud.tag.remove(db, id=tag_id)
    return None


@router.post("/batch", response_model=List[schemas.TagResponse])
def create_or_get_tags(
    *,
    db: Session = Depends(deps.get_db),
    tag_names: List[str]
) -> Any:
    """
    批量创建或获取标签
    """
    if not tag_names:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="标签名称列表不能为空"
        )
    
    # 去重
    unique_names = list(set(tag_names))
    tags = crud.tag.get_or_create_multi(db, names=unique_names)
    return tags


@router.get("/name/{tag_name}", response_model=schemas.TagResponse)
def read_tag_by_name(
    *,
    db: Session = Depends(deps.get_db),
    tag_name: str
) -> Any:
    """
    根据名称获取标签
    """
    tag = crud.tag.get_by_name(db, name=tag_name)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    return tag