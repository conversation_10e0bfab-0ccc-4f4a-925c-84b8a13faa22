from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app import crud, schemas
from app.db.session import get_async_db, get_db

router = APIRouter()


@router.get("/", response_model=List[schemas.FilterKolAssociationResponse])
def read_associations(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    filter_id: Optional[int] = None,
    kol_id: Optional[str] = None,
    project_code: Optional[str] = None,
) -> Any:
    """
    获取多个关联对象
    """
    if filter_id:
        associations = crud.filter_kol_association.get_multi_by_filter_id(
            db, filter_id=filter_id, skip=skip, limit=limit
        )
    elif kol_id:
        associations = crud.filter_kol_association.get_multi_by_kol_id(
            db, kol_id=kol_id, skip=skip, limit=limit
        )
    elif project_code:
        associations = crud.filter_kol_association.get_multi_by_project_code(
            db, project_code=project_code, skip=skip, limit=limit
        )
    else:
        associations = crud.filter_kol_association.get_multi(db, skip=skip, limit=limit)
    
    return associations


@router.post("/", response_model=schemas.FilterKolAssociationResponse)
def create_association(
    *,
    db: Session = Depends(get_db),
    association_in: schemas.FilterKolAssociationCreate,
) -> Any:
    """
    创建新的关联
    """
    # 检查筛选条件是否存在
    filter_data = crud.filter_data.get(db, id=association_in.filter_id)
    if not filter_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="筛选条件不存在",
        )
    
    # 检查KOL是否存在
    kol = crud.kol_info.get(db, id=association_in.kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在",
        )
    
    # 检查关联是否已存在
    existing = crud.filter_kol_association.get_by_filter_id_and_kol_id(
        db, filter_id=association_in.filter_id, kol_id=association_in.kol_id
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="关联已存在",
        )
    
    association = crud.filter_kol_association.create(db, obj_in=association_in)
    return association


@router.get("/{id}", response_model=schemas.FilterKolAssociationResponse)
def read_association(
    *,
    db: Session = Depends(get_db),
    id: int,
) -> Any:
    """
    通过ID获取关联对象
    """
    association = crud.filter_kol_association.get(db, id=id)
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关联不存在",
        )
    return association


@router.put("/{id}", response_model=schemas.FilterKolAssociationResponse)
def update_association(
    *,
    db: Session = Depends(get_db),
    id: int,
    association_in: schemas.FilterKolAssociationUpdate,
) -> Any:
    """
    更新关联对象
    """
    association = crud.filter_kol_association.get(db, id=id)
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关联不存在",
        )
    
    # 如果更新了筛选条件ID，检查筛选条件是否存在
    if association_in.filter_id is not None:
        filter_data = crud.filter_data.get(db, id=association_in.filter_id)
        if not filter_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="筛选条件不存在",
            )
    
    # 如果更新了KOL ID，检查KOL是否存在
    if association_in.kol_id is not None:
        kol = crud.kol_info.get(db, id=association_in.kol_id)
        if not kol:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="KOL不存在",
            )
    
    association = crud.filter_kol_association.update(db, db_obj=association, obj_in=association_in)
    return association


@router.delete("/{id}", response_model=schemas.FilterKolAssociationResponse)
def delete_association(
    *,
    db: Session = Depends(get_db),
    id: int,
) -> Any:
    """
    删除关联对象
    """
    association = crud.filter_kol_association.get(db, id=id)
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关联不存在",
        )
    
    association = crud.filter_kol_association.remove(db, id=id)
    return association


@router.get("/by-project/{project_code}", response_model=List[schemas.FilterKolAssociationResponse])
def get_associations_by_project(
    *,
    db: Session = Depends(get_db),
    project_code: str,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取特定项目的所有关联对象
    """
    return crud.filter_kol_association.get_multi_by_project_code(
        db, project_code=project_code, skip=skip, limit=limit
    )


# 异步API端点示例
@router.get("/async/", response_model=List[schemas.FilterKolAssociationResponse])
async def read_associations_async(
    db: AsyncSession = Depends(get_async_db),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    异步获取多个关联对象
    """
    associations = await crud.filter_kol_association.async_get_multi(db, skip=skip, limit=limit)
    return associations 