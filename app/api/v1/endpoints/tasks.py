"""
任务管理相关的API路由
处理异步任务的创建和查询
"""
from typing import Any, Dict, List, Optional

from celery import current_app
from celery.result import AsyncResult
from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.orm import Session
from loguru import logger
import redis
from datetime import datetime, timedelta

from app import schemas
from app.db.session import get_db
from app.worker.celery_app import celery_app
from app.worker.tasks.example import add, long_running_task, db_operation_task
from app.worker.tasks.kol_data import collect_kol_data_task, check_kol_task_status
from app.core.config import settings
from app.services.task_service import get_queue_status, check_task_health, revoke_zombie_tasks
from app.services.tools import send_webhook

router = APIRouter()


# @router.post(
#     "/generic", 
#     response_model=schemas.task.TaskStatus,
#     status_code=status.HTTP_202_ACCEPTED,
#     summary="创建通用任务",
#     description="创建一个通用的Celery任务，任务名称必须在系统中注册",
# )
# def create_generic_task(
#     task: schemas.task.TaskCreate,
#     db: Session = Depends(get_db),
# ) -> Any:
#     """
#     创建一个通用的Celery任务

#     - **task_name**: 任务名称，必须是已注册的任务
#     - **args**: 任务参数列表 (可选)
#     - **kwargs**: 任务命名参数 (可选)
#     - **queue**: 队列名称 (可选)
#     - **countdown**: 延迟执行时间(秒) (可选)
#     """
#     try:
#         # 获取任务
#         task_func = celery_app.tasks.get(task.task_name)
#         if not task_func:
#             raise HTTPException(
#                 status_code=status.HTTP_404_NOT_FOUND,
#                 detail=f"任务 '{task.task_name}' 未注册"
#             )
        
#         # 准备任务参数
#         args = task.args or []
#         kwargs = task.kwargs or {}
#         options = {}
        
#         if task.queue:
#             options["queue"] = task.queue
        
#         if task.countdown:
#             options["countdown"] = task.countdown
        
#         # 执行任务
#         async_result = task_func.apply_async(
#             args=args,
#             kwargs=kwargs,
#             **options
#         )
        
#         # 返回任务状态
#         return {
#             "task_id": async_result.id,
#             "status": async_result.status,
#         }
        
#     except Exception as e:
#         logger.error(f"创建任务失败: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"创建任务失败: {str(e)}"
#         )


@router.post(
    "/kol/collect",
    response_model=schemas.task.TaskStatus,
    status_code=status.HTTP_202_ACCEPTED,
    summary="创建KOL数据采集任务",
    description="创建一个KOL数据采集的后台任务，该任务可能需要较长时间执行",
)
def create_kol_data_task(
    task_data: schemas.task.KOLDataTask,
    db: Session = Depends(get_db),
) -> Any:
    """
    创建KOL数据采集任务
    
    接收KOL数据采集参数，创建后台任务执行数据获取、处理和存储流程
    """
    try:
        # 准备任务参数
        query_params = {
            "source": task_data.source,
            "platform": task_data.platform,
            "filter_name": task_data.filter_name,
            "filter_body": task_data.filter_body,
            "cookie": task_data.cookie,
            "project_code": task_data.project_code,
            "auto_send": task_data.auto_send,
            "template": task_data.template,
            "high_potential": task_data.high_potential,
            "table_save": task_data.table_save,
            "table_duplicate": task_data.table_duplicate,
        }

        click = task_data.click
        if click == "false":
            # 记录任务开始
            logger.info(f"创建KOL数据采集任务: {task_data.filter_name} | 平台: {task_data.platform}")
            
            # 发送任务到队列
            task = collect_kol_data_task.apply_async(
                kwargs={"query_params": query_params},
                queue="kol_data",
            )
            
            logger.info(f"KOL数据采集任务已创建: {task.id}")
            send_webhook(filter_name=task_data.filter_name, status=f"排队中", task_id=task.id)
            
            # 返回任务ID和状态
            return {
                "task_id": task.id,
                "status": task.status,
                "result": None,
                "error": None,
            }
        else:
            logger.info(f"重复执行任务: {task_data.filter_name} | 平台: {task_data.platform}")
            raise HTTPException(
                status_code=status.HTTP_302_FOUND,
                detail=f"重复执行任务"
            )
    except Exception as e:
        logger.error(f"创建KOL数据采集任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建任务失败: {str(e)}"
        )


@router.get(
    "/queue/status",
    response_model=schemas.task.QueueStatusResponse,
    summary="获取任务队列状态",
    description="获取Celery队列中不同状态的任务信息",
)
def get_queue_status_api(
    limit: Optional[int] = Query(20, description="每种状态返回的最大任务数量"),
    queue_name: Optional[str] = Query(None, description="队列名称，不提供则查询所有队列"),
) -> Any:
    """
    获取队列中的任务状态信息
    
    返回不同状态（正在执行、等待、已完成、失败）的任务信息，包括task_id和filter_name
    """
    try:
        # 调用服务层函数获取队列状态
        return get_queue_status(limit=limit, queue_name=queue_name)
        
    except Exception as e:
        logger.error(f"获取队列状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取队列状态失败: {str(e)}"
        )


@router.get(
    "/{task_id}",
    response_model=schemas.task.TaskStatus,
    summary="获取任务状态",
    description="通过任务ID获取任务的执行状态和结果",
)
def get_task_status(
    task_id: str,
) -> Any:
    """
    获取任务的状态和结果

    - **task_id**: 任务ID
    """
    # 获取任务结果
    task_result = AsyncResult(task_id, app=celery_app)
    
    # 准备响应
    response = {
        "task_id": task_id,
        "status": task_result.status,
        "result": None,
        "error": None,
        "progress": None,
    }
    
    # 根据状态处理结果
    if task_result.successful():
        response["result"] = task_result.get()
    elif task_result.failed():
        response["error"] = str(task_result.info)
    elif task_result.status == "PROGRESS" and task_result.info:
        response["progress"] = task_result.info.get('current', 0)
    
    return response


@router.delete(
    "/{task_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="取消任务",
    description="取消一个正在执行或等待执行的任务",
)
def cancel_task(
    task_id: str,
) -> None:
    """
    取消任务

    - **task_id**: 任务ID
    """
    try:
        # 获取任务
        task_result = AsyncResult(task_id, app=celery_app)
        
        # 检查任务是否存在
        if not task_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务 '{task_id}' 不存在"
            )
        
        # 检查任务状态
        if task_result.ready():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"任务 '{task_id}' 已完成，无法取消"
            )
        
        # 先用celery.control.revoke强制终止任务
        celery_app.control.revoke(task_id, terminate=True, signal='SIGKILL')
        
        # 然后用AsyncResult.revoke方法标记任务为已取消
        task_result.revoke(terminate=True)
        
        # 尝试提取任务名称，用于日志记录
        try:
            original_task_info = task_result.info
            filter_name = None
            if isinstance(original_task_info, dict) and "filter_name" in original_task_info:
                filter_name = original_task_info["filter_name"]
            
            # 如果能获取到filter_name，发送webhook通知
            if filter_name:
                send_webhook(filter_name=filter_name, status="已取消", task_id=task_id)
                logger.info(f"任务已取消: ID={task_id}, 名称={filter_name}")
            else:
                logger.info(f"任务已取消: ID={task_id}")
        except Exception as e:
            logger.warning(f"发送取消通知失败: {str(e)}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消任务失败: {str(e)}"
        )


@router.get(
    "/health/{task_id}",
    response_model=schemas.task.TaskHealthStatus,
    summary="检查任务健康状态",
    description="检查任务的健康状态，判断是否为僵尸任务",
)
def check_task_health_api(
    task_id: str,
    timeout: int = Query(120, description="超时时间（秒），超过这个时间没有心跳则视为僵尸任务")
) -> Any:
    """
    检查任务的健康状态
    
    - **task_id**: 任务ID
    - **timeout**: 超时时间（秒）
    """
    try:
        return check_task_health(task_id, timeout)
    except Exception as e:
        logger.error(f"检查任务健康状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查任务健康状态失败: {str(e)}"
        )


@router.post(
    "/cleanup/zombies",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="清理僵尸任务",
    description="检测并撤销所有僵尸任务",
)
def cleanup_zombie_tasks(
    timeout: int = Query(300, description="超时时间（秒），超过这个时间没有心跳则视为僵尸任务")
) -> Any:
    """
    检测并撤销所有僵尸任务
    
    - **timeout**: 超时时间（秒）
    """
    try:
        return revoke_zombie_tasks(timeout)
    except Exception as e:
        logger.error(f"清理僵尸任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理僵尸任务失败: {str(e)}"
        )
