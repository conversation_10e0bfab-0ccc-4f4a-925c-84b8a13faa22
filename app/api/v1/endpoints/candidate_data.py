from typing import Any, Optional
import math
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, select

from app import crud, schemas
from app.api import deps
from app.schemas.candidate_data import (
    CandidateDataAdvancedSearchRequest,
    CandidateDataAggregatedPaginatedResponse,
    CandidateDataAggregated,
    CandidateDataPaginatedResponse
)


router = APIRouter()


@router.get("/", response_model=CandidateDataAggregatedPaginatedResponse)
def read_candidate_datas(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    project: Optional[str] = None,
    label: Optional[str] = None,
    reply_email: Optional[str] = None
) -> Any:
    """
    获取按kol_id聚合的KOL候选人数据列表
    
    参数:
        db: 数据库会话，由依赖注入提供
        skip: 分页偏移量，默认为0
        limit: 每页数据量，默认为100
        project: 可选，按项目筛选
        label: 可选，按标签筛选
        reply_email: 可选，按回复邮箱筛选
        
    返回:
        按kol_id聚合的分页KOL候选人数据列表，相同字段显示一个值，不同字段聚合成数组
    """
    page = skip // limit + 1 if limit > 0 else 1
    
    # 获取聚合数据
    aggregated_data, total = crud.candidate_data.get_aggregated_by_kol_id(
        db, skip=skip, limit=limit, project=project, label=label, reply_email=reply_email
    )
    
    # 转换为响应模型
    items = []
    for data in aggregated_data:
        item = CandidateDataAggregated(
            kol_id=data['kol_id'],
            kol_name=data['kol_name'],
            first_contact_dates=data['first_contact_dates'],
            last_contact_dates=data['last_contact_dates'],
            follow_ups=data['follow_ups'],
            labels=data['labels'],
            sublabels=data['sublabels'],
            thread_ids=data['thread_ids'],
            projects=data['projects'],
            reply_emails=data['reply_emails'],
            ids=data['ids'],
            created_ats=data['created_ats'],
            updated_ats=data['updated_ats'],
            record_count=data['record_count']
        )
        items.append(item)
    
    pages = math.ceil(total / limit) if limit > 0 else 1
    
    return {
        "items": items,
        "total": total,
        "page": page,
        "size": limit,
        "pages": pages
    }


@router.get("/raw", response_model=CandidateDataPaginatedResponse)
def read_raw_candidate_datas(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    project: Optional[str] = None,
    label: Optional[str] = None,
    reply_email: Optional[str] = None
) -> Any:
    """
    获取不聚合的KOL候选人数据列表
    
    参数:
        db: 数据库会话，由依赖注入提供
        skip: 分页偏移量，默认为0
        limit: 每页数据量，默认为100
        project: 可选，按项目筛选
        label: 可选，按标签筛选
        reply_email: 可选，按回复邮箱筛选
        
    返回:
        不聚合的分页KOL候选人数据列表，每条记录保持原始状态
    """
    page = skip // limit + 1 if limit > 0 else 1
    
    # 构建查询
    query = db.query(crud.candidate_data.model)
    
    # 应用筛选条件
    if project:
        query = query.filter(crud.candidate_data.model.project == project)
    if label:
        query = query.filter(crud.candidate_data.model.label == label)
    if reply_email:
        query = query.filter(crud.candidate_data.model.reply_email == reply_email)
    
    # 获取总数
    total = query.count()
    
    # 应用分页和排序
    items = query.order_by(crud.candidate_data.model.created_at.desc()).offset(skip).limit(limit).all()
    
    pages = math.ceil(total / limit) if limit > 0 else 1
    
    return {
        "items": items,
        "total": total,
        "page": page,
        "size": limit,
        "pages": pages
    }


@router.post("/", response_model=schemas.CandidateDataResponse)
def create_candidate_data(
    *,
    db: Session = Depends(deps.get_db),
    candidate_data_in: schemas.CandidateDataCreate,
) -> Any:
    """
    创建新的KOL候选人数据
    
    参数:
        db: 数据库会话，由依赖注入提供
        candidate_data_in: 候选人数据创建模型
        
    返回:
        创建的候选人数据对象
    """
    # 验证KOL是否存在
    # kol = crud.kol_info.get(db, id=candidate_data_in.kol_id)
    # if not kol:
    #     raise HTTPException(
    #         status_code=status.HTTP_404_NOT_FOUND,
    #         detail="KOL不存在"
    #     )
    
    # 直接创建候选人数据，允许同一个KOL ID存在多条记录
    candidate_data = crud.candidate_data.create(db, obj_in=candidate_data_in)
    return candidate_data


@router.get("/{candidate_id}", response_model=schemas.CandidateDataResponse)
def read_candidate_data(
    *,
    db: Session = Depends(deps.get_db),
    candidate_id: int,
) -> Any:
    """
    通过ID获取单个KOL候选人数据
    
    参数:
        db: 数据库会话，由依赖注入提供
        candidate_id: 候选人数据ID
        
    返回:
        候选人数据对象
    """
    candidate_data = crud.candidate_data.get(db, id=candidate_id)
    if not candidate_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="找不到该候选人数据",
        )
    return candidate_data


@router.get("/by-kol/{kol_id}", response_model=schemas.CandidateDataResponse)
def read_candidate_data_by_kol_id(
    *,
    db: Session = Depends(deps.get_db),
    kol_id: str,
) -> Any:
    """
    通过KOL ID获取KOL候选人数据
    
    参数:
        db: 数据库会话，由依赖注入提供
        kol_id: KOL ID
        
    返回:
        候选人数据对象
    """
    candidate_data = crud.candidate_data.get_by_kol_id(db, kol_id=kol_id)
    if not candidate_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"找不到KOL ID为{kol_id}的候选人数据",
        )
    return candidate_data


@router.get("/by-reply-email/{reply_email}", response_model=schemas.CandidateDataResponse)
def read_candidate_data_by_reply_email(
    *,
    db: Session = Depends(deps.get_db),
    reply_email: str,
) -> Any:
    """
    通过回复邮箱获取KOL候选人数据
    
    参数:
        db: 数据库会话，由依赖注入提供
        reply_email: 回复邮箱
        
    返回:
        候选人数据对象
    """
    candidate_data = crud.candidate_data.get_by_reply_email(db, reply_email=reply_email)
    if not candidate_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"找不到回复邮箱为{reply_email}的候选人数据",
        )
    return candidate_data


@router.put("/{candidate_id}", response_model=schemas.CandidateDataResponse)
def update_candidate_data(
    *,
    db: Session = Depends(deps.get_db),
    candidate_id: int,
    candidate_data_in: schemas.CandidateDataUpdate,
) -> Any:
    """
    更新KOL候选人数据
    
    参数:
        db: 数据库会话，由依赖注入提供
        candidate_id: 候选人数据ID
        candidate_data_in: 更新数据模型
        
    返回:
        更新后的候选人数据对象
    """
    candidate_data = crud.candidate_data.get(db, id=candidate_id)
    if not candidate_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="找不到该候选人数据",
        )
    
    candidate_data = crud.candidate_data.update(db, db_obj=candidate_data, obj_in=candidate_data_in)
    return candidate_data


@router.delete("/{candidate_id}", response_model=schemas.CandidateDataResponse)
def delete_candidate_data(
    *,
    db: Session = Depends(deps.get_db),
    candidate_id: int,
) -> Any:
    """
    删除KOL候选人数据
    
    参数:
        db: 数据库会话，由依赖注入提供
        candidate_id: 候选人数据ID
        
    返回:
        被删除的候选人数据对象
    """
    candidate_data = crud.candidate_data.get(db, id=candidate_id)
    if not candidate_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="找不到该候选人数据",
        )
    
    candidate_data = crud.candidate_data.remove(db, id=candidate_id)
    return candidate_data


@router.delete("/by-thread/{thread_id}", response_model=schemas.CandidateDataResponse)
def delete_candidate_data_by_thread_id(
    *,
    db: Session = Depends(deps.get_db),
    thread_id: str,
) -> Any:
    """
    通过对话线程ID删除KOL候选人数据
    
    参数:
        db: 数据库会话，由依赖注入提供
        thread_id: 对话线程ID
        
    返回:
        被删除的候选人数据对象
    """
    candidate_data = crud.candidate_data.get_by_thread_id(db, thread_id=thread_id)
    if not candidate_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"找不到对话线程ID为{thread_id}的候选人数据",
        )
    
    candidate_data = crud.candidate_data.remove_by_thread_id(db, thread_id=thread_id)
    return candidate_data


@router.delete("/async/by-thread/{thread_id}", response_model=schemas.CandidateDataResponse)
async def delete_candidate_data_by_thread_id_async(
    *,
    db: AsyncSession = Depends(deps.get_async_db),
    thread_id: str,
) -> Any:
    """
    异步通过对话线程ID删除KOL候选人数据
    
    参数:
        db: 异步数据库会话，由依赖注入提供
        thread_id: 对话线程ID
        
    返回:
        被删除的候选人数据对象
    """
    candidate_data = await crud.candidate_data.async_get_by_thread_id(db, thread_id=thread_id)
    if not candidate_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"找不到对话线程ID为{thread_id}的候选人数据",
        )
    
    candidate_data = await crud.candidate_data.async_remove_by_thread_id(db, thread_id=thread_id)
    return candidate_data


@router.put("/by-thread/{thread_id}", response_model=schemas.CandidateDataResponse)
async def update_candidate_data_by_thread_id_async(
    *,
    db: AsyncSession = Depends(deps.get_async_db),
    thread_id: str,
    candidate_data_in: schemas.CandidateDataUpdate,
) -> Any:
    """
    异步通过对话线程ID更新KOL候选人数据
    
    参数:
        db: 异步数据库会话，由依赖注入提供
        thread_id: 对话线程ID
        candidate_data_in: 更新数据模型
        
    返回:
        更新后的候选人数据对象
    """
    candidate_data = await crud.candidate_data.async_get_by_thread_id(db, thread_id=thread_id)
    if not candidate_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"找不到对话线程ID为{thread_id}的候选人数据",
        )
    
    candidate_data = await crud.candidate_data.async_update_by_thread_id(db, thread_id=thread_id, obj_in=candidate_data_in)
    return candidate_data


@router.post("/advanced-search", response_model=CandidateDataAggregatedPaginatedResponse)
def advanced_search_candidate_datas(
    *,
    db: Session = Depends(deps.get_db),
    search_params: CandidateDataAdvancedSearchRequest,
) -> Any:
    """
    高级搜索KOL候选人数据（聚合版本）
    
    参数:
        db: 数据库会话，由依赖注入提供
        search_params: 高级搜索参数
        
    返回:
        符合搜索条件的按kol_id聚合的KOL候选人数据列表
    """
    page = search_params.skip // search_params.limit + 1 if search_params.limit > 0 else 1
    
    # 转换搜索条件为字典列表
    conditions = [
        {"field": condition.field, "operator": condition.operator, "value": condition.value}
        for condition in search_params.conditions
    ]
    
    # 先获取原始搜索结果
    candidate_datas, _ = crud.candidate_data.advanced_search(
        db,
        conditions=conditions,
        conjunction=search_params.conjunction,
        project=search_params.project,
        label=search_params.label,
        skip=0,  # 获取所有结果用于聚合
        limit=10000,  # 设置一个较大的限制
    )
    
    # 对结果进行聚合处理
    aggregated_data = {}
    for item in candidate_datas:
        kol_id = item.kol_id
        if kol_id not in aggregated_data:
            aggregated_data[kol_id] = {
                'kol_id': kol_id,
                'kol_name': item.kol_name,
                'first_contact_dates': [],
                'last_contact_dates': [],
                'follow_ups': [],
                'labels': [],
                'sublabels': [],
                'thread_ids': [],
                'projects': [],
                'reply_emails': [],
                'ids': [],
                'created_ats': [],
                'updated_ats': [],
                'record_count': 0
            }
        
        agg_item = aggregated_data[kol_id]
        
        # 聚合不同字段到数组，去除重复值
        if item.first_contact_date not in agg_item['first_contact_dates']:
            agg_item['first_contact_dates'].append(item.first_contact_date)
        if item.last_contact_date not in agg_item['last_contact_dates']:
            agg_item['last_contact_dates'].append(item.last_contact_date)
        if item.follow_up not in agg_item['follow_ups']:
            agg_item['follow_ups'].append(item.follow_up)
        if item.label not in agg_item['labels']:
            agg_item['labels'].append(item.label)
        if item.sublabel not in agg_item['sublabels']:
            agg_item['sublabels'].append(item.sublabel)
        if item.thread_id not in agg_item['thread_ids']:
            agg_item['thread_ids'].append(item.thread_id)
        if item.project not in agg_item['projects']:
            agg_item['projects'].append(item.project)
        if item.reply_email not in agg_item['reply_emails']:
            agg_item['reply_emails'].append(item.reply_email)
        
        # ID和时间戳保留所有值
        agg_item['ids'].append(item.id)
        agg_item['created_ats'].append(item.created_at)
        agg_item['updated_ats'].append(item.updated_at)
        agg_item['record_count'] += 1
    
    # 转换为列表并排序
    aggregated_list = list(aggregated_data.values())
    for item in aggregated_list:
        # 对日期数组先过滤None值再排序
        item['first_contact_dates'] = [x for x in item['first_contact_dates'] if x is not None]
        item['last_contact_dates'] = [x for x in item['last_contact_dates'] if x is not None]
        item['created_ats'] = [x for x in item['created_ats'] if x is not None]
        item['updated_ats'] = [x for x in item['updated_ats'] if x is not None]
        
        # 排序日期数组
        if item['first_contact_dates']:
            item['first_contact_dates'].sort(reverse=True)
        if item['last_contact_dates']:
            item['last_contact_dates'].sort(reverse=True)
        if item['created_ats']:
            item['created_ats'].sort(reverse=True)
        if item['updated_ats']:
            item['updated_ats'].sort(reverse=True)
        
        # 对其他数组去除None值
        item['follow_ups'] = [x for x in item['follow_ups'] if x is not None]
        item['labels'] = [x for x in item['labels'] if x is not None]
        item['sublabels'] = [x for x in item['sublabels'] if x is not None]
        item['thread_ids'] = [x for x in item['thread_ids'] if x is not None]
        item['projects'] = [x for x in item['projects'] if x is not None]
        item['reply_emails'] = [x for x in item['reply_emails'] if x is not None]
    
    # 按最新创建时间排序
    aggregated_list.sort(key=lambda x: max(x['created_ats']) if x['created_ats'] else datetime.min, reverse=True)
    
    # 应用分页
    total = len(aggregated_list)
    paginated_items = aggregated_list[search_params.skip:search_params.skip + search_params.limit]
    
    # 转换为响应模型
    items = []
    for data in paginated_items:
        item = CandidateDataAggregated(
            kol_id=data['kol_id'],
            kol_name=data['kol_name'],
            first_contact_dates=data['first_contact_dates'],
            last_contact_dates=data['last_contact_dates'],
            follow_ups=data['follow_ups'],
            labels=data['labels'],
            sublabels=data['sublabels'],
            thread_ids=data['thread_ids'],
            projects=data['projects'],
            reply_emails=data['reply_emails'],
            ids=data['ids'],
            created_ats=data['created_ats'],
            updated_ats=data['updated_ats'],
            record_count=data['record_count']
        )
        items.append(item)
    
    pages = math.ceil(total / search_params.limit) if search_params.limit > 0 else 1
    
    return {
        "items": items,
        "total": total,
        "page": page,
        "size": search_params.limit,
        "pages": pages
    }


@router.post("/raw/advanced-search", response_model=CandidateDataPaginatedResponse)
def raw_advanced_search_candidate_datas(
    *,
    db: Session = Depends(deps.get_db),
    search_params: CandidateDataAdvancedSearchRequest,
) -> Any:
    """
    高级搜索KOL候选人数据（不聚合版本）
    
    参数:
        db: 数据库会话，由依赖注入提供
        search_params: 高级搜索参数
        
    返回:
        符合搜索条件的不聚合的KOL候选人数据列表，每条记录保持原始状态
    """
    page = search_params.skip // search_params.limit + 1 if search_params.limit > 0 else 1
    
    # 转换搜索条件为字典列表
    conditions = [
        {"field": condition.field, "operator": condition.operator, "value": condition.value}
        for condition in search_params.conditions
    ]
    
    # 直接获取原始搜索结果并应用分页
    candidate_datas, total = crud.candidate_data.advanced_search(
        db,
        conditions=conditions,
        conjunction=search_params.conjunction,
        project=search_params.project,
        label=search_params.label,
        skip=search_params.skip,
        limit=search_params.limit,
    )
    
    pages = math.ceil(total / search_params.limit) if search_params.limit > 0 else 1
    
    return {
        "items": candidate_datas,
        "total": total,
        "page": page,
        "size": search_params.limit,
        "pages": pages
    }


@router.post("/async/advanced-search", response_model=CandidateDataAggregatedPaginatedResponse)
async def advanced_search_candidate_datas_async(
    *,
    db: AsyncSession = Depends(deps.get_async_db),
    search_params: CandidateDataAdvancedSearchRequest,
) -> Any:
    """
    异步高级搜索KOL候选人数据（聚合版本）
    
    参数:
        db: 数据库会话，由依赖注入提供
        search_params: 高级搜索参数
        
    返回:
        符合搜索条件的按kol_id聚合的KOL候选人数据列表
    """
    page = search_params.skip // search_params.limit + 1 if search_params.limit > 0 else 1
    
    # 转换搜索条件为字典列表
    conditions = [
        {"field": condition.field, "operator": condition.operator, "value": condition.value}
        for condition in search_params.conditions
    ]
    
    # 先获取原始搜索结果
    candidate_datas, _ = await crud.candidate_data.async_advanced_search(
        db,
        conditions=conditions,
        conjunction=search_params.conjunction,
        project=search_params.project,
        label=search_params.label,
        skip=0,  # 获取所有结果用于聚合
        limit=10000,  # 设置一个较大的限制
    )
    
    # 对结果进行聚合处理
    aggregated_data = {}
    for item in candidate_datas:
        kol_id = item.kol_id
        if kol_id not in aggregated_data:
            aggregated_data[kol_id] = {
                'kol_id': kol_id,
                'kol_name': item.kol_name,
                'first_contact_dates': [],
                'last_contact_dates': [],
                'follow_ups': [],
                'labels': [],
                'sublabels': [],
                'thread_ids': [],
                'projects': [],
                'reply_emails': [],
                'ids': [],
                'created_ats': [],
                'updated_ats': [],
                'record_count': 0
            }
        
        agg_item = aggregated_data[kol_id]
        
        # 聚合不同字段到数组，去除重复值
        if item.first_contact_date not in agg_item['first_contact_dates']:
            agg_item['first_contact_dates'].append(item.first_contact_date)
        if item.last_contact_date not in agg_item['last_contact_dates']:
            agg_item['last_contact_dates'].append(item.last_contact_date)
        if item.follow_up not in agg_item['follow_ups']:
            agg_item['follow_ups'].append(item.follow_up)
        if item.label not in agg_item['labels']:
            agg_item['labels'].append(item.label)
        if item.sublabel not in agg_item['sublabels']:
            agg_item['sublabels'].append(item.sublabel)
        if item.thread_id not in agg_item['thread_ids']:
            agg_item['thread_ids'].append(item.thread_id)
        if item.project not in agg_item['projects']:
            agg_item['projects'].append(item.project)
        if item.reply_email not in agg_item['reply_emails']:
            agg_item['reply_emails'].append(item.reply_email)
        
        # ID和时间戳保留所有值
        agg_item['ids'].append(item.id)
        agg_item['created_ats'].append(item.created_at)
        agg_item['updated_ats'].append(item.updated_at)
        agg_item['record_count'] += 1
    
    # 转换为列表并排序
    aggregated_list = list(aggregated_data.values())
    for item in aggregated_list:
        # 对日期数组排序
        item['first_contact_dates'].sort(reverse=True)
        item['last_contact_dates'].sort(reverse=True)
        item['created_ats'].sort(reverse=True)
        item['updated_ats'].sort(reverse=True)
        
        # 对其他数组去除None值
        item['follow_ups'] = [x for x in item['follow_ups'] if x is not None]
        item['labels'] = [x for x in item['labels'] if x is not None]
        item['sublabels'] = [x for x in item['sublabels'] if x is not None]
        item['thread_ids'] = [x for x in item['thread_ids'] if x is not None]
        item['projects'] = [x for x in item['projects'] if x is not None]
        item['reply_emails'] = [x for x in item['reply_emails'] if x is not None]
    
    # 按最新创建时间排序
    aggregated_list.sort(key=lambda x: max(x['created_ats']), reverse=True)
    
    # 应用分页
    total = len(aggregated_list)
    paginated_items = aggregated_list[search_params.skip:search_params.skip + search_params.limit]
    
    # 转换为响应模型
    items = []
    for data in paginated_items:
        item = CandidateDataAggregated(
            kol_id=data['kol_id'],
            kol_name=data['kol_name'],
            first_contact_dates=data['first_contact_dates'],
            last_contact_dates=data['last_contact_dates'],
            follow_ups=data['follow_ups'],
            labels=data['labels'],
            sublabels=data['sublabels'],
            thread_ids=data['thread_ids'],
            projects=data['projects'],
            reply_emails=data['reply_emails'],
            ids=data['ids'],
            created_ats=data['created_ats'],
            updated_ats=data['updated_ats'],
            record_count=data['record_count']
        )
        items.append(item)
    
    pages = math.ceil(total / search_params.limit) if search_params.limit > 0 else 1
    
    return {
        "items": items,
        "total": total,
        "page": page,
        "size": search_params.limit,
        "pages": pages
    }


@router.post("/async/raw/advanced-search", response_model=CandidateDataPaginatedResponse)
async def raw_advanced_search_candidate_datas_async(
    *,
    db: AsyncSession = Depends(deps.get_async_db),
    search_params: CandidateDataAdvancedSearchRequest,
) -> Any:
    """
    异步高级搜索KOL候选人数据（不聚合版本）
    
    参数:
        db: 数据库会话，由依赖注入提供
        search_params: 高级搜索参数
        
    返回:
        符合搜索条件的不聚合的KOL候选人数据列表，每条记录保持原始状态
    """
    page = search_params.skip // search_params.limit + 1 if search_params.limit > 0 else 1
    
    # 转换搜索条件为字典列表
    conditions = [
        {"field": condition.field, "operator": condition.operator, "value": condition.value}
        for condition in search_params.conditions
    ]
    
    # 直接获取原始搜索结果并应用分页
    candidate_datas, total = await crud.candidate_data.async_advanced_search(
        db,
        conditions=conditions,
        conjunction=search_params.conjunction,
        project=search_params.project,
        label=search_params.label,
        skip=search_params.skip,
        limit=search_params.limit,
    )
    
    pages = math.ceil(total / search_params.limit) if search_params.limit > 0 else 1
    
    return {
        "items": candidate_datas,
        "total": total,
        "page": page,
        "size": search_params.limit,
        "pages": pages
    }


@router.get("/async/raw", response_model=CandidateDataPaginatedResponse)
async def read_raw_candidate_datas_async(
    *,
    db: AsyncSession = Depends(deps.get_async_db),
    skip: int = 0,
    limit: int = 100,
    project: Optional[str] = None,
    label: Optional[str] = None,
    reply_email: Optional[str] = None
) -> Any:
    """
    异步获取不聚合的KOL候选人数据列表
    
    参数:
        db: 异步数据库会话，由依赖注入提供
        skip: 分页偏移量，默认为0
        limit: 每页数据量，默认为100
        project: 可选，按项目筛选
        label: 可选，按标签筛选
        reply_email: 可选，按回复邮箱筛选
        
    返回:
        不聚合的分页KOL候选人数据列表，每条记录保持原始状态
    """
    page = skip // limit + 1 if limit > 0 else 1
    
    # 构建查询
    query = select(crud.candidate_data.model)
    
    # 应用筛选条件
    if project:
        query = query.where(crud.candidate_data.model.project == project)
    if label:
        query = query.where(crud.candidate_data.model.label == label)
    if reply_email:
        query = query.where(crud.candidate_data.model.reply_email == reply_email)
    
    # 获取总数
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await db.execute(count_query)
    total = total_result.scalar_one() or 0
    
    # 应用分页和排序
    result = await db.execute(
        query.order_by(crud.candidate_data.model.created_at.desc())
        .offset(skip).limit(limit)
    )
    items = result.scalars().all()
    
    pages = math.ceil(total / limit) if limit > 0 else 1
    
    return {
        "items": items,
        "total": total,
        "page": page,
        "size": limit,
        "pages": pages
    }