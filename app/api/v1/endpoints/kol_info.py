from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, schemas
from app.api import deps
from app.schemas.kol_info import KOLInfoPaginatedResponse, HashTag, KOLInfoAdvancedSearchRequest
import math


def process_hashtags(kol_info_list: List[Any]) -> List[Any]:
    """
    处理KOL信息中的哈希标签字段, 确保格式一致
    
    参数:
        kol_info_list: KOL信息列表
        
    返回:
        处理后的KOL信息列表
        
    说明:
        1. 检查platform是否为TIKTOK
        2. 对TIKTOK平台的most_used_hashtags进行标准化处理
        3. 将复杂对象类型的hashtag转换为字符串
    """
    for item in kol_info_list:
        # 只处理TikTok平台的数据
        if getattr(item, "platform", None) == "tiktok":
            hashtags = getattr(item, "most_used_hashtags", None)
            if hashtags:
                # 如果是复杂对象的列表, 提取text字段
                processed_hashtags = []
                for tag in hashtags:
                    if isinstance(tag, dict) and "text" in tag:
                        processed_hashtags.append(tag["text"])
                    elif isinstance(tag, HashTag):
                        processed_hashtags.append(tag.text)
                    elif isinstance(tag, str):
                        processed_hashtags.append(tag)
                    else:
                        # 其他情况尝试转换为字符串
                        try:
                            processed_hashtags.append(str(tag))
                        except:
                            pass
                setattr(item, "most_used_hashtags", processed_hashtags)
    return kol_info_list


def attach_filter_names(kol_infos):
    from app import crud
    from app.api.deps import get_db
    
    # 获取数据库会话
    db_gen = get_db()
    db = next(db_gen)
    
    try:
        for kol in kol_infos:
            if hasattr(kol, "filter_datas") and kol.filter_datas:
                kol.filter_names = [fd.filter_name for fd in kol.filter_datas if getattr(fd, 'filter_name', None)]
                # 直接从关联表中获取所有project_codes
                associations = crud.filter_kol_association.get_multi_by_kol_id(db, kol_id=kol.kol_id)
                kol.project_codes = [assoc.project_code for assoc in associations if assoc.project_code]
            else:
                kol.filter_names = []
                kol.project_codes = []
    finally:
        db.close()
    
    return kol_infos


def attach_tag_names(kol_infos):
    """
    将标签信息附加到KOL对象中
    
    参数:
        kol_infos: KOL信息列表或单个KOL对象
        
    返回:
        处理后的KOL信息列表或对象
    """
    for kol in kol_infos:
        if hasattr(kol, "tags") and kol.tags:
            # 使用tag_names作为属性名, 避免与SQLAlchemy关系属性冲突
            setattr(kol, "tag_names", [tag.name for tag in kol.tags if getattr(tag, 'name', None)])
        else:
            setattr(kol, "tag_names", [])
    return kol_infos


router = APIRouter()


@router.get("/", response_model=KOLInfoPaginatedResponse)
def read_kol_infos(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    platform: Optional[str] = None,
    filter_name: Optional[str] = None,
    project_code: Optional[str] = None,
) -> Any:
    """
    获取 kol_info 列表
    
    参数:
        db: 数据库会话, 由依赖注入提供
        skip: 分页偏移量, 默认为0
        limit: 每页数据量, 默认为100
        platform: 可选, 按平台筛选
        filter_name: 可选, 按筛选条件名称筛选
        project_code: 可选, 按项目代码筛选
        
    返回:
        分页的KOL信息列表, 包含总数量、当前页码等
        
    性能说明:
        1. 优先级顺序为: platform > filter_name > project_code
        2. 使用filter_name查询时, 先查找对应的filter_id, 再关联KOL表
        3. 查询使用了子查询和distinct优化, 避免重复数据
    """
    page = skip // limit + 1 if limit > 0 else 1
    
    # 处理 filter_name 参数可能包含前缀的情况
    if filter_name and filter_name.startswith("filter_name="):
        filter_name = filter_name.replace("filter_name=", "", 1)
    
    if platform:
        # 按平台筛选, 直接查询KOL表
        kol_infos, total = crud.kol_info.get_multi_by_platform_with_pagination(
            db, platform=platform, skip=skip, limit=limit
        )
    elif filter_name:
        # 按筛选条件名称筛选, 需要关联筛选条件表和KOL表
        kol_infos, total = crud.kol_info.get_multi_by_filter_name_with_pagination(
            db, filter_name=filter_name, skip=skip, limit=limit
        )
    elif project_code:
        # 按项目代码筛选, 需要关联项目和KOL表
        kol_infos, total = crud.kol_info.get_multi_by_project_code_with_pagination(
            db, project_code=project_code, skip=skip, limit=limit
        )
    else:
        # 不带筛选条件, 返回所有KOL
        kol_infos, total = crud.kol_info.get_with_pagination(
            db, skip=skip, limit=limit
        )
    
    # 处理哈希标签格式
    kol_infos = process_hashtags(kol_infos)
    kol_infos = attach_filter_names(kol_infos)
    kol_infos = attach_tag_names(kol_infos)
    
    pages = math.ceil(total / limit) if limit > 0 else 1
    
    return {
        "items": kol_infos,
        "total": total,
        "page": page,
        "size": limit,
        "pages": pages
    }


@router.post("/", response_model=schemas.KOLInfoResponse)
def create_kol_info(
    *,
    db: Session = Depends(deps.get_db),
    kol_info_in: schemas.KOLInfoCreateExtended,
) -> Any:
    """
    创建新的 kol_info 数据或为已存在的KOL创建项目关联
    
    参数:
        db: 数据库会话, 由依赖注入提供
        kol_info_in: KOL信息创建模型, 包含基本信息, 标签名称列表和筛选条件详情
        
    返回:
        创建的或已存在的KOL信息对象
        
    说明:
        1. 如果KOL ID不存在, 会创建新的KOL数据
        2. 如果KOL ID已存在且未与指定filter+project组合关联, 只创建关联关系
        3. 如果KOL ID已存在且已与指定filter+project组合关联, 返回400错误
        4. 同一KOL可以关联多个不同的filter，或同一filter的不同project
        5. 如果提供了 filter_details, 会自动创建与筛选条件的关联
        6. 如果 filter_name 不存在, 会创建新的筛选条件
        7. 如果提供了 tag_names, 会自动创建与标签的关联(避免重复)
        8. 如果任何一个 tag_name 不存在, 会返回404错误，整个操作会回滚
    
    性能说明:
        使用了精确查询筛选条件和标签名称, 确保关联正确
        使用数据库事务确保数据一致性
    """
    # 检查KOL是否已存在
    existing_kol = crud.kol_info.get(db, id=kol_info_in.kol_id)
    
    # 如果KOL已存在，检查filter与project的具体关联关系
    if existing_kol and kol_info_in.filter_details:
        filter_name = kol_info_in.filter_details.filter_name
        project_code = kol_info_in.filter_details.project_code
        
        # 检查是否已与相同filter和project的组合关联
        filter_data = crud.filter_data.get_by_name(db, name=filter_name)
        if filter_data:
            existing_association = crud.filter_kol_association.get_by_filter_kol_project(
                db, 
                filter_id=filter_data.id, 
                kol_id=kol_info_in.kol_id,
                project_code=project_code
            )
            if existing_association:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"KOL已与此筛选条件和项目关联, KOL ID: {kol_info_in.kol_id}, 筛选条件: {filter_name}, 项目: {project_code}",
                )
        # 如果KOL存在但未与该filter+project组合关联，只创建关联关系
        kol = existing_kol
        create_kol_data = False
    else:
        create_kol_data = True
    
    # 预先验证所有标签是否存在（一次性检查，避免重复查询）
    validated_tags = {}
    if kol_info_in.tag_names and len(kol_info_in.tag_names) > 0:
        for tag_name in kol_info_in.tag_names:
            tag = crud.tag.get_by_name(db, name=tag_name)
            if not tag:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"标签不存在, 名称: {tag_name}",
                )
            validated_tags[tag_name] = tag
    
    # 使用数据库事务确保数据一致性
    try:
        # 如果需要创建新的KOL数据
        if create_kol_data:
            # 提取基本的KOL信息，不包括tag_names和filter_details
            kol_info_dict = {k: v for k, v in kol_info_in.model_dump().items() 
                   if k not in ["tag_names", "filter_details"]}
            
            # 特殊处理 email 字段，确保空字符串转为 None
            if "email" in kol_info_dict and kol_info_dict["email"] == "":
                kol_info_dict["email"] = None
                
            kol_info_base = schemas.KOLInfoCreate(**kol_info_dict)
            
            # 创建KOL信息（不自动提交）
            kol = crud.kol_info.create_without_commit(db, obj_in=kol_info_base)
        # 如果KOL已存在，使用现有的KOL对象（已在上面赋值）
        
        # 处理筛选条件详情
        if kol_info_in.filter_details:
            filter_details = kol_info_in.filter_details
            # 通过筛选条件名称查找
            filter_data = crud.filter_data.get_by_name(db, name=filter_details.filter_name)
            
            # 如果筛选条件不存在，创建新的筛选条件
            if not filter_data:
                filter_create = schemas.FilterDataCreate(
                    language=filter_details.language,
                    gender=filter_details.gender,
                    location=filter_details.location,
                    filter_body=filter_details.filter_body,
                    filter_name=filter_details.filter_name,
                    project_code=filter_details.project_code
                )
                filter_data = crud.filter_data.create_without_commit(db, obj_in=filter_create)
            
            # 创建KOL与filter的关联
            association_in = schemas.FilterKolAssociationCreate(
                filter_id=filter_data.id,
                kol_id=kol.kol_id,
                project_code=filter_details.project_code
            )
            crud.filter_kol_association.create_without_commit(db, obj_in=association_in)
        
        # 处理标签列表（使用已验证的标签）
        if validated_tags:
            project_code = kol_info_in.filter_details.project_code if kol_info_in.filter_details else None
            for tag_name, tag in validated_tags.items():
                # 检查标签关联是否已存在
                existing_tag_association = crud.kol_tag_association.get_by_kol_and_tag(
                    db, kol_id=kol.kol_id, tag_id=tag.id
                )
                if not existing_tag_association:
                    association_in = schemas.KolTagAssociationCreate(
                        tag_id=tag.id,
                        kol_id=kol.kol_id,
                        project_code=project_code
                    )
                    crud.kol_tag_association.create_without_commit(db, obj_in=association_in)
        
        # 提交事务
        db.commit()
        
        # 刷新对象以获取最新关联数据
        db.refresh(kol)
        
        # 处理哈希标签格式
        kol = process_hashtags([kol])[0]
        kol = attach_filter_names([kol])[0]
        kol = attach_tag_names([kol])[0]
        
        return kol
        
    except HTTPException:
        # HTTPException 直接重新抛出，回滚事务
        db.rollback()
        raise
    except Exception as e:
        # 其他异常回滚事务
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建KOL信息失败: {str(e)}"
        )


@router.get("/{kol_id}", response_model=schemas.KOLInfoResponse)
def read_kol_info(
    *,
    db: Session = Depends(deps.get_db),
    kol_id: str,
) -> Any:
    """
    通过ID获取KOL信息
    """
    kol = crud.kol_info.get(db, id=kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在",
        )
    
    # 处理哈希标签格式
    kol = process_hashtags([kol])[0]
    kol = attach_filter_names([kol])[0]
    kol = attach_tag_names([kol])[0]
    
    return kol


@router.put("/{kol_id}", response_model=schemas.KOLInfoResponse)
def update_kol_info(
    *,
    db: Session = Depends(deps.get_db),
    kol_id: str,
    kol_info_in: schemas.KOLInfoUpdate,
) -> Any:
    """
    更新KOL信息
    """
    kol = crud.kol_info.get(db, id=kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在",
        )
    
    # 特殊处理 email 字段，确保空字符串转为 None
    update_data = kol_info_in.model_dump(exclude_unset=True)
    if "email" in update_data and update_data["email"] == "":
        update_data["email"] = None
        kol_info_in = schemas.KOLInfoUpdate(**update_data)
    
    kol = crud.kol_info.update(db, db_obj=kol, obj_in=kol_info_in)
    
    # 处理哈希标签格式
    kol = process_hashtags([kol])[0]
    kol = attach_filter_names([kol])[0]
    kol = attach_tag_names([kol])[0]
    
    return kol


@router.delete("/{kol_id}", response_model=schemas.KOLInfoResponse)
def delete_kol_info(
    *,
    db: Session = Depends(deps.get_db),
    kol_id: str,
) -> Any:
    """
    删除KOL信息及其所有关联关系
    
    说明:
        1. 删除KOL与所有filter的关联关系
        2. 删除KOL与所有tag的关联关系  
        3. 删除KOL与所有视频的关联关系
        4. 最后删除KOL主记录
        
    注意:
        这是级联删除操作，会清理所有相关的关联数据
        使用事务确保数据一致性
    """
    # 使用数据库级联删除，简化删除逻辑
    try:
        # 首先获取KOL对象用于返回，同时验证其存在性
        kol = crud.kol_info.get(db, id=kol_id)
        if not kol:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="KOL不存在",
            )
        
        # 在删除前备份KOL数据，避免返回已删除对象
        # 处理哈希标签格式和关联信息
        kol_for_response = process_hashtags([kol])[0]
        kol_for_response = attach_filter_names([kol_for_response])[0]
        kol_for_response = attach_tag_names([kol_for_response])[0]
        
        # 从Session中expunge对象，使其脱离SQLAlchemy管理
        # 这样删除操作就不会影响这个对象
        db.expunge(kol_for_response)
        
        # 删除KOL主记录，数据库级联删除会自动处理所有关联表
        # 1. filter_kol_association: kol_id 有 ondelete="CASCADE"
        # 2. kol_tag_association: kol_id 有 ondelete="CASCADE"  
        # 3. video_info: kol_id 有 ondelete="CASCADE" + SQLAlchemy关系级联
        crud.kol_info.remove_without_commit(db, id=kol_id)
        
        # 提交事务
        db.commit()
        
        # 返回脱离SQLAlchemy管理的对象，不会受删除影响
        return kol_for_response
        
    except HTTPException:
        # HTTPException 直接重新抛出
        db.rollback()
        raise
    except Exception as e:
        # 回滚事务
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除KOL信息失败: {str(e)}"
        )


@router.get("/by-email/{email}", response_model=schemas.KOLInfoResponse)
def read_kol_info_by_email(
    *,
    db: Session = Depends(deps.get_db),
    email: str,
) -> Any:
    """
    通过邮箱获取KOL信息
    """
    kol = crud.kol_info.get_by_email(db, email=email)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到使用此邮箱的KOL",
        )
    
    # 处理哈希标签格式
    kol = process_hashtags([kol])[0]
    kol = attach_filter_names([kol])[0]
    kol = attach_tag_names([kol])[0]
    
    return kol


@router.post("/{kol_id}/filters/{filter_id}", response_model=schemas.FilterKolAssociationResponse)
def associate_kol_with_filter(
    *,
    db: Session = Depends(deps.get_db),
    kol_id: str,
    filter_id: int,
    project_code: Optional[str] = None,
) -> Any:
    """
    将KOL与筛选条件关联起来
    """
    # 检查KOL是否存在
    kol = crud.kol_info.get(db, id=kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在",
        )
    
    # 检查筛选条件是否存在
    filter_data = crud.filter_data.get(db, id=filter_id)
    if not filter_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="筛选条件不存在",
        )
    
    # 检查关联是否已存在
    existing = crud.filter_kol_association.get_by_filter_id_and_kol_id(
        db, filter_id=filter_id, kol_id=kol_id
    )
    if existing:
        # 如果关联已存在但项目代码不同, 则更新项目代码
        if project_code and existing.project_code != project_code:
            association_update = schemas.FilterKolAssociationUpdate(project_code=project_code)
            return crud.filter_kol_association.update(db, db_obj=existing, obj_in=association_update)
        return existing
    
    # 创建新关联
    association_in = schemas.FilterKolAssociationCreate(
        filter_id=filter_id,
        kol_id=kol_id,
        project_code=project_code
    )
    return crud.filter_kol_association.create(db, obj_in=association_in)


@router.post("/{kol_id}/filter-by-name/{filter_name}", response_model=schemas.FilterKolAssociationResponse)
def associate_kol_with_filter_by_name(
    *,
    db: Session = Depends(deps.get_db),
    kol_id: str,
    filter_name: str,
    project_code: Optional[str] = None,
) -> Any:
    """
    将KOL与筛选条件关联（通过筛选条件名称）
    
    参数:
        db: 数据库会话, 由依赖注入提供
        kol_id: KOL ID
        filter_name: 筛选条件名称
        project_code: 可选, 项目代码
        
    返回:
        关联对象
        
    说明:
        1. 如果筛选条件名称不存在, 会返回404错误
        2. 如果KOL不存在, 会返回404错误
        3. 如果关联已存在, 会返回现有关联
    """
    # 处理 filter_name 参数可能包含前缀的情况
    if filter_name.startswith("filter_name="):
        filter_name = filter_name.replace("filter_name=", "", 1)
    
    # 检查KOL是否存在
    kol = crud.kol_info.get(db, id=kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在",
        )
    
    # 通过名称查找筛选条件
    filter_data = crud.filter_data.get_by_name(db, name=filter_name)
    if not filter_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"筛选条件不存在, 名称: {filter_name}",
        )
    
    # 检查关联是否已存在
    existing = crud.filter_kol_association.get_by_filter_id_and_kol_id(
        db, filter_id=filter_data.id, kol_id=kol_id
    )
    if existing:
        # 如果关联已存在但项目代码不同, 则更新项目代码
        if project_code and existing.project_code != project_code:
            association_update = schemas.FilterKolAssociationUpdate(project_code=project_code)
            return crud.filter_kol_association.update(db, db_obj=existing, obj_in=association_update)
        return existing
    
    # 创建新关联
    association_in = schemas.FilterKolAssociationCreate(
        filter_id=filter_data.id,
        kol_id=kol_id,
        project_code=project_code
    )
    return crud.filter_kol_association.create(db, obj_in=association_in)


@router.delete("/{kol_id}/filters/{filter_id}", response_model=schemas.FilterKolAssociationResponse)
def remove_kol_filter_association(
    *,
    db: Session = Depends(deps.get_db),
    kol_id: str,
    filter_id: int,
) -> Any:
    """
    移除KOL与筛选条件的关联
    """
    # 检查关联是否存在
    association = crud.filter_kol_association.get_by_filter_id_and_kol_id(
        db, filter_id=filter_id, kol_id=kol_id
    )
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关联不存在",
        )
    
    return crud.filter_kol_association.remove(db, id=association.id)


@router.delete("/{kol_id}/filter-by-name/{filter_name}", response_model=schemas.FilterKolAssociationResponse)
def remove_kol_filter_association_by_name(
    *,
    db: Session = Depends(deps.get_db),
    kol_id: str,
    filter_name: str,
) -> Any:
    """
    通过筛选条件名称删除KOL与筛选条件的关联
    """
    # 处理 filter_name 参数可能包含前缀的情况
    if filter_name.startswith("filter_name="):
        filter_name = filter_name.replace("filter_name=", "", 1)
    
    # 检查KOL是否存在
    kol = crud.kol_info.get(db, id=kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="KOL不存在",
        )
    
    # 通过名称查找筛选条件
    filter_data = crud.filter_data.get_by_name(db, name=filter_name)
    if not filter_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"筛选条件不存在, 名称: {filter_name}",
        )
    
    # 检查关联是否存在
    association = crud.filter_kol_association.get_by_filter_id_and_kol_id(
        db, filter_id=filter_data.id, kol_id=kol_id
    )
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关联不存在",
        )
    
    return crud.filter_kol_association.remove(db, id=association.id)


@router.post("/advanced-search", response_model=KOLInfoPaginatedResponse)
def advanced_search_kol_infos(
    *,
    db: Session = Depends(deps.get_db),
    search_params: KOLInfoAdvancedSearchRequest,
) -> Any:
    """
    kol_info 高级搜索
    支持对每个字段进行多条件、多值的复杂查询:
    - 等于 (eq): 精确匹配
    - 不等于 (ne): 不等于指定值
    - 包含 (contains): 字符串包含、数组包含
    - 不包含 (not_contains): 字符串不包含、数组不包含
    - 为空 (is_null): 字段值为空
    - 不为空 (is_not_null): 字段值不为空
    同时支持与platform、project_code筛选条件组合
    """
    conditions = [
        {
            "field": condition.field,
            "operator": condition.operator,
            "value": condition.value
        }
        for condition in search_params.conditions
    ]
    kol_infos, total = crud.kol_info.advanced_search(
        db,
        conditions=conditions,
        platform=search_params.platform,
        project_code=search_params.project_code,
        skip=search_params.skip,
        limit=search_params.limit,
        conjunction=search_params.conjunction
    )
    kol_infos = process_hashtags(kol_infos)
    kol_infos = attach_filter_names(kol_infos)
    kol_infos = attach_tag_names(kol_infos)
    page = search_params.skip // search_params.limit + 1 if search_params.limit > 0 else 1
    pages = math.ceil(total / search_params.limit) if search_params.limit > 0 else 1
    return {
        "items": kol_infos,
        "total": total,
        "page": page,
        "size": search_params.limit,
        "pages": pages
    }


@router.post("/async/advanced-search", response_model=KOLInfoPaginatedResponse)
async def advanced_search_kol_infos_async(
    *,
    db: AsyncSession = Depends(deps.get_async_db),
    search_params: KOLInfoAdvancedSearchRequest,
) -> Any:
    """
    KOL信息异步高级搜索
    支持对每个字段进行多条件、多值的复杂查询, 使用异步方式处理:
    - 等于 (eq): 精确匹配
    - 不等于 (ne): 不等于指定值
    - 包含 (contains): 字符串包含、数组包含
    - 不包含 (not_contains): 字符串不包含、数组不包含
    - 为空 (is_null): 字段值为空
    - 不为空 (is_not_null): 字段值不为空
    同时支持与platform、project_code筛选条件组合
    """
    conditions = [
        {
            "field": condition.field,
            "operator": condition.operator,
            "value": condition.value
        }
        for condition in search_params.conditions
    ]
    kol_infos, total = await crud.kol_info.async_advanced_search(
        db,
        conditions=conditions,
        platform=search_params.platform,
        project_code=search_params.project_code,
        skip=search_params.skip,
        limit=search_params.limit,
        conjunction=search_params.conjunction
    )
    kol_infos = process_hashtags(kol_infos)
    kol_infos = attach_filter_names(kol_infos)
    kol_infos = attach_tag_names(kol_infos)
    page = search_params.skip // search_params.limit + 1 if search_params.limit > 0 else 1
    pages = math.ceil(total / search_params.limit) if search_params.limit > 0 else 1
    return {
        "items": kol_infos,
        "total": total,
        "page": page,
        "size": search_params.limit,
        "pages": pages
    }