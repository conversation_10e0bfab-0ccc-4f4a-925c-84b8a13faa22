from typing import Any, List, Optional
import math

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app import crud, schemas
from app.db.session import get_db

router = APIRouter()


@router.get("/", response_model=schemas.SendDataPaginatedResponse)
def read_send_data(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
    platform: Optional[str] = Query(None, description="平台筛选"),
    kol_id: Optional[str] = Query(None, description="KOL ID筛选"),
    send_status: Optional[str] = Query(None, description="发送状态筛选"),
) -> Any:
    """
    获取 send_data 数据列表（分页）
    """
    skip = (page - 1) * size
    
    # 获取总数和数据
    if kol_id:
        total = crud.send_data.count_by_kol_id(db, kol_id=kol_id)
        send_data = crud.send_data.get_multi_by_kol_id(
            db, kol_id=kol_id, skip=skip, limit=size
        )
    elif platform:
        total = crud.send_data.count_by_platform(db, platform=platform)
        send_data = crud.send_data.get_multi_by_platform(
            db, platform=platform, skip=skip, limit=size
        )
    elif send_status:
        total = crud.send_data.count_by_status(db, send_status=send_status)
        send_data = crud.send_data.get_multi_by_status(
            db, send_status=send_status, skip=skip, limit=size
        )
    else:
        total = crud.send_data.count(db)
        send_data = crud.send_data.get_multi(db, skip=skip, limit=size)
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return schemas.SendDataPaginatedResponse(
        items=send_data,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/statuses", response_model=schemas.SendStatusListResponse)
def get_all_send_statuses(
    db: Session = Depends(get_db),
) -> Any:
    """
    获取所有不同的发送状态值列表，用于前端下拉框展示
    """
    statuses = crud.send_data.get_all_send_statuses(db)
    return schemas.SendStatusListResponse(statuses=statuses)


@router.post("/", response_model=schemas.SendDataResponse)
def create_send_data(
    *,
    db: Session = Depends(get_db),
    send_data_in: schemas.SendDataCreate,
) -> Any:
    """
    创建新的发送数据
    """
    send_data = crud.send_data.get(db, id=send_data_in.id)
    if send_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"发送数据已存在，ID: {send_data_in.id}",
        )
    
    # 验证KOL是否存在
    kol = crud.kol_info.get(db, id=send_data_in.kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"KOL不存在，ID: {send_data_in.kol_id}",
        )
        
    send_data = crud.send_data.create(db, obj_in=send_data_in)
    return send_data


@router.get("/{id}", response_model=schemas.SendDataResponse)
def read_send_data_by_id(
    *,
    db: Session = Depends(get_db),
    id: str,
) -> Any:
    """
    通过ID获取发送数据
    """
    send_data = crud.send_data.get(db, id=id)
    if not send_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="发送数据不存在",
        )
    return send_data


@router.put("/{id}", response_model=schemas.SendDataResponse)
def update_send_data(
    *,
    db: Session = Depends(get_db),
    id: str,
    send_data_in: schemas.SendDataUpdate,
) -> Any:
    """
    更新发送数据
    """
    send_data = crud.send_data.get(db, id=id)
    if not send_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="发送数据不存在",
        )
    send_data = crud.send_data.update(db, db_obj=send_data, obj_in=send_data_in)
    return send_data


@router.delete("/{id}", response_model=schemas.SendDataResponse)
def delete_send_data(
    *,
    db: Session = Depends(get_db),
    id: str,
) -> Any:
    """
    删除发送数据
    """
    send_data = crud.send_data.get(db, id=id)
    if not send_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="发送数据不存在",
        )
    send_data = crud.send_data.remove(db, id=id)
    return send_data


@router.get("/kol/{kol_id}", response_model=schemas.SendDataPaginatedResponse)
def read_send_data_by_kol(
    *,
    db: Session = Depends(get_db),
    kol_id: str,
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
) -> Any:
    """
    获取指定KOL的所有发送数据（分页）
    """
    # 验证KOL是否存在
    kol = crud.kol_info.get(db, id=kol_id)
    if not kol:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"KOL不存在，ID: {kol_id}",
        )
    
    skip = (page - 1) * size
    
    total = crud.send_data.count_by_kol_id(db, kol_id=kol_id)
    send_data = crud.send_data.get_multi_by_kol_id(
        db, kol_id=kol_id, skip=skip, limit=size
    )
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return schemas.SendDataPaginatedResponse(
        items=send_data,
        total=total,
        page=page,
        size=size,
        pages=pages
    )