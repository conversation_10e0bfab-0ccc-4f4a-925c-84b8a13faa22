from typing import Generator, AsyncGenerator
from sqlalchemy.orm import Session
from fastapi import Depends

from app.db.session import SessionLocal, AsyncSessionLocal

# 同步数据库会话依赖
def get_db() -> Generator[Session, None, None]:
    """
    获取同步数据库会话，用于依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 异步数据库会话依赖
async def get_async_db() -> AsyncGenerator:
    """
    获取异步数据库会话，用于依赖注入
    """
    async with AsyncSessionLocal() as session:
        yield session 