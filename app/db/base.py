from typing import Any

from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import DeclarativeBase


class Base(DeclarativeBase):
    """基础SQLAlchemy模型类"""
    id: Any
    __name__: str
    
    # 生成表名的逻辑
    @declared_attr
    def __tablename__(cls) -> str:
        """
        自动生成表名，格式为：类名小写
        实际表名在模型类中可以通过 __tablename__ 属性覆盖此方法
        """
        return cls.__name__.lower() 