#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project ：KOL-python
@Date    ：2025/4/9 下午4:39
'''
import json
from typing import Dict, List, Optional, Any, Tuple, Set
import argparse
import requests

from app.db.session import SessionLocal
from app.services.kol_tools.data_collector_v2 import convert_datetime
from app.services.llm_service import email_and_keywords_from_bio
from app.services.tools import send_webhook, fte_compute, compute_view_mean_and_median_k
from app.logging_config import get_task_logger
from app.core.config import settings
import concurrent.futures
from functools import partial
import datetime
import time

# 从 tools.py 导入必要的函数
from app.services.kol_tools.tools import (
    _validate_params,
    _collect_source_data,
    _standardize_data,
    _collect_platform_data,
    _store_data_to_db,
    _parse_table_url
)

# 从 feishu_lark_service 导入 Lark 类
from app.services.feishu_lark_service import Lark

# 使用任务日志记录器
logger = get_task_logger("app.worker.tasks.kol_data")


def ensure_lark_token_valid(lark_client: Lark) -> None:
    """
    确保Lark客户端的tenant token有效，如果无效或过期则刷新

    Args:
        lark_client: Lark客户端实例
    """
    try:
        # 检查token是否存在且未过期
        if not lark_client._tenant_token or (
                lark_client._token_expire_time and
                lark_client._token_expire_time < datetime.datetime.now()
        ):
            logger.info("Lark tenant token已过期或不存在，正在刷新")
            # 通过调用API强制刷新token
            # 这里假设获取wiki信息就会刷新token
            lark_client.get_wiki_node_info(node_token="QTRqwY8vFiplVUkP5CMc9e9Fn1f")
            logger.info("Lark tenant token已刷新")
    except Exception as e:
        logger.warning(f"刷新Lark token时出现异常: {str(e)}")
        # 这里不抛出异常，让后续操作自行处理token问题
        pass


def _get_app_token(lark_client: Lark, node_token: str, table_url: str) -> Optional[str]:
    """从飞书wiki node token获取app token"""
    if not node_token:
        return None
    try:
        info = lark_client.get_wiki_node_info(node_token=node_token)
        if info and info.get("obj_token"):
            return info.get("obj_token")
        logger.error(f"查询飞书 app_token 失败; \n{table_url}\n{node_token}")
        return None
    except Exception as e:
        logger.error(f"查询飞书 app_token 时发生异常: {e}; \n{table_url}\n{node_token}")
        return None


def _preload_kol_ids_from_lark(lark_client: Lark, tables: List[Dict]) -> Set[str]:
    """从所有指定的飞书表中预加载所有KOL ID"""
    existing_kol_ids = set()
    logger.info(f"开始从 {len(tables)} 个飞书表中预加载KOL ID用于去重...")

    for table in tables:
        try:
            all_records = lark_client.search_table_all_records(
                app_token=table["app_token"],
                table_id=table["table_id"],
                search_params={"field_names": ["KOL ID"]}
            )
            for record in all_records:
                kol_id = record.get("fields", {}).get("KOL ID")
                if kol_id:
                    existing_kol_ids.add(kol_id)
            logger.info(f"从表格 {table['table_id']} 加载了 {len(all_records)} 条记录，当前总KOL ID数: {len(existing_kol_ids)}")
        except Exception as e:
            logger.error(f"从表格 {table['table_id']} 加载KOL ID失败: {e}")
            continue

    logger.info(f"KOL ID预加载完成，总共 {len(existing_kol_ids)} 个唯一的KOL ID。")
    return existing_kol_ids


def collect_kol_data_v2(query_params: Dict, lark_client: Lark, record_id: str) -> Dict:
    """
    采用流式处理的KOL数据采集函数，每条数据单独处理并立即存储

    Args:
        query_params: 查询参数，包含所需的数据源、平台等信息
        lark_client: Lark客户端实例
        record_id: 飞书任务记录ID

    Returns:
        包含任务结果的字典
    """
    try:
        logger.info(f"开始采集平台 {query_params.get('platform')} 的KOL数据（流式处理）")

        source = query_params.get("source", "").lower()
        platform = query_params.get("platform", "").lower()
        filter_name = query_params.get("filter_name", "")
        project_code = query_params.get("project_code", "")
        auto_send = query_params.get("auto_send", "no")
        high_potential = query_params.get("high_potential", "no")
        table_save_url = query_params.get("table_save", "")
        table_duplicate_urls = query_params.get("table_duplicate", [])

        max_workers = settings.MAX_WORKERS if hasattr(settings, 'MAX_WORKERS') else 15

        _validate_params(source, platform)
        ensure_lark_token_valid(lark_client)

        send_webhook(filter_name=filter_name, status="进行中: 0%")

        try:
            # 1. 采集原始数据
            logger.info(f"[1/5] 从数据源 {source} 采集原始数据")
            raw_data = _collect_source_data(source, query_params)
            if not raw_data:
                raise ValueError(f"从数据源 {source} 获取数据失败")

            # 2. 数据标准化处理
            logger.info(f"[2/5] 数据标准化处理")
            standardized_data = _standardize_data(raw_data, source, platform)
            if not standardized_data:
                raise ValueError("数据标准化处理失败")

            total_count = len(standardized_data)
            fte_compute('UA_KOL', 'UA_KOL_Standard_Database_TK', total_count)

            # 3. 预加载用于去重的数据
            logger.info("[3/5] 预加载飞书表数据用于去重")
            # a. 获取主存储表的app_token
            save_node_token, save_table_id = _parse_table_url(table_save_url)
            main_app_token = _get_app_token(lark_client, save_node_token, table_save_url)
            if not main_app_token:
                raise ValueError(f"查询主表飞书 app_token 失败: {table_save_url}")

            # b. 构建所有需要去重的表格列表
            duplicate_tables = [{"app_token": main_app_token, "table_id": save_table_id}]
            if table_duplicate_urls:
                for url in table_duplicate_urls:
                    node_token, table_id = _parse_table_url(url)
                    app_token = _get_app_token(lark_client, node_token, url)
                    if app_token and table_id:
                        duplicate_tables.append({"app_token": app_token, "table_id": table_id})
                    else:
                        logger.warning(f"跳过无效的去重表URL或无法获取app_token: {url}")

            # c. 一次性加载所有KOL ID
            existing_kol_ids = _preload_kol_ids_from_lark(lark_client, duplicate_tables)

            # d. 在主线程中预先过滤掉重复数据
            if not settings.UPDATE:
                original_count = len(standardized_data)
                data_to_process = [item for item in standardized_data if item.get('kol_name') not in existing_kol_ids]
                skipped_count_pre_filter = original_count - len(data_to_process)
                logger.info(f"预去重完成：发现 {skipped_count_pre_filter} 条重复数据，将处理 {len(data_to_process)} 条新数据。")
            else:
                data_to_process = standardized_data
                skipped_count_pre_filter = 0

            total_to_process = len(data_to_process)
            if total_to_process == 0:
                logger.info("所有数据均为重复项，无需进一步处理。")
                # 在这里可以根据需要更新状态并提前返回
                # ...

            # 4. 多线程处理
            logger.info(f"[4/5] 开始多线程处理 {total_to_process} 条KOL数据，使用 {max_workers} 个线程")
            processed_count = 0

            # 创建线程池并执行任务
            high_potential_records = []
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                process_func = partial(
                    process_single_kol,
                    query_params=query_params,
                    lark_client=lark_client,
                    main_app_token=main_app_token,
                    save_table_id=save_table_id
                )
                futures = [executor.submit(process_func, item) for item in data_to_process]

                for future in concurrent.futures.as_completed(futures):
                    processed_count += 1
                    progress = 20 + int((processed_count / total_to_process) * 70) if total_to_process > 0 else 90
                    progress = min(progress, 90)
                    if processed_count % 10 == 0 or processed_count == total_to_process:
                        send_webhook(filter_name=filter_name, status=f"进行中: {progress}%")
                        logger.info(f"处理进度: {processed_count}/{total_to_process} ({progress}%)")

                    result_status, hp_record = future.result()
                    if hp_record:
                        high_potential_records.append(hp_record)


            # 5. 完成处理并聚合结果
            logger.info("[5/5] 所有线程执行完毕，开始聚合结果")
            results = [f.result() for f in futures]
            success_count = len([r for r, d in results if r.startswith('SUCCESS')])
            email_count = len([r for r, d in results if r == 'SUCCESS_EMAIL'])
            failed_count = len([r for r, d in results if r == 'FAILED'])
            skipped_count = skipped_count_pre_filter

            send_webhook(filter_name=filter_name, status="进行中: 100%")
            logger.info(f"总数据: {total_count}, 成功: {success_count}, 失败: {failed_count}, 预先跳过: {skipped_count}")

            msg = f"总采集数:{total_count}条，成功:{success_count}条，失败:{failed_count}条，跳过:{skipped_count}条"
            send_webhook(
                filter_name=filter_name, status="已完成", msg=msg,
                unique_count=len(high_potential_records), email_count=email_count
            )

            # 更新飞书任务状态
            remark = f"成功采集数据 {success_count} 条，失败 {failed_count} 条，跳过 {skipped_count} 条"
            record_data = {
                "点击": True, "Status": "已完成", "标记": "本地", "Remark": remark,
                "Unique Count": len(high_potential_records), "Valid Email": email_count, "Date": None
            }
            lark_client.update_table_record("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya", record_id, record_data)

            return {
                "status": "success", "message": remark, "data_count": total_count,
                "success_count": success_count, "failed_count": failed_count, "skipped_count": skipped_count,
                "unique_count": len(high_potential_records), "email_count": email_count
            }

        except (Exception, ValueError) as e:
            logger.exception(f"采集KOL数据时发生错误: {e}")
            lark_client.update_table_record("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya", record_id, {"Status": "失败", "Remark": str(e)})
            send_webhook(filter_name=filter_name, status="失败", msg=str(e))
            return {"status": "error", "message": str(e)}


    except Exception as e:
        logger.exception(f"执行采集KOL数据时发生严重错误: {e}")
        # 确保即使在最外层捕获到异常也能更新状态
        try:
            lark_client.update_table_record("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya", record_id, {"Status": "失败", "Remark": str(e)})
        except Exception as inner_e:
            logger.error(f"更新飞书失败状态时再次发生错误: {inner_e}")
        return {"status": "error", "message": str(e)}


def process_single_kol(item: Dict, query_params: Dict, lark_client: Lark,
                       main_app_token: str, save_table_id: Optional[str]) -> Tuple[str, Optional[Dict]]:
    kol_name = item.get('kol_name', '')
    try:
        # 3.1 采集平台详细数据
        item_with_detail = collect_platform_data_single(query_params.get("platform", "").lower(), item, query_params)
        if not item_with_detail:
            return 'FAILED', None

        # 3.2 对数据进行整理，将KOL信息保存到飞书，视屏信息保存到数据库
        stored_item = store_data_to_db_single(item_with_detail, query_params, lark_client, main_app_token,
                                              save_table_id)


    except Exception as e:
        logger.exception(f"处理KOL '{kol_name}' 时发生错误: {e}")
        return 'FAILED', None
def collect_platform_data_single(platform: str, item: Dict, query_params: Dict) -> Optional[Dict]:
    """采集单个KOL的平台详细数据"""
    try:
        # 利用现有的_collect_platform_data函数
        result = _collect_platform_data(platform, [item], query_params)
        if result:
            return result[0]
        logger.error(f"获取 {item.get('kol_name', '')} 的平台数据失败")
        return None
    except Exception as e:
        logger.exception(f"采集KOL {item.get('kol_name', '')} 的平台数据时发生错误: {e}")
        return None

def store_data_to_db_single(kol_detail_item: Dict, query_params: Dict, lark_client, main_app_token, save_table_id, log_prefix: str="") -> Optional[Dict]:
    """
    将单个KOL数据存储到数据库，通过API接口实现

    Args:
        kol_detail_item: 单个KOL的详细数据
        query_params: 查询参数
        log_prefix: 日志前缀

    Returns:
        Optional[Dict]: 存储后的数据，失败则返回None
    """
    kol_name = kol_detail_item.get('kol_name', '未知KOL')
    kol_id = kol_detail_item.get('kol_id', '未知ID')

    logger.info(f"{log_prefix} 💾 开始将KOL数据存储到数据库: {kol_name}")

    try:

        # 使用大模型提取邮箱和关键词
        logger.debug(f"{log_prefix} 🤖 正在使用AI提取邮箱和关键词...")
        extracted_keywords, extracted_email = email_and_keywords_from_bio(kol_detail_item.get("bio", ""))
        logger.debug(f"{log_prefix} 📧 提取到邮箱: {extracted_email if extracted_email else '无'}")

        # 根据粉丝数判断KOL等级
        followers_count = kol_detail_item.get("followers_count", 0)
        kol_level = None
        if followers_count is not None:
            if followers_count < 10000:
                kol_level = "Nano 1k～10k"
            elif 10000 <= followers_count < 50000:
                kol_level = "Micro 10k～50k"
            elif followers_count >= 50000:
                kol_level = "Mid-tier 50k～500k"

        logger.debug(f"{log_prefix} 📊 KOL等级: {kol_level} (粉丝数: {followers_count})")

        # 计算视频统计数据
        logger.debug(f"{log_prefix} 📹 正在计算视频统计数据...")
        mean_views_k, median_views_k = compute_view_mean_and_median_k(videos=kol_detail_item.get("videos", []))

        # 构建API请求体
        request_body = {
            "KOL ID": f"{kol_detail_item.get('kol_id', '')}",
            "Followers(K)": kol_detail_item.get("followers_count", 0) / 1000 if kol_detail_item.get(
                "followers_count") else 0,
            "Email": extracted_email,
            "Account Link": kol_detail_item.get("url", ""),
            "Bio": kol_detail_item.get("bio", ""),
            "KOL Name": kol_detail_item.get("username", ""),
            "Source": query_params.get("source", ""),
            "Level": kol_level,
            "Engagement Rate(%)": kol_detail_item.get("engagement_rate", 0),
            "Median Views(K)": median_views_k,
            "Filter": query_params.get("filter_name", ""),
            "Likes(K)": kol_detail_item.get("likes_count", 0) / 1000 if kol_detail_item.get("likes_count") else 0,
            "Mean Views(K)": mean_views_k,
            "Average Views(K)": kol_detail_item.get("averageViews", 0) / 1000 if kol_detail_item.get(
                "averageViews") else 0,
            "Average Likes(K)": kol_detail_item.get("averageLikes", 0) / 1000 if kol_detail_item.get(
                "averageLikes") else 0,
            "Average Comments(K)": kol_detail_item.get("averageComments", 0) / 1000 if kol_detail_item.get(
                "averageComments") else 0,
            "Most used hashtags":"",
            "Project": query_params.get("project_code", ""),
            # "Platform": kol_detail_item.get("platform", ""),
            # "Keywords-AI": extracted_keywords
        }

        logger.info(f"{log_prefix} 📤 正在调用飞书存储KOL数据...")
        record_id = lark_client.batch_create_table_records(main_app_token, save_table_id, request_body)

        send_to_feishu_single(kol_detail_item, query_params, lark_client)
        # 处理视频数据存储
        if kol_detail_item.get("videos"):
            logger.debug(f"{log_prefix} 📹 开始处理 {len(kol_detail_item['videos'])} 个视频数据...")
            videos_list = kol_detail_item["videos"]

            for video_index, video_item in enumerate(videos_list, 1):
                # 构建视频数据请求体
                video_request_body = {
                    "kol_id": request_body["kol_id"],
                    "play_count": video_item.get("play_count", 0),
                    "is_pinned": video_item.get("is_pinned", False),
                    "share_url": video_item.get("share_url", ""),
                    "desc": video_item.get("desc", ""),
                    "desc_language": video_item.get("desc_language", ""),
                    "video_url": video_item.get("video_url", ""),
                    "music_url": video_item.get("music_url", ""),
                    "likes_count": video_item.get("likes_count", 0),
                    "comments_count": video_item.get("comments_count", 0),
                    "shares_count": video_item.get("shares_count", 0),
                    "collect_count": video_item.get("collect_count", 0),
                    "platform": kol_detail_item.get("platform", ""),
                    "hashtags": video_item.get("hashtags", []),
                    "create_time": video_item.get("create_time"),
                    "video_id": video_item.get("video_id", "")
                }

                # 检查视频ID是否有效
                if not video_request_body["video_id"]:
                    logger.warning(f"{log_prefix} ⚠️ 跳过无效视频ID的视频数据 (视频 {video_index})")
                    continue

                # 调用视频API
                video_response = requests.post(
                    "http://54.84.111.234:8000/api/v1/videos",
                    json=convert_datetime(video_request_body),
                    timeout=30
                )

                if video_response.status_code == 200:
                    logger.debug(
                        f"{log_prefix} ✅ 视频数据存储成功: {video_request_body['video_id']} (视频 {video_index})")
                else:
                    logger.error(
                        f"{log_prefix} ❌ 视频数据存储失败: {video_response.status_code} - {video_response.text} (视频 {video_index})")

        else:
                logger.error(f"{log_prefix} ❌ KOL数据存储失败")
                return None

    except Exception as e:
        logger.exception(f"{log_prefix} ❌ 存储KOL数据到数据库时发生异常: {str(e)}, KOL: {kol_name}")
        return None

def store_data_to_db(platform_data: List[Dict], query_params: Optional[Dict] = None) -> List[Dict]:
    """
    将数据存储到数据库

    Args:
        db: 数据库会话
        platform_data: 平台数据
        query_params: 查询参数，包含filter_name，filter_body，project_code等

    Returns:
        存储后的数据列表
    """
    logger.info("开始将数据存储到数据库")

    stored_data = []
    try:
        # 遍历平台数据
        for item in platform_data:
            # 让大模型提取 邮箱 和 keyword ai
            keywords_ai, email = email_and_keywords_from_bio(item.get("bio", ""))

            # 根据粉丝数判断等级
            followers_count = item.get("followers_count", 0)
            level = None
            if followers_count is not None:
                if followers_count < 10000:
                    level = "Nano 1k～10k"
                elif 10000 <= followers_count < 50000:
                    level = "Micro 10k～50k"
                elif followers_count >= 50000:
                    level = "Mid-tier 50k～500k"

            # 计算平均播放数和中位数
            mean_views_k, median_views_k = compute_view_mean_and_median_k(videos=item.get("videos", []))

            # 构建KOL信息对象
            kol_data = {
                "kol_id": item.get("kol_name", ""),
                "username": item.get("username", ""),
                "email": email,
                "bio": item.get("bio", ""),
                "account_link": item.get("url", ""),  # 使用url作为账号链接
                "platform": item.get("platform", ""),
                "engagement_rate": item.get("engagement_rate", 0),
                "average_views_k": item.get("averageViews", 0) / 1000 if item.get("averageViews") else 0,  # 转换为K单位
                "followers_k": item.get("followers_count", 0) / 1000 if item.get("followers_count") else 0,  # 转换为K单位
                "likes_k": item.get("likes_count", 0) / 1000 if item.get("likes_count") else 0,  # 转换为K单位
                "keywords_ai": keywords_ai,
                "source": query_params.get("source", ""),
                "level": level,
                "mean_views_k": mean_views_k,
                "median_views_k": median_views_k
            }

            # 检查KOL ID是否有效
            if not kol_data["kol_id"]:
                logger.warning(f"无效的KOL ID，跳过此KOL: {kol_data}")
                continue
            stored_data.append(kol_data)
            # 处理视频数据
            if item.get("videos"):
                videos_list = item["videos"]
                for video_index, video_item in enumerate(videos_list, 1):
                    # 构建视频数据请求体
                    video_request_body = {
                        "kol_id": item["kol_id"],
                        "play_count": video_item.get("play_count", 0),
                        "is_pinned": video_item.get("is_pinned", False),
                        "share_url": video_item.get("share_url", ""),
                        "desc": video_item.get("desc", ""),
                        "desc_language": video_item.get("desc_language", ""),
                        "video_url": video_item.get("video_url", ""),
                        "music_url": video_item.get("music_url", ""),
                        "likes_count": video_item.get("likes_count", 0),
                        "comments_count": video_item.get("comments_count", 0),
                        "shares_count": video_item.get("shares_count", 0),
                        "collect_count": video_item.get("collect_count", 0),
                        "platform": item.get("platform", ""),
                        "hashtags": video_item.get("hashtags", []),
                        "create_time": video_item.get("create_time"),
                        "video_id": video_item.get("video_id", "")
                    }

                    # 检查视频ID是否有效
                    if not video_request_body["video_id"]:
                        logger.warning(f" ⚠️ 跳过无效视频ID的视频数据 (视频 {video_index})")
                        continue

                    # 调用视频API
                    video_response = requests.post(
                        "http://54.84.111.234:8000/api/v1/videos",
                        json=convert_datetime(video_request_body),
                        timeout=30
                    )

                    if video_response.status_code == 200:
                        logger.debug(
                            f"✅ 视频数据存储成功: {video_request_body['video_id']} (视频 {video_index})")
                    else:
                        logger.error(
                            f"❌ 视频数据存储失败: {video_response.status_code} - {video_response.text} (视频 {video_index})")

        logger.info(f"成功将 {len(stored_data)} 条数据存储到数据库")
        return stored_data

    except Exception as e:
        logger.exception(f"将数据存储到数据库时发生错误: {str(e)}")
        return []


def send_to_feishu_single(item: Dict, query_params: Dict, lark_client: Lark) -> bool:
    """
    将单个KOL数据发送到飞书邮箱表

    Args:
        item: 单个KOL的详细数据（可以是API返回结果或数据库查询结果）
        query_params: 查询参数
        lark_client: Lark客户端实例

    Returns:
        是否成功发送
    """
    logger.info(f"开始将KOL数据发送到邮箱表: {item.get('kol_name', '')}")

    try:
        # 检查是否有邮箱
        if not item.get("email"):
            logger.warning(f"KOL没有邮箱，跳过发送: {item.get('kol_name', '')}")
            return False

        # 获取飞书配置 https://laientech.feishu.cn/wiki/KNnaw2wdPiwbIDk1ZlDc2AX6nIc?table=tblUdyWvC0X5Sg9k&view=vewTEKwd7L
        # https://laientech.feishu.cn/wiki/KNnaw2wdPiwbIDk1ZlDc2AX6nIc?table=tblaInulGyWuAoCk&view=vewTEKwd7L test
        app_token = 'Mo7obvrgtaIMqRsZkdwc6UUFnCg'
        table_id = 'tblUdyWvC0X5Sg9k'

        # 获取KOL ID - 支持多种字段名
        kol_id = item.get("kol_name") or item.get("kol_id", "")
        if not kol_id:
            logger.error("KOL ID为空，无法发送数据")
            return False

        # 构建精确查询条件，只查询特定KOL ID
        search_params = {
            "filter": {
                "conjunction": "and",
                "conditions": [
                    {
                        "field_name": "KOL ID",
                        "operator": "is",
                        "value": [kol_id]
                    }
                ]
            }
        }

        # 精确查询特定KOL ID的记录
        matching_records = lark_client.search_table_record(app_token, table_id, search_params)

        # 检查是否找到匹配记录
        if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
            logger.info(f"KOL ID '{kol_id}' 已存在于邮件表，跳过发送")
            return True  # 已存在，返回成功

        # 格式化数据为飞书表格所需格式
        # 支持从数据库查询结果中获取数据
        record = {
            "KOL ID": kol_id,
            "Email": item.get("email", ""),
            "Account link": {"text": item.get("account_link", ""), "link": item.get("account_link", "")},
            "KOL Name": item.get("username", ""),
            "Template": query_params.get("template", ""),
            "App Code": query_params.get("project_code", ""),
        }

        # 创建新记录
        result = lark_client.batch_create_table_records(app_token, table_id, [record])

        if result:
            logger.info(f"成功将KOL '{kol_id}' 发送到自动发邮件飞书表")
            return True
        else:
            logger.error(f"发送KOL '{kol_id}' 到自动发邮件飞书表失败")
            return False

    except Exception as e:
        logger.exception(f"发送单条KOL数据到飞书邮箱表时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return False


def store_high_potential_data_single(filter_name: str, project_code: str, item: Dict,
                                     table_save_url: str, lark_client: Lark, app_token: str) -> Tuple[bool, Optional[Dict]]:
    """将单个高潜力KOL数据存储到指定飞书表 (不再进行查重)"""
    kol_id = item.get("kol_name", "")
    logger.info(f"开始将高潜力KOL数据 '{kol_id}' 存储到飞书表")

    try:
        _, save_table_id = _parse_table_url(table_save_url)
        if not save_table_id:
            logger.error(f"解析目标存储表URL失败: {table_save_url}")
            return False, None
        if not kol_id:
            logger.error("KOL ID为空，无法存储数据")
            return False, None

        record = {
            "KOL ID": kol_id,
            "Email": item.get("email", ""),
            "Account link": {"text": item.get("account_link", ""), "link": item.get("account_link", "")},
            "KOL Name": item.get("username", ""),
            "Bio": item.get("bio", ""),
            "Source": item.get("source", ""),
            "Filter": filter_name,
            "Followers(K)": item.get('followers_k', 0),
            "Mean Views(K)": item.get('mean_views_k', 0),
            "Median Views(K)": item.get('median_views_k', 0),
            "Keywords-AI": ", ".join(str(keyword) for keyword in item.get('keywords_ai', []) if keyword),
            "Level": item.get('level'),
            "Project": project_code,
            "Engagement Rate(%)": f"{item.get('engagement_rate', 0) * 100:.2f} %",
            "Average Views(K)": item.get('average_views_k', 0),
        }

        result = lark_client.batch_create_table_records(app_token, save_table_id, [record])
        if result:
            logger.info(f"成功将KOL '{kol_id}' 存储到高潜力飞书表")
            return True, record
        else:
            logger.error(f"存储KOL '{kol_id}' 到高潜力飞书表失败")
            return False, None

    except Exception as e:
        logger.exception(f"存储高潜力KOL '{kol_id}' 到飞书表时发生错误: {e}")
        return False, None



def send_slack_message(channel, text):
    requestBody = {
        "channel": channel,
        "blocks": [
            {"type": "section", "text": {"type": "mrkdwn", "text": text}}
        ]
    }
    header = {
        "Authorization": "Bearer *******************************************************"
    }
    try:
        response = requests.post("https://slack.com/api/chat.postMessage", headers=header, json=requestBody, timeout=10)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        logger.error(f"发送Slack消息失败: {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='KOL数据采集工具')
    parser.add_argument('--channel_id', type=str, default="U05PD11UDPW", help='Slack频道ID')
    args = parser.parse_args()
    channel_id = args.channel_id

    lark_client = Lark()
    while True:
        try:
            items = lark_client.search_table_all_records(
                "PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya",
                search_params={
                    "filter": {
                        "conjunction": "and",
                        "conditions": [
                            {"field_name": "Date", "operator": "is", "value": ["Today"]},
                            {"field_name": "点击", "operator": "is", "value": ["false"]}
                        ]
                    }
                },
                page_size=100
            )

            if not items:
                logger.info("未找到今日待处理任务，脚本执行结束。")
                send_slack_message(channel_id, "*任务检查完成:* ✅ 未发现今日待处理任务。")
                break

            processed_data = []
            for entry in items:
                fields = entry.get("fields", {})
                filter_criteria = fields.get("Filter Criteria", [])
                cookie_list = fields.get("Cookie", [])

                query_params = {
                    "source": fields.get("Source", ""),
                    "platform": fields.get("Platform", ""),
                    "filter_name": fields.get("Filter Name", [{}])[0].get("text", ""),
                    "filter_body": "".join([fc.get("text", "") for fc in filter_criteria]),
                    "cookie": "; ".join([c.get("text", "").strip().rstrip(";") for c in cookie_list if c.get("type") == "text"]),
                    "project_code": fields.get("Project Code", ""),
                    "auto_send": fields.get("Auto Send", ""),
                    "template": fields.get("Email Template", ""),
                    "high_potential": fields.get("High Pontential", ""),
                    "table_save": fields.get("high potential存储表", {}).get("link", ""),
                    "table_duplicate":[
                        link["link"] if isinstance(link, dict) else link
                        for link in fields.get("high potential去重表", [])
                        if link and (isinstance(link, str) or "link" in link)
                    ]
                }
                processed_data.append({"query_params": query_params, "record_id": entry["record_id"]})

            logger.info(f"本次共有 {len(processed_data)} 条filter执行任务")
            for item in processed_data:
                filter_name = item['query_params']['filter_name']
                send_slack_message(channel_id, f"*开始执行:* 🚀 ` {filter_name} `")
                logger.info(f"开始执行: {filter_name}")

                result = collect_kol_data_v2(item['query_params'], lark_client, item['record_id'])

                logger.info(f"执行结果: {result.get('message')}")
                send_slack_message(channel_id, f"*执行结果:* 🏁 ` {filter_name} ` - {result.get('message')}")
                logger.info(f"执行结束: {filter_name}\n")

            send_slack_message(channel_id, f"*全部任务执行结束:* 🎉")
            logger.info("当前轮次任务全部执行完毕")
            # 如果希望脚本只运行一次，在这里加上 break
            break
        except Exception as e:
            logger.exception(f"主循环发生严重错误: {e}")
            send_slack_message(channel_id, f"*严重错误:* ❌ 脚本执行异常，请检查日志。错误: {e}")
            # 等待一段时间再重试，避免因临时问题导致无限循环
            time.sleep(60)
