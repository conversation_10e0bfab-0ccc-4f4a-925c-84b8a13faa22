#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project ：KOL-python
@Date    ：2025/4/9 下午4:39
@Description: KOL数据采集工具V3版本，主信息保存到飞书表，视频数据保存到数据库，去重逻辑与V2一致。
'''
import json
import os
import hashlib
from typing import Dict, List, Optional, Any, Tuple, Set
import argparse
import requests
import concurrent.futures
from functools import partial
import datetime
import time
import threading

from app.db.session import SessionLocal
from app.services.kol_tools.data_collector_v2 import convert_datetime
from app.services.llm_service import email_and_keywords_from_bio
from app.services.tools import send_webhook, fte_compute, compute_view_mean_and_median_k
from app.logging_config import get_task_logger
from app.core.config import settings
from app.services.kol_tools.tools import (
    _validate_params,
    _collect_source_data,
    _standardize_data,
    _parse_table_url,
    _collect_platform_data
)
from app.services.feishu_lark_service import Lark

# 使用任务日志记录器
logger = get_task_logger("app.worker.tasks.kol_data")

# 缓存目录
CACHE_DIR = "cache/raw_data"


# ===================== 缓存相关函数 =====================
def get_cache_filename(filter_name: str, source_name: str, platform_name: str) -> str:
    """
    生成缓存文件名

    Args:
        filter_name: 过滤条件名称
        source_name: 数据源名称
        platform_name: 平台名称

    Returns:
        str: 缓存文件名
    """
    # 使用filter_name、source_name、platform_name生成唯一标识
    cache_key = f"{filter_name}_{source_name}_{platform_name}"
    # 使用MD5生成固定长度的文件名
    filename = hashlib.md5(cache_key.encode()).hexdigest() + ".json"
    return filename


def save_raw_data_to_cache(raw_data_list: List[Dict], filter_name: str, source_name: str, platform_name: str) -> str:
    """
    将原始数据保存到缓存文件

    Args:
        raw_data_list: 原始数据列表
        filter_name: 过滤条件名称
        source_name: 数据源名称
        platform_name: 平台名称

    Returns:
        str: 缓存文件路径
    """
    # 确保缓存目录存在
    os.makedirs(CACHE_DIR, exist_ok=True)

    # 生成缓存文件名
    filename = get_cache_filename(filter_name, source_name, platform_name)
    cache_path = os.path.join(CACHE_DIR, filename)

    # 保存数据到文件
    cache_data = {
        "filter_name": filter_name,
        "source_name": source_name,
        "platform_name": platform_name,
        "timestamp": datetime.datetime.now().isoformat(),
        "data_count": len(raw_data_list),
        "raw_data": raw_data_list
    }

    with open(cache_path, 'w', encoding='utf-8') as f:
        json.dump(cache_data, f, ensure_ascii=False, indent=2)

    logger.info(f"💾 原始数据已缓存: {cache_path} (共 {len(raw_data_list)} 条记录)")
    return cache_path


def load_raw_data_from_cache(filter_name: str, source_name: str, platform_name: str) -> Optional[List[Dict]]:
    """
    从缓存文件加载原始数据

    Args:
        filter_name: 过滤条件名称
        source_name: 数据源名称
        platform_name: 平台名称

    Returns:
        Optional[List[Dict]]: 原始数据列表，如果缓存不存在则返回None
    """
    filename = get_cache_filename(filter_name, source_name, platform_name)
    cache_path = os.path.join(CACHE_DIR, filename)

    if not os.path.exists(cache_path):
        logger.debug(f"📂 缓存文件不存在: {cache_path}")
        return None

    try:
        with open(cache_path, 'r', encoding='utf-8') as f:
            cache_data = json.load(f)

        # 验证缓存数据的有效性
        if (cache_data.get("filter_name") == filter_name and
                cache_data.get("source_name") == source_name and
                cache_data.get("platform_name") == platform_name):

            raw_data_list = cache_data.get("raw_data", [])
            logger.info(f"📂 从缓存加载数据: {cache_path} (共 {len(raw_data_list)} 条记录)")
            return raw_data_list
        else:
            logger.warning(f"⚠️ 缓存文件数据不匹配，忽略缓存: {cache_path}")
            return None

    except Exception as e:
        logger.error(f"❌ 读取缓存文件失败: {cache_path}, 错误: {str(e)}")
        return None


def delete_cache_file(filter_name: str, source_name: str, platform_name: str) -> bool:
    """
    删除缓存文件

    Args:
        filter_name: 过滤条件名称
        source_name: 数据源名称
        platform_name: 平台名称

    Returns:
        bool: 是否成功删除
    """
    filename = get_cache_filename(filter_name, source_name, platform_name)
    cache_path = os.path.join(CACHE_DIR, filename)

    try:
        if os.path.exists(cache_path):
            os.remove(cache_path)
            logger.info(f"🗑️ 缓存文件已删除: {cache_path}")
            return True
        else:
            logger.debug(f"📂 缓存文件不存在，无需删除: {cache_path}")
            return True
    except Exception as e:
        logger.error(f"❌ 删除缓存文件失败: {cache_path}, 错误: {str(e)}")
        return False


# ===================== 主流程入口 =====================
def collect_kol_data_v3(query_params: Dict, lark_client: Lark, record_id: str) -> Dict:
    """
    KOL数据采集主流程，主信息保存到飞书表，视频数据保存到数据库。
    1. 参数校验与初始化
    2. 采集原始数据
    3. 数据标准化
    4. 去重处理
    5. 多线程存储（KOL主信息写入飞书表，有邮箱的写入飞书邮箱表，视频数据写入数据库）
    6. 任务状态与通知
    """
    try:
        # 1. 参数校验与初始化
        logger.info("🚀 ========== KOL数据采集任务开始 ==========")
        source = query_params.get("source", "").lower()
        platform = query_params.get("platform", "").lower()
        filter_name = query_params.get("filter_name", "")
        project_code = query_params.get("project_code", "")
        table_save_url = query_params.get("table_save", "")
        table_duplicate_urls = query_params.get("table_duplicate", [])
        max_workers = settings.MAX_WORKERS if hasattr(settings, 'MAX_WORKERS') else 1
        # 获取Slack频道ID，优先从query_params获取
        channel_id = query_params.get("channel_id", "U05PD11UDPW")
        
        logger.info(f"📋 任务参数: 平台={platform}, 数据源={source}, 过滤条件={filter_name}")
        _validate_params(source, platform)
        ensure_lark_token_valid(lark_client)
        # Slack通知：任务开始
        send_slack_message(channel_id, f"*KOL采集任务开始* :rocket: 过滤器:`{filter_name}`")

        # 2. 采集原始数据
        logger.info("📊 ========== 第一阶段：数据源采集 ==========")
        # 首先尝试从缓存加载数据
        raw_data = load_raw_data_from_cache(filter_name, source, platform)

        if raw_data is None:
            # 缓存不存在，从数据源采集
            logger.info(f"📡 缓存不存在，开始从数据源 '{source}' 采集原始数据...")
            raw_data = _collect_source_data(source, query_params)

            if raw_data:
                # 采集成功，保存到缓存
                save_raw_data_to_cache(raw_data, filter_name, source, platform)
            else:
                send_slack_message(channel_id,
                                   f"*KOL采集任务失败* :x: 过滤器:`{filter_name}`\n原因: 从数据源 {source} 获取数据失败")
                raise ValueError(f"从数据源 {source} 获取数据失败")
        else:
            logger.info(f"✅ 使用缓存数据，跳过数据源采集")

        logger.info(f"✅ 原始数据采集完成，共获取 {len(raw_data)} 条记录")

        # 3. 数据标准化
        logger.info("🔄 ========== 第二阶段：数据标准化 ==========")
        standardized_data = _standardize_data(raw_data, source, platform)
        if not standardized_data:
            send_slack_message(channel_id, f"*KOL采集任务失败* :x: 过滤器:`{filter_name}`\n原因: 数据标准化处理失败")
            raise ValueError("数据标准化处理失败")
        total_count = len(standardized_data)
        fte_compute('UA_KOL', 'UA_KOL_Standard_Database_TK', total_count)
        logger.info(f"✅ 数据标准化完成，共处理 {len(standardized_data)} 条记录")

        # 4. 去重处理
        logger.info("🔍 ========== 第三阶段：去重处理 ==========")
        # a. 获取主存储表的 app_token 和 table_id
        save_node_token, save_table_id = _parse_table_url(table_save_url)
        main_app_token = _get_app_token(lark_client, save_node_token, table_save_url)
        if not main_app_token:
            raise ValueError(f"查询主表飞书 app_token 失败: {table_save_url}")

        # b. 构建去重表格列表，排除主表 URL
        duplicate_tables = [{"app_token": main_app_token, "table_id": save_table_id}]
        if table_duplicate_urls:
            for url in table_duplicate_urls:
                if save_table_id in url:
                    continue  # 跳过主表 URL，防止重复
                node_token, table_id = _parse_table_url(url)
                app_token = _get_app_token(lark_client, node_token, url)
                if app_token and table_id:
                    duplicate_tables.append({"app_token": app_token, "table_id": table_id})
                else:
                    logger.warning(f"⚠️ 跳过无效的去重表URL或无法获取app_token: {url}")
        # c. 一次性加载所有KOL ID
        existing_kol_ids = _preload_kol_ids_from_lark(lark_client, duplicate_tables)
        # d. 主线程过滤重复数据
        original_count = len(standardized_data)
        data_to_process = [item for item in standardized_data if item.get('kol_name') not in existing_kol_ids]
        skipped_count = original_count - len(data_to_process)
        logger.info(f"✅ 去重完成：发现 {skipped_count} 条重复数据，将处理 {len(data_to_process)} 条新数据")
        total_to_process = len(data_to_process)
        if total_to_process == 0:
            logger.info("ℹ️ 所有数据均为重复项，无需进一步处理")
            send_slack_message(channel_id,
                               f"*KOL采集任务完成* :white_check_mark: 过滤器:`{filter_name}`\n全部为重复数据，无需处理。")
            return {"status": "success", "message": "全部为重复数据，无需处理。", "data_count": total_count,
                    "success_count": 0, "failed_count": 0, "skipped_count": skipped_count}

        # 5. 多线程存储
        logger.info("⚡ ========== 第四阶段：多线程数据处理 ==========")
        logger.info(f"🎯 开始多线程处理，总记录数: {total_to_process}, 工作线程数: {max_workers}")
        processed_count = 0
        success_count = 0
        failed_count = 0
        email_count = 0
        lock = threading.Lock()

        def process_single_kol(item: Dict) -> str:
            """
            处理单个KOL数据：采集平台详细数据，主信息写入飞书表，有邮箱的写入邮箱表，视频数据写入数据库。
            """
            nonlocal success_count, failed_count, email_count, processed_count
            kol_name = item.get('kol_name', '')
            thread_id = threading.current_thread().ident
            thread_prefix = f"[线程{thread_id}]"
            
            try:
                # 1. 采集单个KOL的平台详细数据
                logger.debug(f"{thread_prefix} 🔍 开始采集KOL '{kol_name}' 的平台详细数据...")
                kol_detail_data = collect_platform_data_single(platform, item, query_params)
                if not kol_detail_data:
                    logger.error(f"{thread_prefix} ❌ KOL '{kol_name}' 平台数据采集失败")
                    with lock:
                        failed_count += 1
                    return 'FAILED'
                
                logger.debug(f"{thread_prefix} ✅ KOL '{kol_name}' 平台数据采集成功，开始存储...")

                extracted_keywords, extracted_email = email_and_keywords_from_bio(kol_detail_data.get("bio", ""))
                # 2. 写入飞书主表
                record_body = {}
                
                # 只有当字段值有效时才添加到record_body
                if kol_detail_data.get('kol_name'):
                    record_body["KOL ID"] = f"{kol_detail_data.get('kol_name', '')}"
                
                followers_count = kol_detail_data.get("followers_count", 0)
                if followers_count and followers_count > 0:
                    record_body["Followers(K)"] = followers_count / 1000
                
                if extracted_email:
                    record_body["Email"] = extracted_email
                
                if kol_detail_data.get("url"):
                    record_body["Account link"] = {
                        "link": kol_detail_data.get("url", ""),
                        "text": kol_detail_data.get("url", ""),
                        "type": "url"
                    }
                
                if kol_detail_data.get("bio"):
                    record_body["Bio"] = kol_detail_data.get("bio", "")
                
                if kol_detail_data.get("username"):
                    record_body["KOL Name"] = kol_detail_data.get("username", "")
                
                if query_params.get("source"):
                    record_body["Source"] = query_params.get("source", "")
                
                # 计算KOL等级
                if followers_count and followers_count > 0:
                    if followers_count < 10000:
                        record_body["Level"] = "Nano 1k～10k"
                    elif 10000 <= followers_count < 50000:
                        record_body["Level"] = "Micro 10k～50k"
                    elif followers_count >= 50000:
                        record_body["Level"] = "Mid-tier 50k～500k"
                
                engagement_rate = kol_detail_data.get("engagement_rate", 0)
                if engagement_rate and engagement_rate > 0:
                    record_body["Engagement Rate(%)"] = engagement_rate
                
                if query_params.get("filter_name"):
                    record_body["Filter"] = query_params.get("filter_name", "")
                
                likes_count = kol_detail_data.get("likes_count", 0)
                if likes_count and likes_count > 0:
                    record_body["Likes(K)"] = likes_count / 1000
                
                # 计算视频统计
                mean_views_k, median_views_k = compute_view_mean_and_median_k(videos=kol_detail_data.get("videos", []))
                if mean_views_k and mean_views_k > 0:
                    record_body["Mean Views(K)"] = mean_views_k
                if median_views_k and median_views_k > 0:
                    record_body["Median Views(K)"] = median_views_k
                
                average_views = kol_detail_data.get("averageViews", 0)
                if average_views and average_views > 0:
                    record_body["Average Views(K)"] = average_views / 1000
                
                average_likes = kol_detail_data.get("averageLikes", 0)
                if average_likes and average_likes > 0:
                    record_body["Average Likes(K)"] = average_likes / 1000
                
                average_comments = kol_detail_data.get("averageComments", 0)
                if average_comments and average_comments > 0:
                    record_body["Average Comments(K)"] = average_comments / 1000

                if extracted_keywords:
                    record_body["Keywords-AI"] = ", ".join(extracted_keywords)
                
                # 写入飞书主表
                records = [record_body]
                lark_response = lark_client.batch_create_table_records(main_app_token, save_table_id, records)
                
                # 检查飞书API返回结果
                if not lark_response or not lark_response.get("records") or len(lark_response.get("records", [])) == 0:
                    logger.error(f"{thread_prefix} ❌ KOL '{kol_name}' 写入飞书主表失败: {lark_response}")
                    with lock:
                        failed_count += 1
                    return 'FAILED'
                
                logger.debug(f"{thread_prefix} ✅ KOL '{kol_name}' 写入飞书主表成功，记录ID: {lark_response['records'][0].get('record_id', 'unknown')}")
                
                # 有邮箱的写入邮箱表
                if extracted_email:
                    _store_kol_to_email_lark(record_body, query_params, lark_client)
                # 视频数据写入数据库
                if kol_detail_data.get("videos"):
                    for video in kol_detail_data.get("videos", [])[:15]:
                        _store_video_to_db(kol_detail_data, video)
                with lock:
                    success_count += 1
                    if extracted_email:
                        email_count += 1
                return 'SUCCESS'
            except Exception as e:
                logger.exception(f"{thread_prefix} ❌ 处理KOL '{kol_name}' 时发生错误: {e}")
                with lock:
                    failed_count += 1
                return 'FAILED'
            finally:
                with lock:
                    processed_count += 1
                    # 每处理一条记录就输出进度日志，不发送Slack
                    logger.info(f"{thread_prefix} 📊 处理进度: [{processed_count}/{total_to_process}]")

        # 多线程执行
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(process_single_kol, item) for item in data_to_process]
            concurrent.futures.wait(futures)

        # 6. 任务状态与通知
        logger.info("🎉 ========== 第五阶段：任务完成 ==========")
        msg = f"总采集数:{total_count}条，成功:{success_count}条，失败:{failed_count}条，跳过:{skipped_count}条"
        send_slack_message(channel_id, f"*KOL采集任务完成* :tada: 过滤器:`{filter_name}`\n{msg}")
        remark = f"成功采集数据 {success_count} 条，失败 {failed_count} 条，跳过 {skipped_count} 条"
        lark_client.update_table_record("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya", record_id, {
            "点击": True, "Status": "已完成", "标记": "本地", "Remark": remark,
            "Unique Count": success_count, "Valid Email": email_count, "Date": None
        })
        logger.info(f"✅ 任务完成统计: {msg}")
        return {
            "status": "success", "message": remark, "data_count": total_count,
            "success_count": success_count, "failed_count": failed_count, "skipped_count": skipped_count,
            "unique_count": success_count, "email_count": email_count
        }
    except Exception as e:
        logger.exception(f"❌ 执行采集KOL数据时发生严重错误: {e}")
        try:
            send_slack_message("U05PD11UDPW", f"*KOL采集任务异常* :x: 过滤器:`{filter_name}`\n错误: {str(e)}")
            lark_client.update_table_record("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya", record_id,
                                            {"Status": "失败", "Remark": str(e)})
        except Exception as inner_e:
            logger.error(f"❌ 更新飞书失败状态时再次发生错误: {inner_e}")
        return {"status": "error", "message": str(e)}


# ===================== 工具函数 =====================
def collect_platform_data_single(platform_name: str, kol_item: Dict, query_params: Dict) -> Optional[Dict]:
    """
    采集单个KOL的平台详细数据

    Args:
        platform_name: 平台名称（TikTok/Instagram/YouTube）
        kol_item: 单个KOL的标准化数据
        query_params: 查询参数

    Returns:
        Optional[Dict]: 采集到的平台详细数据，失败则返回None
    """
    kol_name = kol_item.get('kol_name', '未知KOL')
    kol_id = kol_item.get('kol_id', '未知ID')

    logger.debug(f"🔍 开始采集平台 '{platform_name}' 的KOL详细数据: {kol_name} (ID: {kol_id})")

    try:
        # 构建单条数据列表，利用现有的批量采集函数
        single_kol_list = [kol_item]
        platform_data_result = _collect_platform_data(platform_name, single_kol_list, query_params)

        # 检查采集结果
        if platform_data_result and len(platform_data_result) > 0:
            kol_detail_data = platform_data_result[0]

            # 验证数据完整性
            if _validate_kol_data_completeness(kol_detail_data, platform_name):
                logger.debug(f"✅ KOL '{kol_name}' 平台数据采集成功")
                return kol_detail_data
            else:
                logger.error(f"❌ KOL '{kol_name}' 平台数据不完整，视为采集失败")
                return None
        else:
            logger.error(f"❌ KOL '{kol_name}' 平台数据采集失败，未获取到有效数据")
            return None

    except Exception as e:
        logger.exception(f"❌ 采集KOL '{kol_name}' 平台数据时发生异常: {str(e)}")
        return None


def _validate_kol_data_completeness(kol_data: Dict, platform_name: str) -> bool:
    """
    验证KOL数据的完整性
    
    Args:
        kol_data: KOL数据
        platform_name: 平台名称
        
    Returns:
        bool: 数据是否完整
    """
    # 基础必需字段
    required_fields = ['kol_name', 'username', 'url']

    # 检查基础字段
    for field in required_fields:
        if not kol_data.get(field):
            logger.debug(f"⚠️ 缺少必需字段: {field}")
            return False

    # 平台特定验证
    if platform_name.lower() == 'tiktok':
        # TikTok需要粉丝数和视频数据
        if not kol_data.get('followers_count') or kol_data.get('followers_count') <= 0:
            logger.debug(f"⚠️ TikTok KOL缺少有效的粉丝数: {kol_data.get('followers_count')}")
            return False

        # 检查是否有视频数据（即使为空列表也算有效）
        if 'videos' not in kol_data:
            logger.debug("⚠️ TikTok KOL缺少视频数据字段")
            return False

    elif platform_name.lower() == 'instagram':
        # Instagram需要粉丝数
        if not kol_data.get('followers_count') or kol_data.get('followers_count') <= 0:
            logger.debug(f"⚠️ Instagram KOL缺少有效的粉丝数: {kol_data.get('followers_count')}")
            return False

    elif platform_name.lower() == 'youtube':
        # YouTube需要粉丝数
        if not kol_data.get('followers_count') or kol_data.get('followers_count') <= 0:
            logger.debug(f"⚠️ YouTube KOL缺少有效的粉丝数: {kol_data.get('followers_count')}")
            return False

    logger.debug(f"✅ KOL数据完整性验证通过")
    return True


def ensure_lark_token_valid(lark_client: Lark) -> None:
    """
    确保Lark客户端的tenant token有效，如果无效或过期则刷新
    """
    try:
        if not lark_client._tenant_token or (
                lark_client._token_expire_time and
                lark_client._token_expire_time < datetime.datetime.now()
        ):
            logger.info("🔄 Lark tenant token已过期或不存在，正在刷新")
            lark_client.get_wiki_node_info(node_token="QTRqwY8vFiplVUkP5CMc9e9Fn1f")
            logger.info("✅ Lark tenant token已刷新")
    except Exception as e:
        logger.warning(f"⚠️ 刷新Lark token时出现异常: {str(e)}")
        pass


def _get_app_token(lark_client: Lark, node_token: str, table_url: str) -> Optional[str]:
    """
    从飞书wiki node token获取app token
    """
    if not node_token:
        return None
    try:
        info = lark_client.get_wiki_node_info(node_token=node_token)
        if info and info.get("obj_token"):
            logger.debug(f"✅ 成功获取飞书 app_token: {info.get('obj_token')}")
            return info.get("obj_token")
        logger.error(f"❌ 查询飞书 app_token 失败: {table_url}")
        return None
    except Exception as e:
        logger.error(f"❌ 查询飞书 app_token 时发生异常: {e}, URL: {table_url}")
        return None


def _preload_kol_ids_from_lark(lark_client: Lark, tables: List[Dict]) -> Set[str]:
    """
    从所有指定的飞书表中预加载所有KOL ID用于去重，使用多线程并发处理提高速度
    """
    existing_kol_ids = set()
    lock = threading.Lock()
    logger.info(f"🔍 开始从 {len(tables)} 个飞书表中预加载KOL ID用于去重...")

    def load_single_table(table: Dict) -> Set[str]:
        """
        加载单个飞书表的KOL ID
        """
        table_kol_ids = set()
        try:
            all_records = lark_client.search_table_all_records(
                app_token=table["app_token"],
                table_id=table["table_id"],
                search_params={"field_names": ["KOL ID"]},
                page_size=500
            )
            for record in all_records:
                fields = record.get("fields", {})
                kol_id_field = fields.get("KOL ID")

                # 只有在字段存在且为非空 list 的情况下才处理
                if isinstance(kol_id_field, list) and kol_id_field:
                    first_item = kol_id_field[0]
                    if isinstance(first_item, dict) and "text" in first_item:
                        kol_id = first_item["text"]
                        table_kol_ids.add(kol_id)

            logger.debug(f"📊 从表格 {table['table_id']} 加载了 {len(all_records)} 条记录，KOL ID数: {len(table_kol_ids)}")
            return table_kol_ids
        except Exception as e:
            logger.error(f"❌ 从表格 {table['table_id']} 加载KOL ID失败: {e}")
            return set()

    # 使用多线程并发处理所有表格
    max_workers = min(len(tables), 10)  # 最多10个线程，避免过多并发
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_table = {executor.submit(load_single_table, table): table for table in tables}

        # 收集结果
        for future in concurrent.futures.as_completed(future_to_table):
            table = future_to_table[future]
            try:
                table_kol_ids = future.result()
                with lock:
                    existing_kol_ids.update(table_kol_ids)
                    logger.debug(f"📊 表格 {table['table_id']} 处理完成，当前总KOL ID数: {len(existing_kol_ids)}")
            except Exception as e:
                logger.error(f"❌ 处理表格 {table['table_id']} 时发生异常: {e}")

    logger.info(f"✅ KOL ID预加载完成，总共 {len(existing_kol_ids)} 个唯一的KOL ID")
    return existing_kol_ids

def _store_kol_to_email_lark(item: Dict, query_params: Dict, lark_client: "Lark") -> bool:
    """
    有邮箱的 KOL 写入飞书邮箱表（查重后再写入）
    """
    try:
        app_token = 'Mo7obvrgtaIMqRsZkdwc6UUFnCg'
        table_id = 'tblUdyWvC0X5Sg9k'

        kol_id = item.get("KOL ID", "")
        if not kol_id:
            logger.error("❌ KOL ID 为空，无法发送数据")
            return False

        # a. 查重
        search_params = {
            "filter": {
                "conjunction": "and",
                "conditions": [
                    {"field_name": "KOL ID", "operator": "is", "value": [kol_id]}
                ]
            }
        }
        matching_records = lark_client.search_table_record(app_token, table_id, search_params)
        if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
            logger.debug(f"⏭️ KOL ID '{kol_id}' 已存在于邮件表，跳过发送")
            return True

        # b. 构建写入字段
        fields = {
            "KOL ID": kol_id,
            "Email": item.get("Email"),
            "Account link": item.get("Account link"),
            "KOL name": item.get("KOL Name"),
            "Template": query_params.get("template"),
            "App Code": query_params.get("project_code")
        }

        # 过滤掉空字段
        fields = {
            k: v for k, v in fields.items()
            if v not in (None, "", [], {}) and not (isinstance(v, str) and v.strip() == "")
        }

        result = lark_client.batch_create_table_records(app_token, table_id, [fields])
        if result:
            logger.debug(f"✅ 成功将 KOL '{kol_id}' 写入飞书邮件表")
            return True
        else:
            logger.error(f"❌ 发送 KOL '{kol_id}' 到飞书邮件表失败")
            return False

    except Exception as e:
        logger.exception(f"❌ 发送 KOL '{item.get('KOL Name', '')}' 到飞书邮件表异常: {str(e)}")
        return False

def _store_video_to_db(item: Dict, video: Dict) -> None:
    """
    视频数据写入数据库，逻辑与V2一致。
    """
    try:
        video_request_body = {
            "kol_id": item.get("kol_id", ""),
            "play_count": video.get("play_count", 0),
            "is_pinned": video.get("is_pinned", False),
            "share_url": video.get("share_url", ""),
            "desc": video.get("desc", ""),
            "desc_language": video.get("desc_language", ""),
            "video_url": video.get("video_url", ""),
            "music_url": video.get("music_url", ""),
            "likes_count": video.get("likes_count", 0),
            "comments_count": video.get("comments_count", 0),
            "shares_count": video.get("shares_count", 0),
            "collect_count": video.get("collect_count", 0),
            "platform": item.get("platform", ""),
            "hashtags": video.get("hashtags", []),
            "create_time": video.get("create_time"),
            "video_id": video.get("video_id", "")
        }
        if not video_request_body["video_id"]:
            logger.debug(f"⚠️ 跳过无效视频ID的视频数据")
            return
        video_response = requests.post(
            "http://54.84.111.234:8000/api/v1/videos",
            json=convert_datetime(video_request_body),
            timeout=30
        )
        if video_response.status_code == 200:
            logger.debug(f"✅ 视频数据存储成功: {video_request_body['video_id']}")
        else:
            logger.error(f"❌ 视频数据存储失败: {video_response.status_code} - {video_response.text}")
    except Exception as e:
        logger.exception(f"❌ 存储视频数据时发生异常: {str(e)}")


def send_slack_message(channel, text):
    requestBody = {
        "channel": channel,
        "blocks": [
            {"type": "section", "text": {"type": "mrkdwn", "text": text}}
        ]
    }
    header = {
        "Authorization": "Bearer *******************************************************"
    }
    try:
        response = requests.post("https://slack.com/api/chat.postMessage", headers=header, json=requestBody, timeout=10)
        response.raise_for_status()
        logger.debug(f"📤 Slack消息发送成功: {text[:50]}...")
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ 发送Slack消息失败: {e}")


if __name__ == "__main__":
    # 解析命令行参数，获取Slack频道ID
    parser = argparse.ArgumentParser(description='KOL数据采集工具')
    parser.add_argument('--channel_id', type=str, default="U05PD11UDPW", help='Slack频道ID')
    args = parser.parse_args()
    channel_id = args.channel_id

    # 初始化Lark客户端
    lark_client = Lark()

    # 主流程采用for循环，每轮处理所有待办任务，结构与V2一致
    for round_idx in range(2):
        try:
            logger.info(f"🔄 ========== 开始第 {round_idx + 1} 轮任务检查 ==========")
            
            # 查询今日未处理的任务
            items = lark_client.search_table_all_records(
                "PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya",
                search_params={
                    "filter": {
                        "conjunction": "and",
                        "conditions": [
                            {"field_name": "Date", "operator": "is", "value": ["Today"]},
                            {"field_name": "点击", "operator": "is", "value": ["false"]}
                        ]
                    }
                },
                page_size=100
            )

            # 如果没有待处理任务，发送Slack通知并退出
            if not items:
                logger.info("ℹ️ 未找到今日待处理任务，脚本执行结束")
                send_slack_message(channel_id, "*任务检查完成:* ✅ 未发现今日待处理任务。")
                break

            # 处理每条任务记录，组装参数
            processed_data = []
            for entry in items:
                fields = entry.get("fields", {})
                filter_criteria = fields.get("Filter Criteria", [])
                cookie_list = fields.get("Cookie", [])
                query_params = {
                    "source": fields.get("Source", ""),
                    "platform": fields.get("Platform", ""),
                    "filter_name": fields.get("Filter Name", [{}])[0].get("text", ""),
                    "filter_body": "".join([fc.get("text", "") for fc in filter_criteria]),
                    "cookie": "; ".join(
                        [c.get("text", "").strip().rstrip(";") for c in cookie_list if c.get("type") == "text"]),
                    "project_code": fields.get("Project Code", ""),
                    "auto_send": fields.get("Auto Send", ""),
                    "template": fields.get("Email Template", ""),
                    "high_potential": fields.get("High Pontential", ""),
                    "table_save": fields.get("high potential存储表", {}).get("link", ""),
                    "table_duplicate": [
                        link["link"] if isinstance(link, dict) else link
                        for link in fields.get("high potential去重表", [])
                        if link and (isinstance(link, str) or "link" in link)
                    ],
                    # 传递Slack频道ID
                    "channel_id": channel_id
                }
                processed_data.append({"query_params": query_params, "record_id": entry["record_id"]})

            logger.info(f"📋 本轮共有 {len(processed_data)} 条filter执行任务")
            send_slack_message(channel_id, f"*本轮任务数:* {len(processed_data)} 条，开始执行...")

            # 依次处理每个任务
            for idx, item in enumerate(processed_data, 1):
                filter_name = item['query_params']['filter_name']
                if filter_name != "Modash-INS-anime related-bio-10k-200k-E-333":
                     continue
                logger.info(f"🎯 ========== 执行第 {idx}/{len(processed_data)} 个任务: {filter_name} ==========")
                send_slack_message(channel_id, f"*开始执行:* 🚀 ` {filter_name} `")

                result = collect_kol_data_v3(item['query_params'], lark_client, item['record_id'])

                logger.info(f"✅ 任务完成: {filter_name} - {result.get('message')}")
                send_slack_message(channel_id, f"*执行结果:* 🏁 ` {filter_name} ` - {result.get('message')}")
                logger.info(f"📊 任务统计: {filter_name} - 成功:{result.get('success_count', 0)}, 失败:{result.get('failed_count', 0)}, 跳过:{result.get('skipped_count', 0)}")

            logger.info("🎉 ========== 本轮所有任务执行完毕 ==========")
            send_slack_message(channel_id, f"*全部任务执行结束:* 🎉")
            break
        except Exception as e:
            logger.exception(f"❌ 主循环发生严重错误: {e}")
            send_slack_message(channel_id, f"*严重错误:* ❌ 脚本执行异常，请检查日志。错误: {e}")
            # 发生异常后直接退出，不再死循环
            break
