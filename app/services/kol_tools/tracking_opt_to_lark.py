import os
import asyncio
import logging
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from logging.handlers import RotatingFileHandler
import instaloader

# # 创建日志目录
log_dir = "logs"
# os.makedirs(log_dir, exist_ok=True)
#
# # 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[
        # 控制台输出
        logging.StreamHandler(),
        # 文件输出（按大小轮转）
        RotatingFileHandler(
            filename=os.path.join(log_dir, "tiktok_sync.log"),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
    ]
)
logger = logging.getLogger(__name__)

# 飞书配置
FEISHU_CONFIG = {
    "auth": {
        "app_id": "cli_a50254e4e43bd013",
        "app_secret": "0PStyxmXLUnUKq5KvS5iCbmg8z4z6nkJ",
    },
    "apps": {
        "performance": {
            "app_token": "Io0rbbGEsaHNc2sIxdncYnDOnUd",
            "tables": {
                "performance": "tbl12d9OhyRORvvw",
            },
        },
        "payouts": {
            "app_token": "QRIqbU7VNaDTRQsrsmecoCu7nah",
            "tables": {
                "status": "tblbdRR3PCYQXTGe",
            },
        },
        "candidates": {
            "app_token": "AC42blhOTaXvSvs6Z1ScczGlnmg",
            "tables": "tbleR29Dw3dAgVpQ",
        }
    },
    "kol_tables": [
        ('KKd0bPAgJaKHHlsQlfRcbHIfnJg', 'tblJLHf1FYCuXeum', 'TK'),  # TK
        ('Ozj1bnsW8awu6lswMzgclzLfnIf', 'tblL7YsTpfFwiSvG', 'TK'),  # TK
        ('Ozj1bnsW8awu6lswMzgclzLfnIf', 'tblcIuGkxxR4Rvyw', 'TK'),  # TK
        ('Ozj1bnsW8awu6lswMzgclzLfnIf', 'tblpubK6Nu9Np6Uy', 'TK'),  # TK
        ('Ozj1bnsW8awu6lswMzgclzLfnIf', 'tblEoA6lFSkjDIXQ', 'TK'),  # TK
        ('Ozj1bnsW8awu6lswMzgclzLfnIf', 'tblEO0zc6bB2CZxz', 'TK'),  # TK
        ('JLNXbjpRLaXZcdsigkIcqddSnIh', 'tblXSAtZm1wSUyyK', 'INS'),  # Ins
    ],
}


class FeishuClient:
    """飞书多维表格操作客户端"""

    def __init__(self):
        self._session = self._create_retry_session()
        self.tenant_access_token = ""
        self.token_expire_time = datetime.min

    def _create_retry_session(self) -> requests.Session:
        """创建带重试机制的Session"""
        session = requests.Session()
        retry = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504],
            allowed_methods=["GET", "POST", "PUT"],
        )
        adapter = HTTPAdapter(max_retries=retry)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session

    def _check_token_valid(self) -> bool:
        """检查token是否有效"""
        return datetime.now() < self.token_expire_time

    def _refresh_token(self) -> None:
        """刷新tenant_access_token"""
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        payload = {
            "app_id": FEISHU_CONFIG["auth"]["app_id"],
            "app_secret": FEISHU_CONFIG["auth"]["app_secret"],
        }

        try:
            response = self._session.post(url, json=payload)
            response.raise_for_status()
            data = response.json()
            if data.get("code") != 0:
                raise ValueError(f"获取token失败: {data.get('msg')}")

            self.tenant_access_token = data["tenant_access_token"]
            self.token_expire_time = datetime.now() + timedelta(
                seconds=data["expire"] - 600
            )
            logger.info("飞书token刷新成功")
        except Exception as e:
            logger.error(f"刷新飞书token失败: {str(e)}")
            raise

    def _ensure_token(self) -> None:
        """确保token有效"""
        if not self._check_token_valid():
            self._refresh_token()

    def search_records(
            self,
            app_token: str,
            table_id: str,
            field_names: List[str],
            filter_conditions: Dict,
            page_size: int = 500,
    ) -> List[Dict]:
        """搜索表格记录"""
        self._ensure_token()
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/search"
        headers = {"Authorization": f"Bearer {self.tenant_access_token}"}

        all_records = []
        page_token = None

        while True:
            # 创建请求参数
            payload = {
                "field_names": field_names,
                "filter": filter_conditions,
                "page_size": page_size
            }

            # 如果存在page_token，将它添加到URL的查询参数中
            if page_token:
                url_with_token = f"{url}?page_token={page_token}&page_size={page_size}"
            else:
                url_with_token = f"{url}?page_size={page_size}"

            try:
                logger.info(f"搜索记录请求: {payload}，请求URL: {url_with_token}")
                response = self._session.post(url_with_token, headers=headers, json=payload)
                response.raise_for_status()
                data = response.json()

                if data["code"] != 0:
                    logger.error(f"搜索记录失败: {data.get('msg')}")
                    break

                records = data["data"].get("items", [])
                all_records.extend(records)
                logger.info(f"本页获取到{len(records)}条记录")

                if data["data"].get("has_more"):
                    page_token = data["data"].get("page_token")
                    logger.info(f"继续获取下一页，token: {page_token}")
                else:
                    break
            except Exception as e:
                logger.error(f"搜索记录异常: {str(e)}")
                break

        logger.info(f"共搜索到{len(all_records)}条记录")
        return all_records

    def batch_update_records(
            self, app_token: str, table_id: str, records: List[Dict]
    ) -> bool:
        """批量更新记录"""
        self._ensure_token()
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_update"
        headers = {"Authorization": f"Bearer {self.tenant_access_token}"}

        try:
            logger.info(f"批量更新{len(records)}条记录")
            response = self._session.post(url, headers=headers, json={"records": records})
            response.raise_for_status()
            result = response.json()

            if result["code"] == 0:
                logger.info(f"批量更新成功")
                return True
            logger.error(f"批量更新失败: {result.get('msg')}")
            return False
        except Exception as e:
            logger.error(f"批量更新异常: {str(e)}")
            return False

    def batch_create_records(
            self, app_token: str, table_id: str, records: List[Dict]
    ) -> bool:
        """批量创建记录"""
        self._ensure_token()
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_create"
        headers = {"Authorization": f"Bearer {self.tenant_access_token}"}

        try:
            logger.info(f"批量创建{len(records)}条记录")
            response = self._session.post(url, headers=headers, json={"records": records})
            response.raise_for_status()
            result = response.json()

            if result["code"] == 0:
                logger.info(f"批量创建成功")
                return True
            logger.error(f"批量创建失败: {result.get('msg')}")
            return False
        except Exception as e:
            logger.error(f"批量创建异常: {str(e)}")
            return False


class InstagramProcessor:
    """Instagram数据处理类"""

    def __init__(self):
        self.loader = instaloader.Instaloader()
        self.max_retries = 3
        self.base_delay = 1

    async def get_video_stats(self, video_url: str) -> Optional[Dict]:
        """获取Instagram视频统计数据"""
        logger.info(f"开始获取Instagram视频数据: {video_url}")

        try:
            shortcode = self._extract_shortcode(video_url)
        except ValueError as e:
            logger.error(f"无效的Instagram URL: {str(e)}")
            return None

        for attempt in range(self.max_retries + 1):
            try:
                # 使用异步包装同步操作
                post = await asyncio.to_thread(
                    self._get_post_info,
                    shortcode
                )

                if not post:
                    raise ValueError("未找到视频信息")

                # 获取作者信息和粉丝数
                profile = post.owner_profile

                # 处理特殊值
                play_count = post.video_view_count if post.video_view_count is not None else 0
                likes = post.likes if post.likes >= 0 else 0
                comments = post.comments if post.comments >= 0 else 0
                # 将粉丝数转换为K为单位（四舍五入到1位小数）
                followers = round(profile.followers / 1000, 1) if profile.followers is not None else 0

                stats = {
                    "video_id": post.shortcode,
                    "create_time": int(post.date_local.timestamp()),
                    "platform": "instagram",
                    "stats": {
                        "play_count": play_count,
                        "digg_count": likes,
                        "comment_count": comments,
                        "collect_count": play_count,  # 使用播放数代替
                        "follower_count": followers,  # 新增粉丝数字段
                    },
                }

                logger.info(f"获取Instagram视频数据成功: ID={stats['video_id']}")
                return stats

            except Exception as e:
                if attempt < self.max_retries:
                    wait_time = self.base_delay * (2 ** attempt)
                    logger.warning(f"请求失败，第{attempt + 1}次重试（{wait_time}秒后）: {str(e)}")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"获取Instagram数据失败（已重试{self.max_retries}次）: {str(e)}")
                    return None

    def _extract_shortcode(self, url: str) -> str:
        """从URL中提取视频shortcode"""
        # 处理多种Instagram URL格式
        # https://www.instagram.com/p/{shortcode}/
        # https://www.instagram.com/reel/{shortcode}/
        import re
        patterns = [
            r'instagram\.com/p/([^/]+)',
            r'instagram\.com/reel/([^/]+)',
        ]

        for pattern in patterns:
            if match := re.search(pattern, url):
                return match.group(1)

        raise ValueError(f"无法从URL中提取shortcode: {url}")

    def _get_post_info(self, shortcode: str) -> Optional[instaloader.Post]:
        """获取帖子信息"""
        try:
            return instaloader.Post.from_shortcode(
                self.loader.context,
                shortcode
            )
        except Exception as e:
            logger.error(f"获取帖子信息失败: {str(e)}")
            return None


class TikTokProcessor:
    """TikTok数据处理类（带重试机制）"""

    def __init__(self):
        """初始化TikTok处理器"""
        self.max_retries = 3  # 最大重试次数
        self.base_delay = 1  # 基础等待时间（秒）
        self.base_url = "https://douyin.wtf/api/tiktok"

    async def _make_request(self, url: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """
        发送HTTP请求（带重试机制）

        Args:
            url: 请求URL
            params: URL参数

        Returns:
            Optional[Dict]: 响应数据，请求失败时返回None
        """
        for attempt in range(self.max_retries + 1):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            raise Exception(f"HTTP {response.status}: {await response.text()}")

            except Exception as e:
                if attempt < self.max_retries:
                    wait_time = self.base_delay * (2 ** attempt)
                    logger.warning(
                        f"请求失败，第{attempt + 1}次重试（{wait_time}秒后）: {str(e)}"
                    )
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(
                        f"请求失败（已重试{self.max_retries}次）: {str(e)}",
                        exc_info=True
                    )
                    return None

    async def get_aweme_id(self, video_url: str) -> Optional[str]:
        """
        从视频URL获取作品ID

        Args:
            video_url: TikTok视频URL

        Returns:
            Optional[str]: 作品ID，获取失败时返回None
        """
        logger.info(f"开始获取视频作品ID: {video_url}")

        url = f"{self.base_url}/web/get_aweme_id"
        response = await self._make_request(url, params={"url": video_url})

        if response and response.get("code") == 200:
            aweme_id = response.get("data")
            if aweme_id:
                logger.info(f"获取作品ID成功: {aweme_id}")
                return aweme_id
        return None

    async def get_video_data(self, aweme_id: str) -> Optional[Dict]:
        """
        通过作品ID获取视频数据

        Args:
            aweme_id: 视频作品ID

        Returns:
            Optional[Dict]: 视频数据，获取失败时返回None
        """
        logger.info(f"开始获取视频数据: {aweme_id}")

        url = f"{self.base_url}/web/fetch_one_video"
        response = await self._make_request(url, params={"itemId": aweme_id})

        if response and response.get("code") == 200:
            data = response.get("data", {})
            item_struct = data.get("itemInfo", {}).get("itemStruct", {})
            stats = item_struct.get("stats", {})
            author_stats = item_struct.get("authorStats", {})

            # 将粉丝数转换为K为单位（四舍五入到1位小数）
            follower_count = round(author_stats.get("followerCount", 0) / 1000, 1)

            # 从createTime获取时间戳并转换为年/月/日格式
            timestamp = item_struct.get("createTime", 0)
            create_time = datetime.fromtimestamp(timestamp).strftime("%Y/%m/%d")

            # 构建返回数据
            stats_data = {
                "video_id": aweme_id,
                "create_time": create_time,
                "platform": "tiktok",
                "stats": {
                    "play_count": stats.get("playCount", 0),
                    "digg_count": stats.get("diggCount", 0),
                    "comment_count": stats.get("commentCount", 0),
                    "share_count": stats.get("shareCount", 0),
                    "collect_count": stats.get("collectCount", 0),
                    "follower_count": follower_count,
                },
            }
            logger.info(f"获取视频数据成功: ID={stats_data['video_id']}")
            return stats_data
        return None

    async def get_video_stats(self, video_url: str) -> Optional[Dict]:
        """
        获取视频统计数据（主方法）

        Args:
            video_url: TikTok视频URL

        Returns:
            Optional[Dict]: 视频统计数据，获取失败时返回None
        """
        # 1. 获取作品ID
        aweme_id = await self.get_aweme_id(video_url)
        if not aweme_id:
            logger.error("无法获取作品ID")
            return None

        # 2. 获取视频数据
        return await self.get_video_data(aweme_id)


class TikHubAPIClient:
    """TikHub API客户端 - 统一处理多平台视频数据获取"""
    
    def __init__(self, api_token: str = ""):
        """
        初始化TikHub API客户端
        
        Args:
            api_token: API认证令牌
        """
        self.api_token = api_token
        self.base_url = "https://api.tikhub.io/api/v1"
        self.headers = {
            "accept": "application/json",
            "Authorization": f"Bearer {api_token}"
        }
        self.max_retries = 3
        self.base_delay = 1
        self._session = self._create_retry_session()

    def _create_retry_session(self) -> requests.Session:
        """创建带重试机制的Session"""
        session = requests.Session()
        retry = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504],
            allowed_methods=["GET", "POST", "PUT"],
        )
        adapter = HTTPAdapter(max_retries=retry)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session

    def _make_request(self, url: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """
        发送HTTP请求（带重试机制）
        
        Args:
            url: 请求URL
            params: URL参数
            
        Returns:
            Optional[Dict]: 响应数据，请求失败时返回None
        """
        for attempt in range(self.max_retries + 1):
            try:
                response = self._session.get(url, headers=self.headers, params=params)
                response.raise_for_status()
                return response.json()
                            
            except Exception as e:
                if attempt < self.max_retries:
                    wait_time = self.base_delay * (2 ** attempt)
                    logger.warning(f"请求失败，第{attempt + 1}次重试（{wait_time}秒后）: {str(e)}")
                    import time
                    time.sleep(wait_time)
                else:
                    logger.error(f"请求失败（已重试{self.max_retries}次）: {str(e)}", exc_info=True)
                    return None

    def get_instagram_video_info(self, instagram_url: str) -> Optional[Dict]:
        """
        获取Instagram视频信息
        
        Args:
            instagram_url: Instagram视频URL
            
        Returns:
            Optional[Dict]: Instagram视频数据，获取失败时返回None
        """
        logger.info(f"开始获取Instagram视频信息: {instagram_url}")
        
        url = f"{self.base_url}/instagram/web_app/fetch_post_info_by_url"
        params = {"url": instagram_url}
        
        response = self._make_request(url, params)
        
        if response and response.get("code") == 200:
            data = response.get("data", {})
            logger.info(f"获取Instagram视频信息成功")
            return data
        else:
            logger.error(f"获取Instagram视频信息失败: {response}")
            return None

    def get_tiktok_video_info(self, aweme_id: str) -> Optional[Dict]:
        """
        获取TikTok视频信息
        
        Args:
            aweme_id: TikTok视频作品ID
            
        Returns:
            Optional[Dict]: TikTok视频数据，获取失败时返回None
        """
        logger.info(f"开始获取TikTok视频信息: {aweme_id}")
        
        url = f"{self.base_url}/tiktok/app/v3/fetch_one_video_v2"
        params = {"aweme_id": aweme_id}
        
        response = self._make_request(url, params)
        
        if response and response.get("code") == 200:
            data = response.get("data", {})
            logger.info(f"获取TikTok视频信息成功")
            return data
        else:
            logger.error(f"获取TikTok视频信息失败: {response}")
            return None

    def get_youtube_video_info(self, video_id: str, url_access: str = "normal", 
                              lang: str = "zh-CN", videos: str = "auto", 
                              audios: str = "auto", subtitles: bool = True, 
                              related: bool = True) -> Optional[Dict]:
        """
        获取YouTube视频信息
        
        Args:
            video_id: YouTube视频ID
            url_access: URL访问类型，默认为"normal"
            lang: 语言，默认为"zh-CN"
            videos: 视频类型，默认为"auto"
            audios: 音频类型，默认为"auto"
            subtitles: 是否包含字幕，默认为True
            related: 是否包含相关视频，默认为True
            
        Returns:
            Optional[Dict]: YouTube视频数据，获取失败时返回None
        """
        logger.info(f"开始获取YouTube视频信息: {video_id}")
        
        url = f"{self.base_url}/youtube/web/get_video_info"
        params = {
            "video_id": video_id,
            "url_access": url_access,
            "lang": lang,
            "videos": videos,
            "audios": audios,
            "subtitles": str(subtitles).lower(),
            "related": str(related).lower()
        }
        
        response = self._make_request(url, params)
        
        if response and response.get("code") == 200:
            data = response.get("data", {})
            logger.info(f"获取YouTube视频信息成功")
            return data
        else:
            logger.error(f"获取YouTube视频信息失败: {response}")
            return None

    def get_video_info_by_url(self, video_url: str) -> Optional[Dict]:
        """
        根据URL自动识别平台并获取视频信息
        
        Args:
            video_url: 视频URL
            
        Returns:
            Optional[Dict]: 视频数据，获取失败时返回None
        """
        logger.info(f"开始根据URL获取视频信息: {video_url}")
        
        # 识别平台类型
        if "instagram.com" in video_url:
            return self.get_instagram_video_info(video_url)
        elif "tiktok.com" in video_url:
            # 对于TikTok，需要先提取aweme_id
            aweme_id = self._extract_tiktok_aweme_id(video_url)
            if aweme_id:
                return self.get_tiktok_video_info(aweme_id)
            else:
                logger.error("无法从TikTok URL中提取aweme_id")
                return None
        elif "youtube.com" in video_url or "youtu.be" in video_url:
            # 从YouTube URL中提取video_id
            import re
            patterns = [
                r'(?:youtube\.com/watch\?v=|youtu\.be/)([a-zA-Z0-9_-]+)',
                r'youtube\.com/embed/([a-zA-Z0-9_-]+)'
            ]
            
            for pattern in patterns:
                if match := re.search(pattern, video_url):
                    video_id = match.group(1)
                    return self.get_youtube_video_info(video_id)
            
            logger.error("无法从YouTube URL中提取video_id")
            return None
        else:
            logger.error(f"不支持的平台URL: {video_url}")
            return None

    def _extract_tiktok_aweme_id(self, video_url: str) -> Optional[str]:
        """
        从TikTok URL中提取aweme_id
        
        Args:
            video_url: TikTok视频URL
            
        Returns:
            Optional[str]: aweme_id，提取失败时返回None
        """
        import re
        
        # TikTok URL模式
        patterns = [
            r'tiktok\.com/@[^/]+/video/(\d+)',
            r'tiktok\.com/v/(\d+)',
            r'tiktok\.com/t/([a-zA-Z0-9]+)',
            r'vm\.tiktok\.com/([a-zA-Z0-9]+)',
            r'vt\.tiktok\.com/([a-zA-Z0-9]+)'
        ]
        
        for pattern in patterns:
            if match := re.search(pattern, video_url):
                aweme_id = match.group(1)
                logger.info(f"从TikTok URL提取到aweme_id: {aweme_id}")
                return aweme_id
        
        logger.warning(f"无法从TikTok URL中提取aweme_id: {video_url}")
        return None


class DataSyncService:
    """数据同步服务"""

    def __init__(self):
        self.feishu_client = FeishuClient()
        self.tiktok_processor = TikTokProcessor()  # 使用TikTok处理器
        self.instagram_processor = InstagramProcessor()  # 使用Instagram处理器
        self.tikhub_client = TikHubAPIClient()  # 使用TikHub API客户端

    def _transform_payouts_record(self, record: Dict) -> Dict:
        """转换支付记录格式"""
        fields = record.get("fields", {})
        return {
            "fields": {
                "Post Link": {"link": fields["Post Link"]["link"]},
                "KOL ID": fields["KOL ID"][0]["text"],
                "Channel": fields["Channel"],
                "Project": fields["Project"],
                "Payment": fields["Payment"],
            }
        }

    def sync_new_records(self) -> None:
        """同步新记录到绩效表"""
        logger.info("开始同步新记录")
        try:
            filter_conditions = {
                "conjunction": "and",
                "conditions": [
                    {"field_name": "Sync Status", "operator": "is", "value": ["false"]},
                    {"field_name": "Post Link", "operator": "isNotEmpty", "value": []},
                    {"field_name": "KOL ID", "operator": "isNotEmpty", "value": []},
                    {"field_name": "Channel", "operator": "isNotEmpty", "value": []},
                    {"field_name": "Project", "operator": "isNotEmpty", "value": []},
                    {"field_name": "Payment", "operator": "isNotEmpty", "value": []},
                ],
            }

            records = self.feishu_client.search_records(
                app_token=FEISHU_CONFIG["apps"]["payouts"]["app_token"],
                table_id=FEISHU_CONFIG["apps"]["payouts"]["tables"]["status"],
                field_names=["KOL ID", "Channel", "Project", "Payment", "Post Link"],
                filter_conditions=filter_conditions,
            )

            if not records:
                logger.info("没有需要同步的新记录")
                return

            logger.info(f"发现{len(records)}条需要同步的新记录")
            transformed = [self._transform_payouts_record(r) for r in records]

            if self.feishu_client.batch_create_records(
                    app_token=FEISHU_CONFIG["apps"]["performance"]["app_token"],
                    table_id=FEISHU_CONFIG["apps"]["performance"]["tables"]["performance"],
                    records=transformed,
            ):
                update_records = [
                    {"record_id": r["record_id"], "fields": {"Sync Status": True}}
                    for r in records
                ]
                self.feishu_client.batch_update_records(
                    app_token=FEISHU_CONFIG["apps"]["payouts"]["app_token"],
                    table_id=FEISHU_CONFIG["apps"]["payouts"]["tables"]["status"],
                    records=update_records,
                )
                logger.info(f"成功同步{len(records)}条记录")
            else:
                logger.error("同步新记录失败")
        except Exception as e:
            logger.error(f"同步新记录时发生异常: {str(e)}", exc_info=True)

    async def process_performance_records(self) -> None:
        """处理绩效记录（优化版）"""
        logger.info("开始处理现有绩效记录")
        try:
            records = self.feishu_client.search_records(
                app_token=FEISHU_CONFIG["apps"]["performance"]["app_token"],
                table_id=FEISHU_CONFIG["apps"]["performance"]["tables"]["performance"],
                field_names=["Post Link", "KOL ID", "Post Date", "Followers(K)", "Level"],
                filter_conditions={"conjunction": "and", "conditions": []},
            )

            logger.info(f"共找到{len(records)}条需要处理的绩效记录")
            if not records:
                return
            candidates_reccords = self.feishu_client.search_records(
                app_token=FEISHU_CONFIG["apps"]["candidates"]["app_token"],
                table_id=FEISHU_CONFIG["apps"]["candidates"]["tables"],
                field_names=["KOL ID", "Source", "KOL Priority"],
                filter_conditions={"conjunction": "and",
                                   "conditions": [{"field_name": "KOL ID", "operator": "isNotEmpty", "value": []}]},
            )
            current_time = datetime.now()
            for idx, record in enumerate(records, 1):
                record_id = record.get("record_id", "UNKNOWN")
                try:
                    logger.info(f"正在处理记录 ({idx}/{len(records)}) [ID: {record_id}]")
                    await self._process_optimized_record(record, current_time, candidates_reccords)
                    await asyncio.sleep(5)
                except Exception as e:
                    logger.error(f"处理记录失败 [ID: {record_id}]: {str(e)}", exc_info=True)
        except Exception as e:
            logger.error(f"获取绩效记录失败: {str(e)}")
        finally:
            logger.info("绩效记录处理完成")

    async def _process_optimized_record(self, record: Dict, current_time: datetime, candidates_reccords) -> None:
        """优化后的单记录处理"""
        record_id = record["record_id"]
        fields = record.get("fields", {})
        post_link = fields.get("Post Link", {}).get("link")
        kol_id = fields.get("KOL ID", [{}])[0].get("text") if fields.get("KOL ID") else None

        if not post_link:
            logger.warning(f"跳过处理 [ID: {record_id}]: 缺少Post Link")
            return

        # 根据链接类型选择处理器
        processor = None
        platform = "unknown"
        if "tiktok.com" in post_link:
            processor = self.tiktok_processor
            platform = "TikTok"
        elif "instagram.com" in post_link:
            processor = self.instagram_processor
            platform = "Instagram"

        if not processor:
            logger.warning(f"不支持的平台链接 [ID: {record_id}]: {post_link}")
            return

        # 获取视频数据
        logger.info(f"使用{platform}处理器处理视频 [ID: {record_id}]")
        video_stats = await processor.get_video_stats(post_link)
        if not video_stats:
            logger.warning(f"获取{platform}视频数据失败 [ID: {record_id}]")
            return

        # 每天强制更新总统计字段
        self._update_total_stats(record_id, video_stats)

        # 处理日期逻辑
        post_date = self._parse_post_date(fields.get("Post Date"))
        if post_date:
            days_diff = (current_time.date() - post_date.date()).days
            logger.info(f"当前时间差: {days_diff}天 [ID: {record_id}]")

            # 仅在特定天数更新日统计
            if days_diff in [1, 3, 7]:
                logger.info(f"========开始更新数据========")
                self._update_stats(record_id, video_stats, days_diff)
        else:
            logger.info(f"初始化发布日期 [ID: {record_id}]")
            self._update_post_date(record_id, video_stats["create_time"])

            # 使用统一的日期解析方法
            create_date = self._parse_post_date(video_stats["create_time"])
            days_diff = (current_time.date() - create_date.date()).days
            logger.info(f"当前时间差: {days_diff}天 [ID: {record_id}]")

            # 仅在特定天数更新日统计
            if days_diff in [1, 3, 7]:
                logger.info(f"========开始更新数据========")
                self._update_stats(record_id, video_stats, days_diff)

        # 检查是否需要设置Level
        if "Level" not in fields:
            logger.info(f"记录缺少Level字段，进行初始化 [ID: {record_id}]")
            self._set_initial_level(record_id, video_stats["stats"]["follower_count"])
        else:
            logger.info(f"记录不缺少Level字段，不需要进行初始化 [ID: {record_id}]")

        # 更新source字段
        for candidate in candidates_reccords:
            if kol_id == candidate['fields']['KOL ID'][0]['text']:
                logger.info(f'找到需要更新source字段')
                fields_to_update = {
                    "Source": candidate['fields']['Source']
                }
                if "KOL Priority" in candidate['fields'] and candidate['fields']['KOL Priority']:
                    fields_to_update["KOL Priority"] = candidate['fields']['KOL Priority']

                resp = self.feishu_client.batch_update_records(
                    app_token=FEISHU_CONFIG["apps"]["performance"]["app_token"],
                    table_id=FEISHU_CONFIG["apps"]["performance"]["tables"]["performance"],
                    records=[{
                        "record_id": record_id,
                        "fields": fields_to_update
                    }]
                )
                logger.info(f'更新source字段：{resp}')
                logger.info(f'成功更新source字段：{candidate["fields"]["Source"]}')

    def _set_initial_level(self, record_id: str, follower_count: float) -> None:
        """只在初始化时设置KOL等级"""
        try:
            level = self._get_kol_level(follower_count)
            logger.info(f"设置初始Level [ID: {record_id}]: {level}")

            success = self.feishu_client.batch_update_records(
                app_token=FEISHU_CONFIG["apps"]["performance"]["app_token"],
                table_id=FEISHU_CONFIG["apps"]["performance"]["tables"]["performance"],
                records=[{
                    "record_id": record_id,
                    "fields": {"Level": level}
                }],
            )

            if success:
                logger.info(f"Level设置成功 [ID: {record_id}]")
            else:
                logger.warning(f"Level设置失败 [ID: {record_id}]")
        except Exception as e:
            logger.error(f"设置Level异常 [ID: {record_id}]: {str(e)}")

    def _update_total_stats(self, record_id: str, video_stats: Dict) -> None:
        """更新总统计字段"""
        try:
            # 基础字段（两个平台都有）
            follower_count = video_stats["stats"]["follower_count"]
            update_fields = {
                "Views-Total": video_stats["stats"]["play_count"],
                "Likes-Total": video_stats["stats"]["digg_count"],
                "Comments-Total": video_stats["stats"]["comment_count"],
                "Followers(K)": follower_count,
            }

            # TikTok数据时总是更新share字段（包括0值）
            if "share_count" in video_stats["stats"]:
                update_fields["Shares-Total"] = video_stats["stats"]["share_count"]

            logger.info(f"更新总统计字段 [ID: {record_id}]: {update_fields}")
            success = self.feishu_client.batch_update_records(
                app_token=FEISHU_CONFIG["apps"]["performance"]["app_token"],
                table_id=FEISHU_CONFIG["apps"]["performance"]["tables"]["performance"],
                records=[{
                    "record_id": record_id,
                    "fields": update_fields
                }],
            )

            if success:
                logger.info(f"总统计字段更新成功 [ID: {record_id}]")
            else:
                logger.warning(f"总统计字段更新失败 [ID: {record_id}]")
        except Exception as e:
            logger.error(f"更新总统计字段异常 [ID: {record_id}]: {str(e)}")

    def _parse_post_date(self, post_date_value: Any) -> Optional[datetime]:
        """解析多种日期格式"""
        if not post_date_value:
            return None

        try:
            # 处理毫秒时间戳
            if isinstance(post_date_value, (int, float)):
                if post_date_value > 1e12:  # 毫秒级时间戳
                    return datetime.fromtimestamp(post_date_value / 1000)
                return datetime.fromtimestamp(post_date_value)  # 秒级时间戳

            # 处理字符串格式
            if isinstance(post_date_value, str):
                if post_date_value.isdigit():
                    return datetime.fromtimestamp(int(post_date_value))
                if '/' in post_date_value:  # 处理 YYYY/MM/DD 格式
                    return datetime.strptime(post_date_value, "%Y/%m/%d")
                return datetime.fromisoformat(post_date_value.replace('Z', '+00:00'))

            logger.warning(f"未知的日期格式: {type(post_date_value)}")
            return None
        except Exception as e:
            logger.error(f"日期解析失败: {str(e)}")
            return None

    def _full_process_record(self, record_id: str, kol_id: str, video_stats: Dict) -> None:
        """新记录的完整处理流程"""
        # 更新发布日期
        self._update_post_date(record_id, video_stats["create_time"])

        # 更新统计信息
        self._update_stats(record_id, video_stats, datetime.now())

        # 更新创作者信息
        self._update_creator_info(record_id, kol_id)

    def _update_post_date(self, record_id: str, create_time: str) -> None:
        """更新发布日期"""
        try:
            # 处理不同时间格式
            post_date = self._parse_post_date(create_time)
            if not post_date:
                logger.error(f"无效的创建时间格式 [ID: {record_id}]")
                return

            post_date_ms = int(post_date.timestamp() * 1000)
            logger.info(f"更新发布日期 [ID: {record_id}]: {post_date}")

            success = self.feishu_client.batch_update_records(
                app_token=FEISHU_CONFIG["apps"]["performance"]["app_token"],
                table_id=FEISHU_CONFIG["apps"]["performance"]["tables"]["performance"],
                records=[{
                    "record_id": record_id,
                    "fields": {"Post Date": post_date_ms}
                }],
            )

            if success:
                logger.info(f"发布日期更新成功 [ID: {record_id}]")
            else:
                logger.warning(f"发布日期更新失败 [ID: {record_id}]")
        except Exception as e:
            logger.error(f"更新发布日期异常 [ID: {record_id}]: {str(e)}")

    def _update_stats(self, record_id: str, video_stats: Dict, days_diff: int) -> None:
        """按天数更新统计"""
        try:
            fields = {
                f"Views-Day {days_diff}": video_stats["stats"]["play_count"],
                f"Likes-Day {days_diff}": video_stats["stats"]["digg_count"],
                f"Comments-Day {days_diff}": video_stats["stats"]["comment_count"],
            }

            # 只有TikTok才有分享数
            if video_stats.get("platform") == "tiktok":
                fields[f"Shares-Day {days_diff}"] = video_stats["stats"]["share_count"]

            logger.info(f"更新第{days_diff}天统计字段 [ID: {record_id}]: {fields}")
            success = self.feishu_client.batch_update_records(
                app_token=FEISHU_CONFIG["apps"]["performance"]["app_token"],
                table_id=FEISHU_CONFIG["apps"]["performance"]["tables"]["performance"],
                records=[{
                    "record_id": record_id,
                    "fields": fields
                }],
            )

            if success:
                logger.info(f"{days_diff}天统计更新成功 [ID: {record_id}]")
            else:
                logger.warning(f"{days_diff}天统计更新失败 [ID: {record_id}]")
        except Exception as e:
            logger.error(f"更新统计异常 [ID: {record_id}]: {str(e)}")

    def _update_creator_info(self, record_id: str, kol_id: str) -> None:
        """更新创作者信息"""
        if not kol_id:
            logger.warning(f"缺少KOL ID [ID: {record_id}]")
            return

        logger.info(f"查询创作者信息 [ID: {record_id} KOL: {kol_id}]")
        try:
            creator_info = []
            for app_token, table_id, _ in FEISHU_CONFIG["kol_tables"]:
                result = self.feishu_client.search_records(
                    app_token=app_token,
                    table_id=table_id,
                    field_names=["KOL ID", "Followers(K)"],
                    filter_conditions={
                        "conjunction": "and",
                        "conditions": [
                            {"field_name": "KOL ID", "operator": "is", "value": [kol_id]}
                        ]
                    },
                )
                if result:
                    creator_info.extend(result)
                    logger.info(f"在表{table_id}中找到{len(result)}条记录")
                    break  # 找到即停止搜索

            if not creator_info:
                logger.warning(f"未找到创作者信息 [ID: {record_id}]")
                return

            fields = creator_info[0].get("fields", {})
            update_fields = {}

            if "Followers(K)" in fields:
                update_fields["Followers(K)"] = fields["Followers(K)"]
                logger.info(f"更新粉丝数: {fields['Followers(K)']}K")

            if update_fields:
                success = self.feishu_client.batch_update_records(
                    app_token=FEISHU_CONFIG["apps"]["performance"]["app_token"],
                    table_id=FEISHU_CONFIG["apps"]["performance"]["tables"]["performance"],
                    records=[{
                        "record_id": record_id,
                        "fields": update_fields
                    }],
                )

                if success:
                    logger.info(f"创作者信息更新成功 [ID: {record_id}]")
                else:
                    logger.warning(f"创作者信息更新失败 [ID: {record_id}]")
            else:
                logger.info(f"无需更新创作者信息 [ID: {record_id}]")
        except Exception as e:
            logger.error(f"更新创作者信息异常 [ID: {record_id}]: {str(e)}")

    def _get_kol_level(self, follower_count: float) -> str:
        """根据粉丝数(K)确定KOL等级"""
        if follower_count < 10:
            return "Nano 1~10k "
        elif follower_count < 50:
            return "Micro 10~50k"
        else:
            return "Mid-tier 50~500k"


def modash_util(kol_id, channel):
    url = "https://fkwdo6tceerhxqtv5fhokkoxmi0lgpva.lambda-url.eu-central-1.on.aws/"
    params = {
        "operation": "get-profile",
        "username": f"%40{kol_id}",
        "channel": f"{channel}"
    }
    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "keep-alive",
        "Origin": "https://www.modash.io",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "cross-site",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36",
        "sec-ch-ua": '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "macOS"
    }

    response = requests.get(url, headers=headers, params=params)

    if response.status_code == 200:
        print(response.json())
    else:
        print(f"Request failed with status code {response.status_code}: {response.text}")


def fte_compute(project, task, num=None):
    """发送 FTE 计算请求"""
    url = "https://eojwgxpm0t7clzl.m.pipedream.net"
    body = {
        "task": task,
        "project": project
    }
    if num:
        body["number"] = num

    requests.post(url, json=body)


async def main():
    """主函数"""
    service = DataSyncService()

    try:
        logger.info("========== 开始数据同步 ==========")
        # 第一步：同步新记录
        service.sync_new_records()

        # 第二步：处理现有记录
        await service.process_performance_records()

        # 计算FTE
        fte_compute('UA_KOL', 'UA_KOL_Performance_Tracking')
        logger.info("========== 数据处理完成 ==========")
    except Exception as e:
        logger.error(f"主流程异常: {str(e)}", exc_info=True)
    finally:
        logger.info("程序执行结束")


async def run_twice():
    await main()


if __name__ == "__main__":
    # async def test_instagram():
    #     processor = InstagramProcessor()
    #     urls = [
    #         'https://www.instagram.com/reel/DFvebJoS8oz/?igsh=MW5zZjJuanowM2hjZA==',
    #         # 'https://www.instagram.com/reel/DFdSsqTPi01/'
    #     ]
    #
    #     for url in urls:
    #         print(f"\n测试链接: {url}")
    #         result = await processor.get_video_stats(url)
    #         print("获取到的数据:", result)
    #         await asyncio.sleep(2)  # 添加延迟避免请求过快
    #
    # asyncio.run(test_instagram())
    # asyncio.run(main())
    asyncio.run(run_twice())


class InstagramAPIClient:
    """
    Instagram API客户端类

    支持简单的调用方式：

    ```python
    # 方式1：使用 with 语句
    with InstagramAPIClient(username) as client:
        user_info = client.fetch_user_info()
        reels = client.fetch_user_reels(count=20)

    # 方式2：手动管理资源
    client = InstagramAPIClient(username)
    try:
        user_info = client.fetch_user_info()
        reels = client.fetch_user_reels(count=20)
    finally:
        client.cleanup_sync()
    ```
    """
    # API 配置常量
    API_USER_INFO = "https://api.tikhub.io/api/v1/instagram/web_app/fetch_user_info_by_username_v2"
    API_USER_REELS = "https://api.tikhub.io/api/v1/instagram/web_app/fetch_user_reels_by_username_v3"
    API_TOKEN = "Bearer xacqaFZO6SIXljVf6mKMul9v/SEVtrzmuotC+=="  # KOL

    def __init__(self, username: str):
        """
        初始化API客户端实例

        Args:
            username: Instagram 用户名
        """
        self._username = username
        self._session: Optional[requests.Session] = None
        self._initialized: bool = False

    def __enter__(self) -> "InstagramAPIClient":
        """上下文管理器入口"""
        self.init_sync()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器退出"""
        self.cleanup_sync()

    @classmethod
    def create(cls, username: str) -> "InstagramAPIClient":
        """
        创建API客户端实例的工厂方法

        Args:
            username: Instagram 用户名

        Returns:
            InstagramAPIClient 实例
        """
        instance = cls(username)
        instance.init_sync()
        return instance

    def init_sync(self) -> None:
        """初始化API客户端资源"""
        if not self._initialized:
            self._session = requests.Session()
            self._session.headers.update({
                "accept": "application/json",
                "Authorization": self.API_TOKEN,
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                              "AppleWebKit/537.36 (KHTML, like Gecko) "
                              "Chrome/122.0.0.0 Safari/537.36",
            })
            self._initialized = True

    def cleanup_sync(self) -> None:
        """清理API客户端资源"""
        if self._session:
            self._session.close()
            self._session = None
        self._initialized = False

    def fetch_user_info(self) -> dict:
        """
        获取Instagram用户信息

        Returns:
            用户信息字典

        Raises:
            ValueError: 获取用户信息失败
        """
        if not self._initialized:
            self.init_sync()
        try:
            response = self._session.get(
                self.API_USER_INFO,
                params={"username": self._username},
                timeout=30,
            )
            response.raise_for_status()
            data = response.json()
            if "data" not in data or not data["data"]:
                raise ValueError("API返回数据格式错误或用户不存在")
            return data["data"]
        except requests.RequestException as e:
            logger.error(f"获取Instagram用户信息失败: {str(e)}")
            raise ValueError(f"获取Instagram用户信息失败: {str(e)}")

    def fetch_user_reels(self, count: int = 20) -> list:
        """
        获取Instagram用户Reels视频列表

        Args:
            count: 获取视频数量，默认为20

        Returns:
            Reels视频列表

        Raises:
            ValueError: 获取Reels失败
        """
        if not self._initialized:
            self.init_sync()
        try:
            response = self._session.get(
                self.API_USER_REELS,
                params={"username": self._username, "count": count},
                timeout=30,
            )
            response.raise_for_status()
            data = response.json()
            if "data" not in data or not data["data"]:
                raise ValueError("API返回数据格式错误或没有Reels")
            return data["data"]
        except requests.RequestException as e:
            logger.error(f"获取Instagram用户Reels失败: {str(e)}")
            raise ValueError(f"获取Instagram用户Reels失败: {str(e)}")
