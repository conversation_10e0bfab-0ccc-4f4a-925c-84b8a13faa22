#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project ：KOL-python
@File    ：kol_service
<AUTHOR>
@Date    ：2025/4/9 下午4:39 
'''
from typing import Dict
from app.db.session import SessionLocal
from app.services.tools import send_webhook
from app.logging_config import get_task_logger

# 从 tools.py 导入必要的函数
from app.services.kol_tools.tools import (
    _validate_params,
    _collect_source_data,
    _standardize_data,
    _collect_platform_data,
    _store_data_to_db,
    _store_high_potential_data,
    _send_to_feishu
)

# 使用任务日志记录器
logger = get_task_logger("app.worker.tasks.kol_data")


def collect_kol_data(query_params: Dict) -> Dict:
    """
    直接调用KOL数据采集流程的函数，不依赖于BaseTask

    Args:
        query_params: 查询参数，包含所需的数据源、平台等信息

    Returns:
        包含任务结果的字典
    """
    try:
        logger.info(f"开始采集平台 {query_params.get('platform')} 的KOL数据")
        
        # 参数校验
        source = query_params.get("source", "").lower()
        platform = query_params.get("platform", "").lower()
        filter_name = query_params.get("filter_name", "")
        project_code = query_params.get("project_code", "")
        auto_send = query_params.get("auto_send", "no")
        high_potential = query_params.get("high_potential", "no")
        table_save = query_params.get("table_save", "")
        table_duplicate = query_params.get("table_duplicate", "")

        # 参数验证
        _validate_params(source, platform)
        
        # 发送开始通知
        send_webhook(filter_name=filter_name, status="进行中: 0%")

        # 创建数据库会话
        db = SessionLocal()

        try:
            # 1. 采集原始数据
            logger.info(f"[1/6] 从数据源 {source} 采集原始数据")
            raw_data = _collect_source_data(source, query_params)
            if not raw_data:
                logger.error(f"从数据源 {source} 获取数据失败")
                send_webhook(filter_name=filter_name, status="失败")
                return {"status": "error", "message": f"从数据源 {source} 获取数据失败"}
            
            send_webhook(filter_name=filter_name, status=f"进行中: 20%")

            # 2. 数据标准化处理
            logger.info(f"[2/6] 数据标准化处理")
            standardized_data = _standardize_data(raw_data, source, platform)
            if not standardized_data:
                logger.error("数据标准化处理失败")
                send_webhook(filter_name=filter_name, status="失败")
                return {"status": "error", "message": "数据标准化处理失败"}

            # 3. 采集平台详细数据
            logger.info(f"[3/6] 采集平台 {platform} 详细数据")

            platform_data = _collect_platform_data(platform, standardized_data, query_params)
            if not platform_data:
                logger.error(f"从平台 {platform} 获取数据失败")
                send_webhook(filter_name=filter_name, status="失败")
                return {"status": "error", "message": f"从平台 {platform} 获取数据失败"}
            
            send_webhook(filter_name=filter_name, status=f"进行中: 40%")

            # 4. 存储数据到数据库
            logger.info(f"[4/6] 存储数据到数据库")
            stored_data = _store_data_to_db(db, platform_data, query_params)
            send_webhook(filter_name=filter_name, status=f"进行中: 60%")

            # 5. 高潜力KOL处理
            logger.info(f"[5/6] 高潜力KOL处理")
            records = []
            if high_potential.lower() == "yes":
                result, records = _store_high_potential_data(filter_name, project_code, stored_data, table_save, table_duplicate)
                if result:
                    logger.info(f"成功将 {len(records)} 条数据存储到<高潜力>飞书表")
                else:
                    logger.error("存储数据到<高潜力>飞书表失败")
            
            send_webhook(filter_name=filter_name, status=f"进行中: 80%")

            # 6. 自动发送邮件处理
            logger.info(f"[6/6] 自动发送邮件处理")
            email_count = 0
            if auto_send.lower() == "yes":
                result, email_count = _send_to_feishu(stored_data, query_params, records)
                if result:
                    logger.info(f"成功将 {email_count} 条数据发送到<自动发邮件>飞书表")
                else:
                    logger.error("发送数据到<自动发邮件>飞书表失败")

            # 完成
            logger.info(f"完成平台 {platform} 的KOL数据采集，共采集数据 {len(stored_data)} 条")
            send_webhook(filter_name=filter_name, status="已完成",
                         msg=f"总采集数:{len(stored_data)}条", unique_count=len(records), email_count=email_count)

            return {
                "status": "success",
                "message": f"成功采集数据 {len(stored_data)} 条",
                "data_count": len(stored_data),
                "unique_count": len(records),
                "email_count": email_count
            }

        except Exception as e:
            logger.exception(f"采集KOL数据时发生错误: {str(e)}")
            send_webhook(filter_name=filter_name, status="失败", msg=str(e))
            return {"status": "error", "message": str(e)}

        finally:
            db.close()

    except Exception as e:
        logger.exception(f"执行采集KOL数据时发生错误: {str(e)}")
        return {"status": "error", "message": str(e)}


if __name__ == "__main__":
    # 使用命令行参数
    cookie = 'eyJhbGciOiJSUzI1NiIsImtpZCI6IjcxMTE1MjM1YTZjNjE0NTRlZmRlZGM0NWE3N2U0MzUxMzY3ZWViZTAiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.skUOQQ1u6VQEppJm49ajHHgxGUuyZlY4W69GiTMXZ-U03qIWBCQlpStrA_d1xwKQGdoDFInaioy1gQMZzbmlT6zgR_WncPvujgWZSwVy_KHGYnrylN8LcFeiLvFJNiuAdo7lypqX0eeSzaoy6CIvWTYBp-ra3U8KTsfT58glK01PXAYwpf_ECWL_2W20yFEqgEpDJUMh5okJarSicPzlimWcB5IFj-ZCCb8cSIFpSPq-GfDXaAsQjN6YT3id_i8Xs3xaHXhLWRF5Il0xsvcTpj8o7D3PVywZ3iZRKkLhNnHOLEVcao4EyyvjElHrMnTGll4s5dVwj85Td6Mx09Ei3Q'
    filter_body = {
          "page": 0,
          "organizationUID": "CVDSvv2soiVJjRHusX58",
          "filter": {
            "followers": {
              "left_number": 1000,
              "right_number": 500000
            },
            "views": {
              "left_number": 10000,
              "right_number": 200000
            },
            "gender": {
              "code": "FEMALE"
            },
            "geo": [
              {
                "id": 148838
              }
            ],
            "audience_lang": {
              "code": "en",
              "weight": 0.5
            },
            "audience_age": [
              {
                "code": "25-34",
                "weight": 0.05
              }
            ],
            "audience_gender": {
              "code": "FEMALE",
              "weight": 0.5
            },
            "with_contact": [
              {
                "type": "email",
                "action": "must"
              }
            ],
            "engagement_rate": {
              "value": 0.03,
              "operator": "gte"
            },
            "text": "lifestyle",
            "last_posted": 30,
            "text_tags": [
              {
                "type": "hashtag",
                "value": "dailylife"
              },
              {
                "type": "hashtag",
                "value": "gluteday"
              },
              {
                "type": "hashtag",
                "value": "glutegrowth"
              },
              {
                "type": "hashtag",
                "value": "gluteworkout"
              },
              {
                "type": "hashtag",
                "value": "glutesworkout"
              }
            ]
          },
          "platform": "tiktok",
          "sort": {
            "field": "followers",
            "direction": "desc"
          }
        }

    query_params = {
        "source": "creable",
        "platform": "Tiktok",
        "filter_name": "modash-Test-US-64",
        "filter_body": filter_body,
        "cookie": cookie,
        "project_code": "test001",
        "auto_send": "yes",
        "template": "test_v1",
        "high_potential": "yes",
        "table_save": "https://laientech.feishu.cn/wiki/QTRqwY8vFiplVUkP5CMc9e9Fn1f?table=tblLfXDTRh6Ly6jJ&view=vewKNh3NJ2",
        "table_duplicate": ""
    }
    
    # 执行数据采集
    result = collect_kol_data(query_params)
    logger.info(f"执行结果: {result}")
