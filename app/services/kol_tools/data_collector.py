#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project ：KOL-python
@Date    ：2025/4/9 下午4:39
'''
from typing import Dict, List, Optional, Any, Tuple
import argparse
import requests

from app.db.session import SessionLocal
from app.services.tools import send_webhook, fte_compute
from app.logging_config import get_task_logger
from app.core.config import settings
import concurrent.futures
import threading
from functools import partial
import datetime
import time

# 从 tools.py 导入必要的函数
from app.services.kol_tools.tools import (
    _validate_params,
    _collect_source_data,
    _standardize_data,
    _collect_platform_data,
    _store_data_to_db,
    _store_high_potential_data,
    _send_to_feishu,
    _parse_table_url
)

# 从 feishu_lark_service 导入 Lark 类
from app.services.feishu_lark_service import Lark

# 使用任务日志记录器
logger = get_task_logger("app.worker.tasks.kol_data")


def ensure_lark_token_valid(lark_client: Lark) -> None:
    """
    确保Lark客户端的tenant token有效，如果无效或过期则刷新

    Args:
        lark_client: Lark客户端实例
    """
    try:
        # 检查token是否存在且未过期
        if not lark_client._tenant_token or (
                lark_client._token_expire_time and
                lark_client._token_expire_time < datetime.datetime.now()
        ):
            logger.info("Lark tenant token已过期或不存在，正在刷新")
            # 通过调用API强制刷新token
            # 这里假设获取wiki信息就会刷新token
            test_result = lark_client.get_wiki_node_info(node_token="QTRqwY8vFiplVUkP5CMc9e9Fn1f")
            # 无论成功失败，token都应该被刷新了
            logger.info("Lark tenant token已刷新")
    except Exception as e:
        logger.warning(f"刷新Lark token时出现异常: {str(e)}")
        # 这里不抛出异常，让后续操作自行处理token问题
        pass


def collect_kol_data_v2(query_params: Dict, lark_client: Lark(), record_id: str) -> Dict:
    """
    采用流式处理的KOL数据采集函数，每条数据单独处理并立即存储

    Args:
        query_params: 查询参数，包含所需的数据源、平台等信息

    Returns:
        包含任务结果的字典
    """
    try:
        logger.info(f"开始采集平台 {query_params.get('platform')} 的KOL数据（流式处理）")
        # 初始化Lark客户端（只初始化一次，并通过参数传递）
        # lark_client = Lark()

        # 参数校验
        source = query_params.get("source", "").lower()
        platform = query_params.get("platform", "").lower()
        filter_name = query_params.get("filter_name", "")
        project_code = query_params.get("project_code", "")
        auto_send = query_params.get("auto_send", "no")
        high_potential = query_params.get("high_potential", "no")
        table_save = query_params.get("table_save", "")
        table_duplicate = query_params.get("table_duplicate", "")

        # 线程数配置，可以根据实际情况调整
        max_workers = settings.MAX_WORKERS if hasattr(settings, 'MAX_WORKERS') else 15

        # 参数验证
        _validate_params(source, platform)

        # 确保tenant token是最新的
        ensure_lark_token_valid(lark_client)

        # 发送开始通知
        send_webhook(filter_name=filter_name, status="进行中: 0%")

        # 创建线程安全的计数器
        thread_stats = {
            "processed_count": 0,
            "success_count": 0,
            "failed_count": 0,
            "skipped_count": 0,
            "email_count": 0,
            "lock": threading.Lock(),
            "high_potential_records": [],
            "duplicate_records": []  # 新增：用于存储重复数据
        }

        # 创建数据库会话
        db = SessionLocal()

        try:
            # 1. 采集原始数据 (批量)
            logger.info(f"[1/4] 从数据源 {source} 采集原始数据")
            raw_data = _collect_source_data(source, query_params)
            if not raw_data:
                logger.error(f"从数据源 {source} 获取数据失败")
                send_webhook(filter_name=filter_name, status="失败")
                return {"status": "error", "message": f"从数据源 {source} 获取数据失败"}

            send_webhook(filter_name=filter_name, status=f"进行中: 10%")

            # 2. 数据标准化处理 (批量)
            logger.info(f"[2/4] 数据标准化处理")
            standardized_data = _standardize_data(raw_data, source, platform)
            if not standardized_data:
                logger.error("数据标准化处理失败")
                send_webhook(filter_name=filter_name, status="失败")
                return {"status": "error", "message": "数据标准化处理失败"}

            send_webhook(filter_name=filter_name, status=f"进行中: 20%")

            # 3. 多线程处理每一条KOL数据
            logger.info(f"[3/4] 开始多线程处理每一条KOL数据，使用 {max_workers} 个线程")

            # 统计数据
            total_count = len(standardized_data)
            logger.info(f"开始处理: 0/{total_count}")

            # 计算fte
            fte_compute('UA_KOL', 'UA_KOL_Standard_Database_TK', total_count)
            # 需要保存的数据表的app_token, table_id
            save_node_token, save_table_id = _parse_table_url(table_save)
            main_app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
            if not main_app_token:
                logger.error(f"查询主表飞书 app_token 失败; \n{table_save}\n{save_node_token}")
                send_webhook(filter_name=filter_name, status="失败")
                return {"status": "error", "message": f"查询主表飞书 app_token 失败: {table_save}"}
            main_app_token = main_app_token.get("obj_token")

            #  需要去重的表的app_token, table_id
            # 处理查重表格列表
            duplicate_tables = []
            duplicate_tables.append({
                "app_token":main_app_token,
                "table_id": save_table_id
            })
            if table_duplicate:
                for duplicate in table_duplicate:
                    save_node_token, save_table_id = _parse_table_url(duplicate)
                    app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
                    if not app_token:
                        logger.error(f"查询飞书 app_token 失败; \n{duplicate}\n{save_node_token}")
                        send_webhook(filter_name=filter_name, status="失败")
                        return {"status": "error", "message": f"查询去重表飞书 app_token 失败: {duplicate}"}
                    else:
                        app_token = app_token.get("obj_token")
                        duplicate_tables.append({
                            "app_token": app_token,
                            "table_id": save_table_id
                        })



            # 定义处理单条KOL数据的函数
            def process_single_kol(item, total_count, stats, query_params, db_factory, lark_client,
                                   app_token, duplicate_app_tokens):
                # 为每个线程创建独立的数据库会话
                thread_db = db_factory()
                try:
                    # 3.0 查询飞书表中是否存在数据
                    kol_name = item.get('kol_name', '')
                    if not settings.UPDATE:
                        # 如果不需要更新，先查询数据是否存在
                        exists, existing_data = search_feishu_data(item, lark_client, duplicate_app_tokens)
                        if exists:
                            logger.info(f"KOL '{kol_name}' 已存在且不需要更新，跳过处理")
                            # 保存重复数据的详细信息
                            duplicate_info = {
                                "kol_name": kol_name,
                                "source_data": item,
                                "existing_data": existing_data,
                                "found_in_table": True,
                                "timestamp": str(datetime.datetime.now())
                            }
                            with stats["lock"]:
                                stats["processed_count"] += 1
                                stats["skipped_count"] += 1
                                stats["duplicate_records"].append(duplicate_info)  # 添加重复记录
                                # 修改进度输出格式为"已处理/总数"
                                processed = min(stats["processed_count"], total_count)  # 防止processed_count超过total_count
                                logger.info(f"正在处理: {processed}/{total_count}")
                                # 计算百分比用于webhook
                                progress = 20 + int((processed / total_count) * 70)
                                progress = min(progress, 90)  # 确保最大进度不超过90%
                                if stats["processed_count"] % 10 == 0 or processed >= total_count:
                                    send_webhook(filter_name=filter_name, status=f"进行中: {progress}%")
                            return None

                    # 3.1 采集单个KOL的平台详细数据
                    item_with_detail = collect_platform_data_single(platform, item, query_params)

                    if item_with_detail:
                        # 3.2 将单个KOL数据存储到数据库
                        stored_item = store_data_to_db_single(thread_db, item_with_detail, query_params)

                        if stored_item:
                            with stats["lock"]:
                                stats["success_count"] += 1

                            # 3.3 处理高潜力KOL (如果需要)
                            if high_potential.lower() == "yes":
                                result, record = store_high_potential_data_single(
                                    filter_name, project_code, stored_item, table_save, table_duplicate, lark_client,
                                    app_token, duplicate_app_tokens
                                )
                                if result and record:
                                    with stats["lock"]:
                                        stats["high_potential_records"].append(record)

                            # 3.4 自动发送邮件处理 (如果需要)
                            if auto_send.lower() == "yes" and stored_item.get("email"):
                                result = send_to_feishu_single(
                                    stored_item, query_params, stats["high_potential_records"], lark_client
                                )
                                if result:
                                    with stats["lock"]:
                                        stats["email_count"] += 1
                            return stored_item
                        else:
                            with stats["lock"]:
                                stats["failed_count"] += 1
                    else:
                        with stats["lock"]:
                            stats["failed_count"] += 1

                except Exception as e:
                    logger.exception(f"处理单条KOL数据时发生错误: {str(e)}")
                    with stats["lock"]:
                        stats["failed_count"] += 1
                    return None

                finally:
                    # 关闭线程的数据库连接
                    thread_db.close()

                    # 更新处理进度
                    with stats["lock"]:
                        stats["processed_count"] += 1
                        # 修改进度输出格式为"已处理/总数"
                        processed = min(stats["processed_count"], total_count)  # 防止processed_count超过total_count
                        logger.info(f"正在处理: {processed}/{total_count}")
                        # 计算百分比用于webhook
                        progress = 20 + int((processed / total_count) * 70)  # 20%-90%的进度区间
                        progress = min(progress, 90)  # 确保最大进度不超过90%
                        if stats["processed_count"] % 10 == 0 or processed >= total_count:
                            send_webhook(filter_name=filter_name, status=f"进行中: {progress}%")

                return None

            # 创建线程池并执行任务
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 创建数据库工厂函数，为每个线程提供独立的会话
                db_factory = lambda: SessionLocal()

                # 创建处理函数，将固定参数绑定
                process_func = partial(
                    process_single_kol,
                    total_count=total_count,
                    stats=thread_stats,
                    query_params=query_params,
                    db_factory=db_factory,
                    lark_client=lark_client,
                    app_token=main_app_token,
                    duplicate_app_tokens=duplicate_tables
                )

                # 提交所有任务并等待完成
                futures = [executor.submit(process_func, item) for item in standardized_data]
                concurrent.futures.wait(futures)

            # 4. 完成处理
            # 等待一小段时间，确保所有线程的日志都已经输出
            time.sleep(0.5)  # 等待0.5秒，让所有线程的日志都有时间输出

            # 清除之前的处理日志，输出一个明确的处理完成日志
            logger.info("--------------------------------------------------------")
            logger.info(f"[4/4] 完成平台 {platform} 的KOL数据采集")
            logger.info(f"处理完成: {total_count}/{total_count} (100%)")
            logger.info("--------------------------------------------------------")

            # 处理完成后，强制进度为100%
            send_webhook(filter_name=filter_name, status=f"进行中: 100%")
            logger.info(
                f"总数据: {total_count}, 成功: {thread_stats['success_count']}, 失败: {thread_stats['failed_count']}, 跳过: {thread_stats['skipped_count']}")

            # 记录重复数据的数量和部分信息
            duplicate_count = len(thread_stats['duplicate_records'])
            logger.info(f"发现重复数据: {duplicate_count}条")
            if duplicate_count > 0:
                logger.info(
                    f"重复数据示例: {thread_stats['duplicate_records'][0]['kol_name'] if duplicate_count > 0 else '无'}")

            send_webhook(
                filter_name=filter_name,
                status="已完成",
                msg=f"总采集数:{total_count}条，成功:{thread_stats['success_count']}条，失败:{thread_stats['failed_count']}条，跳过:{thread_stats['skipped_count']}条，重复:{duplicate_count}条",
                unique_count=len(thread_stats['high_potential_records']),
                email_count=thread_stats['email_count']
            )

            # 准备重复数据的简化版本用于返回
            # 避免返回过大的数据结构
            simplified_duplicates = []
            for dup in thread_stats['duplicate_records']:
                simplified_dup = {
                    "kol_name": dup.get("kol_name", ""),
                    "account_link": dup.get("source_data", {}).get("account_link", ""),
                    "username": dup.get("source_data", {}).get("username", ""),
                    "timestamp": dup.get("timestamp", "")
                }
                simplified_duplicates.append(simplified_dup)

            # 更新状态
            record_data ={
                    "点击": True,
                    "Status": "已完成",
                    "标记": "本地",
                    "Remark": f"成功采集数据 {thread_stats['success_count']} 条，失败 {thread_stats['failed_count']} 条，跳过 {thread_stats['skipped_count']} 条，重复 {duplicate_count} 条",
                    "Unique Count": len(thread_stats['high_potential_records']),
                    "Valid Email": int(thread_stats['email_count']),
                    "Date": None
                }
            resp = lark_client.update_table_record("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya", record_id, record_data)
            logger.info(f'执行完毕更新飞书爬取工具记录:{resp}')
            return {
                "status": "success",
                "message": f"成功采集数据 {thread_stats['success_count']} 条，失败 {thread_stats['failed_count']} 条，跳过 {thread_stats['skipped_count']} 条，重复 {duplicate_count} 条",
                "data_count": total_count,
                "success_count": thread_stats['success_count'],
                "failed_count": thread_stats['failed_count'],
                "skipped_count": thread_stats['skipped_count'],
                "unique_count": len(thread_stats['high_potential_records']),
                "email_count": thread_stats['email_count'],
                "duplicate_count": duplicate_count,
                "duplicate_records": simplified_duplicates  # 添加重复记录到返回结果
            }

        except Exception as e:
            logger.exception(f"采集KOL数据时发生错误: {str(e)}")
            # 更新状态
            record_data = {
                "fields": {
                    "Status": "失败",
                }
            }
            lark_client.update_table_record("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya", record_id, record_data)
            send_webhook(filter_name=filter_name, status="失败", msg=str(e))
            return {"status": "error", "message": str(e)}

        finally:
            db.close()

    except Exception as e:
        # 更新状态
        record_data = {
            "fields": {
                "Status": "失败",
            }
        }
        lark_client.update_table_record("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya", record_id, record_data)
        logger.exception(f"执行采集KOL数据时发生错误: {str(e)}")
        return {"status": "error", "message": str(e)}


def collect_platform_data_single(platform: str, item: Dict, query_params: Dict) -> Optional[Dict]:
    """
    采集单个KOL的平台详细数据

    Args:
        platform: 平台名称（TikTok/Instagram/YouTube）
        item: 单个KOL的标准化数据
        query_params: 查询参数

    Returns:
        采集到的平台详细数据，失败则返回None
    """
    logger.info(f"开始采集平台 {platform} 的KOL详细数据: {item.get('kol_name', '')}")

    try:
        # 构建一个只包含当前项的列表，利用现有的_collect_platform_data函数
        single_item_list = [item]
        result = _collect_platform_data(platform, single_item_list, query_params)

        # 如果采集成功，返回第一个项
        if result and len(result) > 0:
            return result[0]
        else:
            logger.error(f"获取 {item.get('kol_name', '')} 的平台数据失败")
            return None

    except Exception as e:
        logger.exception(f"采集单条KOL数据时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return None


def store_data_to_db_single(db: Any, item: Dict, query_params: Dict) -> Optional[Dict]:
    """
    将单个KOL数据存储到数据库

    Args:
        db: 数据库会话
        item: 单个KOL的详细数据
        query_params: 查询参数

    Returns:
        存储后的数据，失败则返回None
    """
    logger.info(f"开始将KOL数据存储到数据库: {item.get('kol_name', '')}")

    try:
        # 构建一个只包含当前项的列表，利用现有的_store_data_to_db函数
        single_item_list = [item]
        result = _store_data_to_db(db, single_item_list, query_params)

        # 如果存储成功，返回第一个项
        if result and len(result) > 0:
            return result[0]
        else:
            logger.error(f"存储 {item.get('kol_name', '')} 的数据到数据库失败")
            return None

    except Exception as e:
        logger.exception(f"存储单条KOL数据到数据库时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return None


def store_high_potential_data_single(filter_name: str, project_code: str, item: Dict,
                                     table_save: str, table_duplicate: str, lark_client: Lark, app_token: str, duplicate_app_tokens) -> tuple[
    bool, Optional[Dict]]:
    """
    将单个高潜力KOL数据存储到指定飞书表

    Args:
        filter_name: 筛选条件名称
        project_code: 项目代码
        item: 单个KOL的详细数据
        table_save: 存储表ID或URL
        table_duplicate: 去重表ID或URL列表，以逗号分隔
        lark_client: Lark客户端实例
        app_token: 主表的app_token对象令牌

    Returns:
        (是否成功存储, 存储的记录)
    """
    logger.info(f"开始将高潜力KOL数据存储到飞书表: {item.get('kol_name', '')}")

    try:
        # 解析表格URL参数
        save_node_token, save_table_id = _parse_table_url(table_save)
        # app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
        # if not app_token:
        #     logger.error(f"查询飞书 app_token 失败; \n{table_save}\n{save_node_token}")
        #     return False, None
        # else:
        #     app_token = app_token.get("obj_token")

        if not save_table_id:
            logger.error(f"解析目标存储表URL失败: {table_save}")
            return False, None

        # 获取要检查的KOL ID
        kol_id = item.get("kol_name", "")
        if not kol_id:
            logger.error("KOL ID为空，无法存储数据")
            return False, None
        # 检查是否已存在该KOL ID
        for dup_table in duplicate_app_tokens:
            # 构建精确查询条件，只查询特定KOL ID
            search_params = {
                "filter": {
                    "conjunction": "and",
                    "conditions": [
                        {
                            "field_name": "KOL ID",
                            "operator": "is",
                            "value": [kol_id]
                        }
                    ]
                }
            }

            # 精确查询特定KOL ID的记录
            matching_records = lark_client.search_table_record(dup_table["app_token"], dup_table["table_id"], search_params)

            # 检查是否找到匹配记录
            if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
                logger.info(f"KOL ID '{kol_id}' 已存在于表格 {dup_table["app_token"]}，跳过存储")
                return True, None  # 已存在，返回成功但不创建新记录

        # 创建飞书表记录格式
        record = {
            "KOL ID": kol_id,  # 写入飞书表格不能有 TK_ 前缀
            "Email": item.get("email", ""),
            "Account link": {"text": item.get("account_link", ""), "link": item.get("account_link", "")},
            "KOL Name": item.get("username", ""),
            "Bio": item.get("bio", ""),
            "Source": item.get("source", ""),
            "Filter": filter_name,
            "Followers(K)": item.get('followers_k', 0),
            "Mean Views(K)": item.get('mean_views_k', 0),
            "Median Views(K)": item.get('median_views_k', 0),
            "Keywords-AI": ", ".join(str(keyword) for keyword in item.get('keywords_ai', []) if keyword),
            "Level": item.get('level'),
            "Project": project_code,
            "Engagement Rate(%)": f"{item.get('engagement_rate', 0) * 100:.2f} %",
            "Average Views(K)": item.get('average_views_k', 0),
        }

        # KOL ID不存在于任何表格中，创建新记录
        records = [record]
        result = lark_client.batch_create_table_records(app_token, save_table_id, records)

        if result:
            logger.info(f"成功将KOL '{kol_id}' 存储到高潜力飞书表")
            return True, record
        else:
            logger.error(f"存储KOL '{kol_id}' 到高潜力飞书表失败")
            return False, None

    except Exception as e:
        logger.exception(f"存储单条高潜力KOL数据到飞书表时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return False, None


def send_to_feishu_single(item: Dict, query_params: Dict, hp_records: Optional[List[Dict]], lark_client: Lark) -> bool:
    """
    将单个KOL数据发送到飞书邮箱表

    Args:
        item: 单个KOL的详细数据
        query_params: 查询参数
        hp_records: 高潜力记录列表，用于检查KOL是否为高潜力KOL
        lark_client: Lark客户端实例

    Returns:
        是否成功发送
    """
    logger.info(f"开始将KOL数据发送到邮箱表: {item.get('kol_name', '')}")

    try:
        # 检查是否有邮箱
        if not item.get("email"):
            logger.warning(f"KOL没有邮箱，跳过发送: {item.get('kol_name', '')}")
            return False

        # 检查是否在高潜力列表中（如果提供了高潜力列表）
        if hp_records:
            is_high_potential = False
            for record in hp_records:
                if record.get("KOL ID") == item.get("kol_name"):
                    is_high_potential = True
                    break

            if not is_high_potential:
                logger.warning(f"KOL不在高潜力列表中，跳过发送: {item.get('kol_name', '')}")
                return False

        # 获取飞书配置
        save_node_token = settings.FEISHU_EMAIL_APP_TOKEN
        table_id = settings.FEISHU_EMAIL_TABLE_ID

        # app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
        app_token = 'Mo7obvrgtaIMqRsZkdwc6UUFnCg'
        # if not app_token:
        #     logger.error(f"查询飞书 app_token 失败; \n{table_id}\n{save_node_token}")
        #     return False
        # else:
        #     app_token = app_token.get("obj_token")

        if not save_node_token or not table_id:
            logger.error("缺少必要的飞书配置: FEISHU_EMAIL_APP_TOKEN 或 FEISHU_EMAIL_TABLE_ID")
            return False

        # 获取KOL ID
        kol_id = item.get("kol_name", "")
        if not kol_id:
            logger.error("KOL ID为空，无法发送数据")
            return False

        # 构建精确查询条件，只查询特定KOL ID
        search_params = {
            "filter": {
                "conjunction": "and",
                "conditions": [
                    {
                        "field_name": "KOL ID",
                        "operator": "is",
                        "value": [kol_id]
                    }
                ]
            }
        }

        # 精确查询特定KOL ID的记录
        matching_records = lark_client.search_table_record(app_token, table_id, search_params)

        # 检查是否找到匹配记录
        if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
            logger.info(f"KOL ID '{kol_id}' 已存在于邮件表，跳过发送")
            return True  # 已存在，返回成功

        # 格式化数据为飞书表格所需格式
        record = {
            "KOL ID": kol_id,
            "Email": item.get("email", ""),
            "Account link": {"text": item.get("account_link", ""), "link": item.get("account_link", "")},
            "KOL Name": item.get("username", ""),
            "Template": query_params.get("template", ""),
            "App Code": query_params.get("project_code", ""),
        }

        # 创建新记录
        result = lark_client.batch_create_table_records(app_token, table_id, [record])

        if result:
            logger.info(f"成功将KOL '{kol_id}' 发送到自动发邮件飞书表")
            return True
        else:
            logger.error(f"发送KOL '{kol_id}' 到自动发邮件飞书表失败")
            return False

    except Exception as e:
        logger.exception(f"发送单条KOL数据到飞书邮箱表时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return False


def search_feishu_data(item: Dict, lark_client: Lark, duplicate_app_tokens) -> tuple[bool, Optional[Dict]]:
    """
    查询飞书表中是否已存在该KOL数据

    Args:
        item: 单个KOL的详细数据
        lark_client: Lark客户端实例
        duplicate_app_tokens: 包含所有查重表app_token的字典

    Returns:
        (是否存在, 存在的记录信息)
    """
    logger.info(f"查询飞书表中是否存在KOL: {item.get('kol_name', '')}")

    try:
        # 获取要检查的KOL ID
        kol_id = item.get("kol_name", "")
        if not kol_id:
            logger.error("KOL ID为空，无法查询数据")
            return False, None

        # 检查是否已存在该KOL ID
        for dup_table in duplicate_app_tokens:
            # 构建精确查询条件，只查询特定KOL ID
            search_params = {
                "filter": {
                    "conjunction": "and",
                    "conditions": [
                        {
                            "field_name": "KOL ID",
                            "operator": "is",
                            "value": [kol_id]
                        }
                    ]
                }
            }

            # 精确查询特定KOL ID的记录
            matching_records = lark_client.search_table_record(dup_table['app_token'], dup_table['table_id'], search_params)

            # 检查是否找到匹配记录
            if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
                logger.info(f"KOL ID '{kol_id}' 已存在于表格 {dup_table}")
                record_data = matching_records["items"][0].get("fields", {})
                return True, record_data

        # 所有表格都没找到匹配记录
        logger.info(f"KOL ID '{kol_id}' 未在任何表格中找到")
        return False, None

    except Exception as e:
        logger.exception(f"查询飞书表数据时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        # 如果查询过程出错，为安全起见返回False，让程序继续处理该数据
        return False, None


def send_slack_message(channel, text):
    requestBody = {
        "channel": channel,
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": text
                }
            }
        ]
    }
    header = {
        "Authorization": "Bearer *******************************************************"
    }
    response = requests.post("https://slack.com/api/chat.postMessage", headers=header, json=requestBody)


if __name__ == "__main__":
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description='KOL数据采集工具')
    parser.add_argument('--channel_id', type=str, default="U05PD11UDPW",
                        help='Slack频道ID，用于发送通知消息')
    args = parser.parse_args()
    # 获取channel_id参数
    channel_id = args.channel_id


    lark_client = Lark()
    for _ in range(2):
        items = lark_client.search_table_all_records("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya",
                                                     search_params={
                                                         "field_names": [
                                                             "Cookie",
                                                             "Filter Criteria",
                                                             "Source",
                                                             "Platform",
                                                             "Filter Name",
                                                             "Project Code",
                                                             "Auto Send",
                                                             "Email Template",
                                                             "High Pontential",
                                                             "high potential存储表",
                                                             "high potential去重表"

                                                         ],
                                                         "filter": {
                                                             "conjunction": "and",
                                                             "conditions": [
                                                                 {
                                                                     "field_name": "Date",
                                                                     "operator": "is",
                                                                     "value": ["Today"]
                                                                 },
                                                                 {
                                                                     "field_name": "点击",
                                                                     "operator": "is",
                                                                     "value": ["false"]
                                                                 }

                                                             ]
                                                         },
                                                         "automatic_fields": False
                                                     },page_size=500)
        processed_data = []

        for entry in items:
            fields = entry["fields"]

            # 拼接 Filter Criteria 中所有 text 字段作为 filter_body
            filter_body = "".join([fc["text"] for fc in fields["Filter Criteria"]])

            # 拼接所有 Cookie 中 type 为 text 的部分
            cookie = "; ".join([
                ck["text"].strip().rstrip(";")
                for ck in fields["Cookie"]
                if ck.get("type") == "text"
            ])

            query_params = {
                "source": fields.get("Source", ""),
                "platform": fields.get("Platform", ""),
                "filter_name": fields.get("Filter Name", [{}])[0].get("text", ""),
                "filter_body": filter_body,
                "cookie": cookie,
                "project_code": fields.get("Project Code", ""),
                "auto_send": fields.get("Auto Send", ""),
                "template": fields.get("Email Template", ""),
                "high_potential": fields.get("High Pontential", ""),
                "table_save": fields.get("high potential存储表", {}).get("link", ""),
                "table_duplicate": fields.get("high potential去重表", "")
            }

            processed_data.append({
                "query_params": query_params,
                "record_id": entry["record_id"]
            })
        # 执行数据采集 V2
        logger.info(f"本次共有{len(processed_data)}条filter执行任务")
        for item in processed_data:
            send_slack_message(channel_id, f"*开始执行:* 🚀 {'='*5} {item['query_params']['filter_name']} {'='*5}")
            logger.info(f"开始执行: {item['query_params']['filter_name']}")
            result = collect_kol_data_v2(item['query_params'], lark_client, item['record_id'])
            logger.info(f"执行结果: {result["message"]}")
            send_slack_message(channel_id, f"*执行结果:* :rocket: {'='*5} {result['message']} {'='*5}")
            logger.info(f"执行结束: {item['query_params']['filter_name']}\n\n")
            send_slack_message(channel_id, f"*执行结束:* :🛬: {'='*5} {item['query_params']['filter_name']} {'='*5}\n")

        send_slack_message(channel_id, f"*执行结束:* :🛬: 当前任务全部执行完毕\n")
        logger.info(f"当前任务全部执行完毕")
