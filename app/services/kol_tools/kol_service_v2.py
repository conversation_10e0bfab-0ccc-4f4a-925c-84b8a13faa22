#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project ：KOL-python
@File    ：kol_service_v2
<AUTHOR>
@Date    ：2025/4/9 下午4:39 
'''
from typing import Dict, List, Optional, Any, Tuple
from app.db.session import SessionLocal
from app.services.tools import send_webhook
from app.logging_config import get_task_logger
from app.core.config import settings

# 从 tools.py 导入必要的函数
from app.services.kol_tools.tools import (
    _validate_params,
    _collect_source_data,
    _standardize_data,
    _collect_platform_data,
    _store_data_to_db,
    _store_high_potential_data,
    _send_to_feishu,
    _parse_table_url
)

# 从 feishu_lark_service 导入 Lark 类
from app.services.feishu_lark_service import Lark

# 使用任务日志记录器
logger = get_task_logger("app.worker.tasks.kol_data")


def collect_kol_data_v2(query_params: Dict) -> Dict:
    """
    采用流式处理的KOL数据采集函数，每条数据单独处理并立即存储

    Args:
        query_params: 查询参数，包含所需的数据源、平台等信息

    Returns:
        包含任务结果的字典
    """
    try:
        logger.info(f"开始采集平台 {query_params.get('platform')} 的KOL数据（流式处理）")
        
        # 参数校验
        source = query_params.get("source", "").lower()
        platform = query_params.get("platform", "").lower()
        filter_name = query_params.get("filter_name", "")
        project_code = query_params.get("project_code", "")
        auto_send = query_params.get("auto_send", "no")
        high_potential = query_params.get("high_potential", "no")
        table_save = query_params.get("table_save", "")
        table_duplicate = query_params.get("table_duplicate", "")

        # 参数验证
        _validate_params(source, platform)
        
        # 发送开始通知
        send_webhook(filter_name=filter_name, status="进行中: 0%")

        # 创建数据库会话
        db = SessionLocal()

        try:
            # 1. 采集原始数据 (批量)
            logger.info(f"[1/4] 从数据源 {source} 采集原始数据")
            raw_data = _collect_source_data(source, query_params)
            if not raw_data:
                logger.error(f"从数据源 {source} 获取数据失败")
                send_webhook(filter_name=filter_name, status="失败")
                return {"status": "error", "message": f"从数据源 {source} 获取数据失败"}
            
            send_webhook(filter_name=filter_name, status=f"进行中: 10%")

            # 2. 数据标准化处理 (批量)
            logger.info(f"[2/4] 数据标准化处理")
            standardized_data = _standardize_data(raw_data, source, platform)
            if not standardized_data:
                logger.error("数据标准化处理失败")
                send_webhook(filter_name=filter_name, status="失败")
                return {"status": "error", "message": "数据标准化处理失败"}
            
            send_webhook(filter_name=filter_name, status=f"进行中: 20%")

            # 3. 逐条处理每一条KOL数据 (流式)
            logger.info(f"[3/4] 开始流式处理每一条KOL数据")
            
            # 统计数据
            total_count = len(standardized_data)
            processed_count = 0
            success_count = 0
            failed_count = 0
            high_potential_records = []
            email_count = 0
            skipped_count = 0  # 新增跳过计数
            
            # 提前初始化飞书表格的app_token缓存
            feishu_tokens_cache = {}
            if not settings.UPDATE and (table_save or table_duplicate):
                logger.info("预先初始化飞书表格的app_token缓存")
                lark_client = Lark()
                
                # 处理所有相关表格
                all_tables = []
                if table_save:
                    all_tables.append(table_save)
                if table_duplicate:
                    all_tables.extend(table_duplicate.split(','))
                
                # 获取并缓存所有表格的app_token
                for table in all_tables:
                    if table not in feishu_tokens_cache:
                        node_token, table_id = _parse_table_url(table)
                        if node_token and table_id:
                            app_token = lark_client.get_wiki_node_info(node_token=node_token)
                            if app_token:
                                feishu_tokens_cache[table] = {
                                    "app_token": app_token.get("obj_token"),
                                    "table_id": table_id,
                                    "node_token": node_token
                                }
                                logger.info(f"缓存表格 {table} 的app_token成功")
                            else:
                                logger.warning(f"获取表格 {table} 的app_token失败")
                        else:
                            logger.warning(f"解析表格URL失败: {table}")
            
            # 流式处理每条数据
            for item in standardized_data:
                try:
                    # 3.0 查询飞书表中是否存在数据
                    kol_name = item.get('kol_name', '')
                    if not settings.UPDATE:
                        # 如果不需要更新，先查询数据是否存在
                        exists, _ = search_feishu_data(filter_name, project_code, item, table_save, table_duplicate, feishu_tokens_cache)
                        if exists:
                            logger.info(f"KOL '{kol_name}' 已存在且不需要更新，跳过处理")
                            processed_count += 1
                            skipped_count += 1
                            # 更新进度
                            progress = 20 + int((processed_count / total_count) * 70)
                            logger.info(f"当前总进度为:{progress}%")
                            if processed_count % 10 == 0 or processed_count == total_count:
                                send_webhook(filter_name=filter_name, status=f"进行中: {progress}%")
                            continue

                    # 3.1 采集单个KOL的平台详细数据
                    item_with_detail = collect_platform_data_single(platform, item, query_params)
                    
                    if item_with_detail:
                        # 3.2 将单个KOL数据存储到数据库
                        stored_item = store_data_to_db_single(db, item_with_detail, query_params)
                        
                        if stored_item:
                            success_count += 1
                            
                            # 3.3 处理高潜力KOL (如果需要)
                            if high_potential.lower() == "yes":
                                result, record = store_high_potential_data_single(
                                    filter_name, project_code, stored_item, table_save, table_duplicate
                                )
                                if result and record:
                                    high_potential_records.append(record)
                            
                            # 3.4 自动发送邮件处理 (如果需要)
                            if auto_send.lower() == "yes" and stored_item.get("email"):
                                result = send_to_feishu_single(
                                    stored_item, query_params, high_potential_records
                                )
                                if result:
                                    email_count += 1
                        else:
                            failed_count += 1
                    else:
                        failed_count += 1
                
                except Exception as e:
                    logger.exception(f"处理单条KOL数据时发生错误: {str(e)}")
                    failed_count += 1
                
                finally:
                    # 更新处理进度
                    processed_count += 1
                    progress = 20 + int((processed_count / total_count) * 70)  # 20%-90%的进度区间
                    logger.info(f"当前总进度为:{progress}%")
                    send_webhook(filter_name=filter_name, status=f"进行中: {progress}%")
            
            # 4. 完成处理
            logger.info(f"[4/4] 完成平台 {platform} 的KOL数据采集")
            logger.info(f"总数据: {total_count}, 成功: {success_count}, 失败: {failed_count}, 跳过: {skipped_count}")
            
            send_webhook(
                filter_name=filter_name, 
                status="已完成",
                msg=f"总采集数:{total_count}条，成功:{success_count}条，失败:{failed_count}条，跳过:{skipped_count}条", 
                unique_count=len(high_potential_records), 
                email_count=email_count
            )

            return {
                "status": "success",
                "message": f"成功采集数据 {success_count} 条，失败 {failed_count} 条，跳过 {skipped_count} 条",
                "data_count": total_count,
                "success_count": success_count,
                "failed_count": failed_count,
                "skipped_count": skipped_count,
                "unique_count": len(high_potential_records),
                "email_count": email_count
            }

        except Exception as e:
            logger.exception(f"采集KOL数据时发生错误: {str(e)}")
            send_webhook(filter_name=filter_name, status="失败", msg=str(e))
            return {"status": "error", "message": str(e)}

        finally:
            db.close()

    except Exception as e:
        logger.exception(f"执行采集KOL数据时发生错误: {str(e)}")
        return {"status": "error", "message": str(e)}


def collect_platform_data_single(platform: str, item: Dict, query_params: Dict) -> Optional[Dict]:
    """
    采集单个KOL的平台详细数据

    Args:
        platform: 平台名称（TikTok/Instagram/YouTube）
        item: 单个KOL的标准化数据
        query_params: 查询参数

    Returns:
        采集到的平台详细数据，失败则返回None
    """
    logger.info(f"开始采集平台 {platform} 的KOL详细数据: {item.get('kol_name', '')}")
    
    try:
        # 构建一个只包含当前项的列表，利用现有的_collect_platform_data函数
        single_item_list = [item]
        result = _collect_platform_data(platform, single_item_list, query_params)
        
        # 如果采集成功，返回第一个项
        if result and len(result) > 0:
            return result[0]
        else:
            logger.error(f"获取 {item.get('kol_name', '')} 的平台数据失败")
            return None
            
    except Exception as e:
        logger.exception(f"采集单条KOL数据时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return None


def store_data_to_db_single(db: Any, item: Dict, query_params: Dict) -> Optional[Dict]:
    """
    将单个KOL数据存储到数据库

    Args:
        db: 数据库会话
        item: 单个KOL的详细数据
        query_params: 查询参数

    Returns:
        存储后的数据，失败则返回None
    """
    logger.info(f"开始将KOL数据存储到数据库: {item.get('kol_name', '')}")
    
    try:
        # 构建一个只包含当前项的列表，利用现有的_store_data_to_db函数
        single_item_list = [item]
        result = _store_data_to_db(db, single_item_list, query_params)
        
        # 如果存储成功，返回第一个项
        if result and len(result) > 0:
            return result[0]
        else:
            logger.error(f"存储 {item.get('kol_name', '')} 的数据到数据库失败")
            return None
            
    except Exception as e:
        logger.exception(f"存储单条KOL数据到数据库时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return None


def store_high_potential_data_single(filter_name: str, project_code: str, item: Dict, 
                                    table_save: str, table_duplicate: str) -> tuple[bool, Optional[Dict]]:
    """
    将单个高潜力KOL数据存储到指定飞书表

    Args:
        filter_name: 筛选条件名称
        project_code: 项目代码
        item: 单个KOL的详细数据
        table_save: 存储表ID或URL
        table_duplicate: 去重表ID或URL列表，以逗号分隔

    Returns:
        (是否成功存储, 存储的记录)
    """
    logger.info(f"开始将高潜力KOL数据存储到飞书表: {item.get('kol_name', '')}")
    
    try:
        # 初始化Lark客户端
        lark_client = Lark()

        # 解析表格URL参数
        save_node_token, save_table_id = _parse_table_url(table_save)
        app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
        if not app_token:
            logger.error(f"查询飞书 app_token 失败; \n{table_save}\n{save_node_token}")
            return False, None
        else:
            app_token = app_token.get("obj_token")

        if not save_table_id:
            logger.error(f"解析目标存储表URL失败: {table_save}")
            return False, None
            
        # 获取要检查的KOL ID
        kol_id = item.get("kol_name", "")
        if not kol_id:
            logger.error("KOL ID为空，无法存储数据")
            return False, None
            
        # 处理查重表格列表
        duplicate_tables = []
        if table_duplicate:
            duplicate_tables = table_duplicate.split(',')

        # 往duplicate_tables中添加目标存储表
        if table_save:
            duplicate_tables.append(table_save)
            
        # 检查是否已存在该KOL ID
        for dup_table in duplicate_tables:
            dup_node_token, dup_table_id = _parse_table_url(dup_table)
            if not dup_table_id:
                logger.warning(f"解析查重表URL失败，将跳过此表: {dup_table}")
                continue
                
            # 构建精确查询条件，只查询特定KOL ID
            search_params = {
                "filter": {
                    "conjunction": "and",
                    "conditions": [
                        {
                            "field_name": "KOL ID", 
                            "operator": "is", 
                            "value": [kol_id]
                        }
                    ]
                }
            }
            
            # 精确查询特定KOL ID的记录
            matching_records = lark_client.search_table_record(app_token, dup_table_id, search_params)
            
            # 检查是否找到匹配记录
            if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
                logger.info(f"KOL ID '{kol_id}' 已存在于表格 {dup_table}，跳过存储")
                return True, None  # 已存在，返回成功但不创建新记录
        
        # 创建飞书表记录格式
        record = {
            "KOL ID": kol_id,  # 写入飞书表格不能有 TK_ 前缀
            "Email": item.get("email", ""),
            "Account link": {"text": item.get("account_link", ""), "link": item.get("account_link", "")},
            "KOL Name": item.get("username", ""),
            "Bio": item.get("bio", ""),
            "Source": item.get("source", ""),
            "Filter": filter_name,
            "Followers(K)": item.get('followers_k', 0),
            "Mean Views(K)": item.get('mean_views_k', 0),
            "Median Views(K)": item.get('median_views_k', 0),
            "Keywords-AI": ", ".join(str(keyword) for keyword in item.get('keywords_ai', []) if keyword),
            "Level": item.get('level'),
            "Project": project_code,
            "Engagement Rate(%)": f"{item.get('engagement_rate', 0) * 100:.2f} %",
            "Average Views(K)": item.get('average_views_k', 0),
        }
        
        # KOL ID不存在于任何表格中，创建新记录
        records = [record]
        result = lark_client.batch_create_table_records(app_token, save_table_id, records)
        
        if result:
            logger.info(f"成功将KOL '{kol_id}' 存储到高潜力飞书表")
            return True, record
        else:
            logger.error(f"存储KOL '{kol_id}' 到高潜力飞书表失败")
            return False, None
            
    except Exception as e:
        logger.exception(f"存储单条高潜力KOL数据到飞书表时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return False, None


def send_to_feishu_single(item: Dict, query_params: Dict, hp_records: Optional[List[Dict]] = None) -> bool:
    """
    将单个KOL数据发送到飞书邮箱表

    Args:
        item: 单个KOL的详细数据
        query_params: 查询参数
        hp_records: 高潜力记录列表，用于检查KOL是否为高潜力KOL

    Returns:
        是否成功发送
    """
    logger.info(f"开始将KOL数据发送到邮箱表: {item.get('kol_name', '')}")
    
    try:
        # 检查是否有邮箱
        if not item.get("email"):
            logger.warning(f"KOL没有邮箱，跳过发送: {item.get('kol_name', '')}")
            return False
            
        # 检查是否在高潜力列表中（如果提供了高潜力列表）
        if hp_records:
            is_high_potential = False
            for record in hp_records:
                if record.get("KOL ID") == item.get("kol_name"):
                    is_high_potential = True
                    break
                    
            if not is_high_potential:
                logger.warning(f"KOL不在高潜力列表中，跳过发送: {item.get('kol_name', '')}")
                return False
        
        # 初始化Lark客户端
        lark_client = Lark()

        # 获取飞书配置
        save_node_token = settings.FEISHU_EMAIL_APP_TOKEN
        table_id = settings.FEISHU_EMAIL_TABLE_ID

        app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
        if not app_token:
            logger.error(f"查询飞书 app_token 失败; \n{table_id}\n{save_node_token}")
            return False
        else:
            app_token = app_token.get("obj_token")

        if not save_node_token or not table_id:
            logger.error("缺少必要的飞书配置: FEISHU_EMAIL_APP_TOKEN 或 FEISHU_EMAIL_TABLE_ID")
            return False
            
        # 获取KOL ID
        kol_id = item.get("kol_name", "")
        if not kol_id:
            logger.error("KOL ID为空，无法发送数据")
            return False
            
        # 构建精确查询条件，只查询特定KOL ID
        search_params = {
            "filter": {
                "conjunction": "and",
                "conditions": [
                    {
                        "field_name": "KOL ID", 
                        "operator": "is", 
                        "value": [kol_id]
                    }
                ]
            }
        }
        
        # 精确查询特定KOL ID的记录
        matching_records = lark_client.search_table_record(app_token, table_id, search_params)
        
        # 检查是否找到匹配记录
        if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
            logger.info(f"KOL ID '{kol_id}' 已存在于邮件表，跳过发送")
            return True  # 已存在，返回成功
        
        # 格式化数据为飞书表格所需格式
        record = {
            "KOL ID": kol_id,
            "Email": item.get("email", ""),
            "Account link": {"text": item.get("account_link", ""), "link": item.get("account_link", "")},
            "KOL Name": item.get("username", ""),
            "Template": query_params.get("template", ""),
            "App Code": query_params.get("project_code", ""),
        }
        
        # 创建新记录
        result = lark_client.batch_create_table_records(app_token, table_id, [record])
        
        if result:
            logger.info(f"成功将KOL '{kol_id}' 发送到自动发邮件飞书表")
            return True
        else:
            logger.error(f"发送KOL '{kol_id}' 到自动发邮件飞书表失败")
            return False
            
    except Exception as e:
        logger.exception(f"发送单条KOL数据到飞书邮箱表时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return False


def search_feishu_data(filter_name: str, project_code: str, item: Dict, 
                       table_save: str, table_duplicate: str, 
                       tokens_cache: Optional[Dict] = None) -> tuple[bool, Optional[Dict]]:
    """
    查询飞书表中是否已存在该KOL数据

    Args:
        filter_name: 筛选条件名称
        project_code: 项目代码
        item: 单个KOL的详细数据
        table_save: 存储表ID或URL
        table_duplicate: 去重表ID或URL列表, 以逗号分隔
        tokens_cache: 可选的app_token缓存字典

    Returns:
        (是否存在, 存在的记录信息)
    """
    logger.info(f"查询飞书表中是否存在KOL: {item.get('kol_name', '')}")
    
    try:
        # 初始化Lark客户端
        lark_client = Lark()

        # 获取要检查的KOL ID
        kol_id = item.get("kol_name", "")
        if not kol_id:
            logger.error("KOL ID为空，无法查询数据")
            return False, None
            
        # 处理查重表格列表
        duplicate_tables = []
        # 初始化或使用传入的token缓存
        app_tokens = tokens_cache or {}
        
        # 先处理主表
        if table_save:
            # 如果缓存中没有主表信息，则获取
            if table_save not in app_tokens:
                # 解析主表URL参数
                save_node_token, save_table_id = _parse_table_url(table_save)
                app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
                if not app_token:
                    logger.error(f"查询飞书 app_token 失败; \n{table_save}\n{save_node_token}")
                    return False, None
                else:
                    app_tokens[table_save] = {
                        "app_token": app_token.get("obj_token"),
                        "table_id": save_table_id,
                        "node_token": save_node_token
                    }
            
            if not app_tokens.get(table_save, {}).get("table_id"):
                logger.error(f"解析目标存储表URL失败: {table_save}")
                return False, None
                
            duplicate_tables.append(table_save)
        
        # 处理其他去重表
        if table_duplicate:
            other_tables = table_duplicate.split(',')
            duplicate_tables.extend(other_tables)
        
        # 如果没有预先提供缓存，则获取所有表的app_token
        if not tokens_cache:
            # 一次性预先获取所有表的app_token
            for dup_table in duplicate_tables:
                if dup_table != table_save and dup_table not in app_tokens:
                    dup_node_token, dup_table_id = _parse_table_url(dup_table)
                    if not dup_node_token or not dup_table_id:
                        logger.warning(f"解析查重表URL节点失败，将跳过此表: {dup_table}")
                        continue
                    
                    dup_app_token = lark_client.get_wiki_node_info(node_token=dup_node_token)
                    if not dup_app_token:
                        logger.warning(f"查询飞书 app_token 失败，跳过此表: {dup_table}")
                        continue
                    app_tokens[dup_table] = {
                        "app_token": dup_app_token.get("obj_token"),
                        "table_id": dup_table_id,
                        "node_token": dup_node_token
                    }
            
        # 检查是否已存在该KOL ID
        for dup_table in duplicate_tables:
            # 从缓存中获取app_token和table_id
            if dup_table not in app_tokens:
                # 如果缓存中没有，尝试获取
                if not tokens_cache:  # 只有在没有预先提供缓存时才尝试获取
                    dup_node_token, dup_table_id = _parse_table_url(dup_table)
                    if not dup_node_token or not dup_table_id:
                        logger.warning(f"解析查重表URL失败，将跳过此表: {dup_table}")
                        continue
                    
                    dup_app_token = lark_client.get_wiki_node_info(node_token=dup_node_token)
                    if not dup_app_token:
                        logger.warning(f"查询飞书 app_token 失败，跳过此表: {dup_table}")
                        continue
                    app_tokens[dup_table] = {
                        "app_token": dup_app_token.get("obj_token"),
                        "table_id": dup_table_id,
                        "node_token": dup_node_token
                    }
                else:
                    logger.warning(f"未找到表格对应的app_token，跳过此表: {dup_table}")
                    continue
            
            table_info = app_tokens[dup_table]
            current_app_token = table_info.get("app_token")
            dup_table_id = table_info.get("table_id")
            
            if not current_app_token or not dup_table_id:
                logger.warning(f"表格信息不完整，跳过此表: {dup_table}")
                continue
                
            # 构建精确查询条件，只查询特定KOL ID
            search_params = {
                "filter": {
                    "conjunction": "and",
                    "conditions": [
                        {
                            "field_name": "KOL ID", 
                            "operator": "is", 
                            "value": [kol_id]
                        }
                    ]
                }
            }
            
            # 精确查询特定KOL ID的记录
            matching_records = lark_client.search_table_record(current_app_token, dup_table_id, search_params)
            
            # 检查是否找到匹配记录
            if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
                logger.info(f"KOL ID '{kol_id}' 已存在于表格 {dup_table}")
                record_data = matching_records["items"][0].get("fields", {})
                return True, record_data
        
        # 所有表格都没找到匹配记录
        logger.info(f"KOL ID '{kol_id}' 未在任何表格中找到")
        return False, None
            
    except Exception as e:
        logger.exception(f"查询飞书表数据时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        # 如果查询过程出错，为安全起见返回False，让程序继续处理该数据
        return False, None


if __name__ == "__main__":
    # 使用命令行参数
    cookie = '_fbp=fb.1.1740542452673.47177652690672846; intercom-device-id-hgbst103=741303a1-e7b0-4450-9eec-84184478f2bb; cwr_u=d4a93369-1096-4934-b9e4-23cb6b98fd6c; __stripe_mid=e11cbfd8-c062-4a35-bafc-9f03ff382a4ed65bfe; g_state={"i_l":0}; ajs_group_id=674d1f64309da8a5f2e84c73; intercom-id-hgbst103=6ebf7867-aec7-4792-b1f5-f750c4bdd391; _clck=19odw5r%7C2%7Cfts%7C0%7C1883; ajs_user_id=674d1f64309da8a5f2e84c6e; ajs_anonymous_id=6138fb9f-041d-4719-b1b6-774052b9a3bf; _gid=GA1.2.**********.**********; modash.xsid2=s%3A3LnCq1i20joq5qfpgSElMVmYpRoIvv0A.K%2FBh%2F0vABfgHlpXoB3g9csnhsXClVguS%2BsiSme010vY; __stripe_sid=73fa26bb-e035-45f4-8f48-99e81bc7f1921a659e; analytics_session_id=1744338301666; amp_session_id=1744338301666; _ga=GA1.1.**********.**********; _ga_6FKKZM7XTB=GS1.1.**********.96.1.**********.56.0.350313169; intercom-session-hgbst103=TUxjcTRSZ1ZaR3BvVlRlRUVwd1lrYXBVMzEvRmhvM0QwbUcvWHJLY092eVBJb3RNQzNqVFA4UzNPVmhyYjVvYys1WmRTZW95MVQ0NThtOFBvMzRBWU96U1FkYkdGSEE0eXVqc25IMFV3Vnc9LS1JSk5xQU1zY1NzS1h0OUh6U0JNUmlBPT0=--874b3005fbe4b2c7e911d2ad01ddc0cbe5adf113; fs_lua=1.1744338734112; analytics_session_id.last_access=1744338802393; fs_uid=#RVTE7#406c2abf-3e35-46a9-9384-33fd71a7f9f6:603e33a8-92f6-46d3-ab80-d0b844a4422b:1744338298015::3#ea0e872f#/**********; AWSALB=p5ZpdBmBkxQupTvctKzJwToNhqAKekoTEAFk6cRpWMsaJwkGz5lokyd6OVVZrlaAXXCSfnvlAH/uLhj8nngpRpO6zK4tvxnTaUdtm2v3Ph55nbVNxf+6KvnMFf5F0/esnadZrL4U2P/JXA2IKWCEPX5jb74YwSibvRmz5e6BSz5dbQ/EqMXTBha3aAUEAtXUZuPjgnGixbh0IxD6BYJWqid2ZhrdW2g+LDxF/6DqawzQxnf8zprSK/WkBTtTLYw=; AWSALBCORS=p5ZpdBmBkxQupTvctKzJwToNhqAKekoTEAFk6cRpWMsaJwkGz5lokyd6OVVZrlaAXXCSfnvlAH/uLhj8nngpRpO6zK4tvxnTaUdtm2v3Ph55nbVNxf+6KvnMFf5F0/esnadZrL4U2P/JXA2IKWCEPX5jb74YwSibvRmz5e6BSz5dbQ/EqMXTBha3aAUEAtXUZuPjgnGixbh0IxD6BYJWqid2ZhrdW2g+LDxF/6DqawzQxnf8zprSK/WkBTtTLYw=; cwr_s=eyJzZXNzaW9uSWQiOiI2Mzc0MDcxNS01ZWFhLTQ5ZGYtOTQyNC1lOWJhODI0OWIwZmMiLCJyZWNvcmQiOnRydWUsImV2ZW50Q291bnQiOjEyNzcsInBhZ2UiOnsicGFnZUlkIjoiL2Rpc2NvdmVyeS90aWt0b2siLCJwYXJlbnRQYWdlSWQiOiIvZGlzY292ZXJ5L2luc3RhZ3JhbSIsImludGVyYWN0aW9uIjozLCJyZWZlcnJlciI6Imh0dHBzOi8vbWFya2V0ZXIubW9kYXNoLmlvL2xvZ2luL21hcmtldGVyP3JlZGlyZWN0PS9kaXNjb3ZlcnkvaW5zdGFncmFtIiwicmVmZXJyZXJEb21haW4iOiJtYXJrZXRlci5tb2Rhc2guaW8iLCJzdGFydCI6MTc0NDMzODMxNDkwN319'    
    filter_body = {
        "page": 0,
        "filters": {
            "influencer": {
            "location": [
                148838,
                1428125,
                2202162,
                62149,
                1311341,
                365331,
                51477,
                16239,
                80500,
                307763,
                295480,
                51701,
                51684,
                50046,
                54224,
                1473946,
                299133,
                62273,
                2323309,
                556706,
                2978650,
                218657,
                49715,
                52411
            ],
            "relevance": {
                "usernames": [],
                "hashtags": []
            },
            "textTags": [
                {
                "type": "hashtag",
                "value": "#handstandchallenge",
                "action": "should"
                },
                {
                "type": "hashtag",
                "value": "#injuryprevention",
                "action": "should"
                },
                {
                "type": "hashtag",
                "value": "#bodyweightworkout",
                "action": "should"
                },
                {
                "type": "hashtag",
                "value": "#pullday",
                "action": "should"
                }
            ],
            "keywords": "",
            "hasContactDetails": [],
            "language": "en",
            "lastposted": 30,
            "followers": {
                "min": 1000,
                "max": 500000
            },
            "engagementRate": 0.03,
            "views": {
                "min": 5000,
                "max": 150000
            },
            "gender": "FEMALE"
            },
            "audience": {
            "location": [
                {
                "id": 148838,
                "weight": 0.15
                },
                {
                "id": 1428125,
                "weight": 0.15
                },
                {
                "id": 2202162,
                "weight": 0.15
                },
                {
                "id": 62149,
                "weight": 0.15
                },
                {
                "id": 1311341,
                "weight": 0.15
                },
                {
                "id": 365331,
                "weight": 0.15
                },
                {
                "id": 51477,
                "weight": 0.15
                },
                {
                "id": 51701,
                "weight": 0.15
                },
                {
                "id": 80500,
                "weight": 0.15
                },
                {
                "id": 16239,
                "weight": 0.15
                },
                {
                "id": 307763,
                "weight": 0.15
                },
                {
                "id": 295480,
                "weight": 0.15
                },
                {
                "id": 49715,
                "weight": 0.15
                },
                {
                "id": 52411,
                "weight": 0.15
                },
                {
                "id": 51684,
                "weight": 0.15
                },
                {
                "id": 50046,
                "weight": 0.15
                },
                {
                "id": 54224,
                "weight": 0.15
                },
                {
                "id": 299133,
                "weight": 0.15
                },
                {
                "id": 62273,
                "weight": 0.15
                },
                {
                "id": 2323309,
                "weight": 0.15
                },
                {
                "id": 556706,
                "weight": 0.15
                },
                {
                "id": 2978650,
                "weight": 0.15
                },
                {
                "id": 218657,
                "weight": 0.15
                },
                {
                "id": 1473946,
                "weight": 0.15
                }
            ],
            "age": [
                {
                "id": "25-34",
                "weight": 0.2
                },
                {
                "id": "35-44",
                "weight": 0.2
                },
                {
                "id": "45-64",
                "weight": 0.2
                }
            ],
            "language": None,
            "gender": {
                "id": "FEMALE",
                "weight": 0.5
            }
            },
            "actions": [],
            "options": {
            "showSavedProfiles": True
            },
            "relevanceType": "relevance"
        },
        "sort": {}
        }

    query_params = {
        "source": "modash",
        "platform": "Tiktok",
        "filter_name": "modash-handstandchallenge-hashtag-tier1-F-V-3-225",
        "filter_body": filter_body,
        "cookie": cookie,
        "project_code": "test001",
        "auto_send": "yes",
        "template": "test_v1",
        "high_potential": "yes",
        "table_save": "https://laientech.feishu.cn/wiki/QTRqwY8vFiplVUkP5CMc9e9Fn1f?table=tblLfXDTRh6Ly6jJ&view=vewKNh3NJ2",
        "table_duplicate": ""
    }
    
    # 执行数据采集 V2
    result = collect_kol_data_v2(query_params)
    logger.info(f"执行结果: {result}")
