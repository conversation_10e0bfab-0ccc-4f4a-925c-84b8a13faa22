import json
import time
import requests
import lark_oapi as lark
from lark_oapi import J<PERSON><PERSON>
from lark_oapi.api.bitable.v1 import *
from lark_oapi.api.contact.v3 import *
from lark_oapi.api.im.v1 import *
from lark_oapi.api.wiki.v2 import GetNodeSpaceRequest, GetNodeSpaceResponse
from loguru import logger
from dotenv import load_dotenv
import os
from datetime import datetime, timedelta
from app.core.config import settings

load_dotenv()

LARK_APP_ID = settings.LARK_APP_ID
LARK_APP_SECRET = settings.LARK_APP_SECRET


class Lark:
    """
    飞书/Lark API封装类，提供与飞书多维表格、消息、用户等功能的交互
    """
    def __init__(self):
        """
        初始化Lark客户端
        从环境变量获取APP_ID和APP_SECRET并构建客户端实例
        """
        self.client = lark.Client.builder() \
            .app_id(LARK_APP_ID) \
            .app_secret(LARK_APP_SECRET) \
            .build()
        self.app_id = LARK_APP_ID
        self.app_secret = LARK_APP_SECRET
        # 添加token缓存机制
        self._tenant_token = None
        self._token_expire_time = None

    def _make_request(self, method, url, **kwargs):
        """
        封装HTTP请求，统一处理异常和重试
        
        参数:
            method: HTTP方法，如'get', 'post'等
            url: 请求URL
            **kwargs: 请求参数
            
        返回:
            Response对象或None(请求失败)
        """
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                response = getattr(requests, method.lower())(url, **kwargs)
                # 检查响应状态
                if response.status_code == 200:
                    return response
                else:
                    logger.error(f"请求失败: URL={url}, 状态码={response.status_code}, 响应={response.text}")
            except Exception as e:
                logger.error(f"请求异常: URL={url}, 异常={str(e)}")
            
            # 重试延迟
            retry_count += 1
            if retry_count < max_retries:
                delay = 2 ** retry_count  # 指数退避策略
                logger.warning(f"正在进行第{retry_count}次重试, {delay}秒后...")
                time.sleep(delay)
        
        return None

    def get_tenant_access_token(self):
        """
        获取租户访问令牌(tenant_access_token)，带缓存机制
        
        返回:
            str: 租户访问令牌
        """
        # 检查现有token是否有效
        current_time = datetime.now()
        if self._tenant_token and self._token_expire_time and current_time < self._token_expire_time:
            return self._tenant_token
        
        # 请求新token
        headers = {
            'Content-Type': 'application/json; charset=utf-8'
        }
        data = {
            'app_id': self.app_id,
            'app_secret': self.app_secret
        }
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
        
        response = self._make_request('post', url, headers=headers, json=data)
        if not response:
            logger.error("获取tenant_access_token失败")
            return None
        
        result = response.json()
        if result.get("code") != 0:
            logger.error(f"获取tenant_access_token失败: {result}")
            return None
        
        self._tenant_token = result.get("tenant_access_token")
        # 设置过期时间，提前5分钟过期以确保安全
        expire_seconds = result.get("expire", 7200)
        self._token_expire_time = current_time + timedelta(seconds=expire_seconds - 300)
        
        return self._tenant_token

    def batch_delete_table_records(self, app_token, table_id, record_ids):
        """
        批量删除多维表格记录
        
        参数:
            app_token: 应用token
            table_id: 表格ID
            record_ids: 要删除的记录ID列表
            
        返回:
            list: 所有响应的列表，失败返回None
        
        说明:
            API限制每次最多删除500条记录，此方法自动分批处理
        """
        # 批量删除记录，每次最多删除500条
        max_batch_size = 500
        total_records = len(record_ids)
        batches = [record_ids[i:i + max_batch_size] for i in range(0, total_records, max_batch_size)]
        all_responses = []

        for batch in batches:
            requests = BatchDeleteAppTableRecordRequest.builder() \
                .app_token(app_token).table_id(table_id) \
                .request_body(BatchDeleteAppTableRecordRequestBody.builder().records(batch).build()) \
                .build()

            response = self.client.bitable.v1.app_table_record.batch_delete(requests)
            if not response.success():
                logger.error(
                    f"client.bitable.v1.app_table_record.batch_delete failed, "
                    f"code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
                return None
            else:
                str_data = JSON.marshal(response.data)
                all_responses.append(json.loads(str_data))

        return all_responses

    def batch_create_table_records(self, app_token, table_id, record_data):
        """
        批量创建多维表格记录
        
        参数:
            app_token: 应用token
            table_id: 表格ID
            record_data: 要创建的记录数据列表
            
        返回:
            dict: 创建结果，失败返回None
        """
        # 批量创建数据
        create_record = [AppTableRecord.builder().fields(record).build() for record in record_data]
        requests: BatchCreateAppTableRecordRequest = BatchCreateAppTableRecordRequest.builder() \
            .app_token(app_token).table_id(table_id) \
            .user_id_type("user_id") \
            .request_body(BatchCreateAppTableRecordRequestBody.builder().records(create_record).build()) \
            .build()
        response: BatchCreateAppTableRecordResponse = self.client.bitable.v1.app_table_record.batch_create(requests)
        if not response.success():
            logger.error(
                f"client.bitable.v1.app_table_record.batch_create failed, code: {response.code}, "
                f"msg: {response.msg}, log_id: {response.get_log_id()}")
            return None
        else:
            str_data = JSON.marshal(response.data)
            return json.loads(str_data)

    def update_table_record(self, app_token, table_id, record_id, record_data):
        """
        更新单条多维表格记录
        
        参数:
            app_token: 应用token
            table_id: 表格ID
            record_id: 记录ID
            record_data: 要更新的记录数据
            
        返回:
            bool: 成功返回True，失败返回False
        """
        request: UpdateAppTableRecordRequest = UpdateAppTableRecordRequest.builder() \
            .app_token(app_token) \
            .table_id(table_id) \
            .record_id(record_id) \
            .request_body(AppTableRecord.builder()
                          .fields(record_data)
                          .build()
                          ) \
            .build()

        response: UpdateAppTableRecordResponse = self.client.bitable.v1.app_table_record.update(request)

        if not response.success():
            logger.error(
                f"client.bitable.v1.app_table_record.update failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
            return False
        return True

    def batch_update_table_record(self, app_token, table_id, record_list):
        """
        批量更新多维表格记录
        
        参数:
            app_token: 应用token
            table_id: 表格ID
            record_list: 要更新的记录列表，每条记录包含record_id和field_data
            
        返回:
            bool: 成功返回True，失败返回False
        """
        request_body = []
        for record in record_list:
            record_id = record["record_id"]
            field_data = record["field_data"]
            request_body.append(
                AppTableRecord.builder()
                .fields(field_data)
                .record_id(record_id)
                .build()
            )

        request: BatchUpdateAppTableRecordRequest = BatchUpdateAppTableRecordRequest.builder() \
            .app_token(app_token) \
            .table_id(table_id) \
            .request_body(BatchUpdateAppTableRecordRequestBody.builder()
                          .records(request_body).build()) \
            .build()
        response: BatchUpdateAppTableRecordResponse = self.client.bitable.v1.app_table_record.batch_update(request)
        if not response.success():
            logger.error(
                f"client.bitable.v1.app_table_record.batch_update failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
            return False
        return True

    def get_wiki_node_info(self, node_token, obj_type="wiki"):
        """
        获取知识空间节点信息
        
        参数:
            node_token: 节点token
            obj_type: 对象类型，默认为"wiki"
            
        返回:
            dict: 节点信息，包含obj_type和obj_token
        """
        try:
            # 启用调试日志
            logger.debug(f"准备获取知识空间节点信息，node_token: {node_token}, obj_type: {obj_type}")
            
            # 构造请求对象
            request = GetNodeSpaceRequest.builder() \
                .token(node_token) \
                .obj_type(obj_type) \
                .build()
            
            # 输出请求信息
            logger.debug(f"请求参数: token={node_token}, obj_type={obj_type}")

            # 发起请求
            response = self.client.wiki.v2.space.get_node(request)
            
            # 输出原始响应
            # logger.debug(f"API响应状态: {response.success()}, code: {response.code}, msg: {response.msg}")
            
            # 查看原始响应内容
            if hasattr(response, 'raw') and hasattr(response.raw, 'content'):
                raw_content = response.raw.content
                # logger.debug(f"原始响应内容: {raw_content[:200]}...")

            # 处理失败返回
            if not response.success():
                logger.error(
                    f"client.wiki.v2.space.get_node failed, code: {response.code}, msg: {response.msg}, "
                    f"log_id: {response.get_log_id()}")
                return None

            # 解析响应数据
            response_json = JSON.marshal(response.data)
            # logger.debug(f"响应数据: {response_json}")
            data = json.loads(response_json)
            
            # 关键修复: 数据在"node"字段内部
            node_data = data.get("node", {})
            # logger.debug(f"节点数据: {node_data}")
            
            result = {
                "obj_type": node_data.get("obj_type"),
                "obj_token": node_data.get("obj_token"),
                "space_id": node_data.get("space_id"),
                "node_token": node_data.get("node_token"),
                "title": node_data.get("title"),
                "owner_id": node_data.get("owner"),  # 注意: 字段名是"owner"而不是"owner_id"
                "has_child": node_data.get("has_child")
            }
            
            logger.debug(f"解析后的结果: {result}")
            return result
            
        except Exception as e:
            logger.exception(f"获取知识空间节点信息异常: {str(e)}")
            return None

    def list_app_table(self, app_token, page_size=20):
        """
        获取应用下的所有多维表格
        
        参数:
            app_token: 应用token（如果是wiki下的资源，需要先通过get_wiki_node_info获取obj_token）
            page_size: 每页记录数，默认20
            
        返回:
            dict: 表格列表，失败返回空字典
        """
        try:
            # 构造请求对象
            request = ListAppTableRequest.builder() \
                .app_token(app_token) \
                .page_size(page_size) \
                .build()
            
            # 发起请求
            response = self.client.bitable.v1.app_table.list(request)
            
            # 处理失败返回
            if not response.success():
                logger.error(
                    f"client.bitable.v1.app_table.list failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
                return {}

            # 解析响应数据
            data = json.loads(JSON.marshal(response.data))
            return data
            
        except Exception as e:
            logger.error(f"获取多维表格列表异常: {str(e)}")
            return {}

    def search_table_record(self, app_token, table_id, search_params=None, page_size=10, page_token=None):
        """
        查询表格记录
        
        参数:
            app_token: 应用token
            table_id: 表格ID
            search_params: 搜索参数，默认为None，表示查询所有记录
            page_size: 每页记录数量，默认10
            page_token: 分页标记，默认为None
            
        返回:
            dict: 查询结果，包含记录列表和分页信息，失败返回空字典
        """
        try:
            logger.info(f"开始查询表格记录: app_token={app_token}, table_id={table_id}")
            
            # 构建请求体
            request_body_builder = SearchAppTableRecordRequestBody.builder()
            
            # 如果有搜索参数，添加到请求体
            if search_params:
                # 搜索参数示例:
                # {
                #   "filter": {
                #     "conjunction": "and",
                #     "conditions": [
                #       {"field_name": "字段名", "operator": "is", "value": "值"}
                #     ]
                #   },
                #   "sort": [
                #     {"field_name": "字段名", "order": "desc"}
                #   ]
                # }
                if "filter" in search_params:
                    request_body_builder.filter(search_params["filter"])
                if "sort" in search_params:
                    request_body_builder.sort(search_params["sort"])
                if "field_names" in search_params:
                    request_body_builder.field_names(search_params["field_names"])
            
            # 构建完整请求
            request_builder = SearchAppTableRecordRequest.builder() \
                .app_token(app_token) \
                .table_id(table_id) \
                .page_size(page_size) \
                .request_body(request_body_builder.build())
            
            # 添加分页标记（如果有）
            if page_token:
                request_builder.page_token(page_token)
            
            # 完成请求构建
            request = request_builder.build()
            
            # 发起请求
            response = self.client.bitable.v1.app_table_record.search(request)
            
            # 处理失败返回
            if not response.success():
                logger.error(
                    f"表格记录搜索失败: code={response.code}, msg={response.msg}, "
                    f"log_id={response.get_log_id()}")
                
                # 如果有原始响应内容，记录更详细的错误信息
                if hasattr(response, 'raw') and hasattr(response.raw, 'content'):
                    error_detail = json.loads(response.raw.content)
                    logger.error(f"详细错误: {json.dumps(error_detail, ensure_ascii=False)}")
                
                return {}
            
            # 处理成功响应
            response_data = json.loads(JSON.marshal(response.data))
            logger.info(f"查询到 {len(response_data.get('items', []))} 条记录")
            
            return response_data
            
        except Exception as e:
            logger.exception(f"查询表格记录异常: {str(e)}")
            return {}
    
    def search_table_all_records(self, app_token, table_id, search_params=None, page_size=100):
        """
        查询表格中的所有符合条件的记录，自动处理分页
        
        参数:
            app_token: 应用token
            table_id: 表格ID
            search_params: 搜索参数，默认为None
            page_size: 每页记录数量，默认100
            
        返回:
            list: 所有符合条件的记录列表，失败返回空列表
        """
        try:
            all_records = []
            page_token = None
            
            while True:
                # 查询当前页数据
                page_result = self.search_table_record(
                    app_token,
                    table_id,
                    search_params,
                    page_size,
                    page_token
                )
                
                # 如果查询失败或结果为空，结束循环
                if not page_result or "items" not in page_result:
                    break
                
                # 添加当前页的记录
                records = page_result.get("items", [])
                all_records.extend(records)
                
                # 检查是否有更多数据
                if not page_result.get("has_more", False):
                    break
                
                # 获取下一页的标记
                page_token = page_result.get("page_token")
                if not page_token:
                    logger.warning("分页标记缺失，无法获取更多数据")
                    break
                
                # logger.debug(f"继续获取下一页数据，当前已获取 {len(all_records)} 条记录")
            
            logger.info(f"总共获取到 {len(all_records)} 条记录")
            return all_records
            
        except Exception as e:
            logger.exception(f"获取所有表格记录异常: {str(e)}")
            return []


if __name__ == "__main__":
    # 初始化客户端
    lark = Lark()
    print("初始化飞书客户端成功")
    
    try:
        # 1. 首先确认app_id和app_secret是否设置正确
        print(f"App ID: {LARK_APP_ID[:4]}***{LARK_APP_ID[-4:] if LARK_APP_ID else ''}")
        print(f"App Secret: {LARK_APP_SECRET[:4]}***{LARK_APP_SECRET[-4:] if LARK_APP_SECRET else ''}")
        
        # 2. 获取tenant_access_token进行验证
        token = lark.get_tenant_access_token()
        if token:
            print(f"成功获取租户访问令牌: {token[:10]}...")
        else:
            print("获取租户访问令牌失败，请检查APP_ID和APP_SECRET配置")
            exit(1)
        
        # 3. 演示查询表格记录
        app_token = "Mo7obvrgtaIMqRsZkdwc6UUFnCg"  # 替换为实际的应用token
        table_id = "tblLjRnZR0d9mjzm"  # 替换为实际的表格ID
        
        print(f"\n开始查询表格记录，app_token: {app_token}, table_id: {table_id}")
        #
        # # 3.1 简单查询，不带过滤条件
        # simple_result = lark.search_table_record(app_token, table_id)
        # print(f"\n简单查询结果: {json.dumps(simple_result, ensure_ascii=False, indent=2)}")
        #
        # # 3.2 带过滤条件的查询 (示例: 查询状态为"进行中"的任务)
        # search_params = {}
        #
        # print("\n开始执行带条件的查询...")
        # filtered_result = lark.search_table_record(app_token, table_id, search_params)
        # print(f"条件查询结果: {json.dumps(filtered_result, ensure_ascii=False, indent=2)}")
        
        # 3.3 获取所有记录 (自动处理分页)
        print("\n开始获取所有符合条件的记录...")
        all_records = lark.search_table_all_records(app_token, table_id, {})
        print(f"总共获取到 {len(all_records)} 条记录")
        
        # 只展示前5条记录
        if all_records:
            print(f"前5条记录示例: {json.dumps(all_records[:5], ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        import traceback
        print(f"\n运行过程中发生错误: {str(e)}")
        traceback.print_exc()
