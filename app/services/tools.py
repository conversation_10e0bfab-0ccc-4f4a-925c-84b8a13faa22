import requests
from loguru import logger
from app.core.config import settings


def compute_view_mean_and_median_k(videos: list):
    """
    计算视频播放量的平均数和中位数
    Args:
        videos: 视频列表，每个视频包含 play_count 和 is_pinned 属性
    Returns:
        tuple: (平均播放量(k), 中位数播放量(k))
    """
    # 过滤掉置顶视频
    non_pinned_videos = [video for video in videos if not video.get('is_pinned', False)]
    
    # 只取前15个视频
    selected_videos = non_pinned_videos[:15]
    
    # 如果没有视频，返回0
    if not selected_videos:
        return 0, 0
    
    # 获取播放量列表
    play_counts = [video['play_count'] for video in selected_videos]
    
    # 计算平均数
    avg = sum(play_counts) / len(play_counts)
    
    # 计算中位数
    sorted_counts = sorted(play_counts)
    mid = len(sorted_counts) // 2
    if len(sorted_counts) % 2 == 0:
        median = (sorted_counts[mid - 1] + sorted_counts[mid]) / 2
    else:
        median = sorted_counts[mid]
    
    # 转换为k单位（除以1000）
    avg_in_k = round(avg / 1000, 2)
    median_in_k = round(median / 1000, 2)
    
    return avg_in_k, median_in_k


def send_webhook(filter_name: str, status: str, msg: str = None, unique_count: int = None, email_count: int = None, task_id: str = None):
    """
    通过 webhook 更新飞书工具状态
    """
    logger.info("飞书工具状态更新成功 (暂时禁用)")
    # url = settings.FEISHU_WEB_HOOK_URL
    # payload = {
    #     "status": status,
    #     "filter_name": filter_name,
    #     "msg": msg,
    #     "unique_count": unique_count,
    #     "email_count": email_count
    # }
    
    # # 如果提供了task_id，添加到payload中
    # if task_id:
    #     payload["task_id"] = task_id
        
    # res = requests.post(url=url, json=payload)
    # result = res.json()
    # if result.get("code") == 0:
    #     logger.info("飞书工具状态更新成功")
    # else:
    #     logger.error(f"飞书工具状态更新失败, msg:{result.get('msg')} \n data:{result.get('data')}")


def fte_compute(project, task, num=None):
    """发送 FTE 计算请求"""
    url = "https://eojwgxpm0t7clzl.m.pipedream.net"
    body = {
        "task": task,
        "project": project
    }
    if num:
            body["number"] = num

    requests.post(url, json=body)