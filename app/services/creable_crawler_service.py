#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project : kol-python
<AUTHOR> 小林同学
@Date    : 2025/3/25 下午6:16 
@Docs    : 通过查询条件从 Creable 聚合平台获取 kol 数据
'''

import logging
import re
import time
from typing import Dict, List, Optional

import requests


class CreableService:
    """
    Creable平台API服务类
    提供与Creable平台交互的方法，包括创作者搜索、解锁和数据获取
    """

    def __init__(self, token: str, logger=None):
        """
        初始化Creable服务
        
        Args:
            token: API认证令牌
            logger: 日志对象，如果不提供则使用内置日志
        """
        self.base_url = "https://creable.com/app/api"
        self.workspace_slug = "meiling-advertiser"
        self.headers = {
            'accept': 'application/json',
            'authorization': token,
            'content-type': 'application/json',
            'origin': 'https://creable.com',
            'referer': 'https://creable.com/app/meiling-advertiser/find-creators'
        }
        self.session = requests.Session()
        
        # 设置日志
        self.logger = logger or logging.getLogger("creable_service")
        if not logger:
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict:
        """发送API请求"""
        url = f"{self.base_url}/{endpoint}"
        try:
            kwargs['headers'] = kwargs.get('headers', self.headers)
            response = self.session.request(method=method, url=url, **kwargs)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"请求失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def search_creators(self, filter_data: Dict) -> List[Dict]:
        """搜索创作者"""
        valid_creators = []
        current_page = 0

        while True:
            filter_data["page"] = current_page
            self.logger.info(f'正在处理：第 {current_page + 1} 页')

            response = self._make_request("POST", "advertiser/search-creators", json=filter_data)
            if not response.get("success"):
                break

            creators = response["data"]["creators"]
            for creator in creators:
                user_profile = creator.get('account', {}).get('user_profile', {})
                if all(user_profile.get(field) for field in ['user_id', 'username', 'fullname', 'url']):
                    valid_creators.append(creator)

            paging_info = response["data"]["paging"]
            if paging_info["isLastPage"]:
                break
            current_page = paging_info["nextPage"]

        self.logger.info(f"总数据量: {len(valid_creators)}")
        return valid_creators

    def get_unblur_creators(self, creator_ids: List[str], batch_size: int = 20) -> Dict:
        """批量获取解锁的创作者信息"""
        all_new_data = {}
        
        for i in range(0, len(creator_ids), batch_size):
            batch_ids = creator_ids[i:i + batch_size]
            payload = {
                "creators": [{"searchResultID": id_} for id_ in batch_ids],
                "workspaceSlug": self.workspace_slug
            }
            
            response = self._make_request("POST", "advertiser/unblur-creators", json=payload)
            if response.get("success"):
                new_data = response.get("data", {}).get("newData", {})
                all_new_data.update(new_data)
            
            time.sleep(1)  # 简单的请求间隔

        return {
            "success": bool(all_new_data),
            "data": all_new_data
        }

    def bulk_unlock_creators(self, account_ids: List[str], platform: str) -> List[Dict]:
        """批量解锁创作者账号"""
        results = []
        
        for i in range(0, len(account_ids), 10):
            batch = account_ids[i:i + 10]
            payload = {
                "workspaceSlug": self.workspace_slug,
                "platform": platform,
                "influencerAccountIDs": batch,
                "campaignUID": None
            }
            
            response = self._make_request("POST", "advertiser/bulk-unlock-creators", json=payload)
            results.append(response)
            time.sleep(1)

        return results

    def get_creator_slug(self, creator_ids: List[Dict[str, str]], platform: str) -> List[Dict]:
        """获取创作者的slug标识"""
        result_data = []
        pattern = r'/unlocked/([^/]+)$'

        for id_ in creator_ids:
            payload = {
                "workspaceSlug": self.workspace_slug,
                "creatorID": id_['creator_id'],
                "platform": platform
            }

            response = self._make_request("POST", "advertiser/get-creator-report-link", json=payload)
            if response.get("success"):
                link = response["data"]["creatorReportLink"]
                if match := re.search(pattern, link):
                    result_data.append({
                        "record_id": id_['record_id'],
                        "fields": {"Slug": match.group(1)}
                    })

        return result_data

    def get_creator_report(self, slug: str, platform: str) -> Dict:
        """获取创作者报告数据"""
        params = f"slug={slug}&workspaceSlug={self.workspace_slug}&platform={platform}"
        return self._make_request("GET", "advertiser/get-creator-report", params=params)

    def get_batch_creator_reports(self, slug_ids: List[str], platform: str) -> List[Dict]:
        """批量获取创作者报告数据"""
        return [
            report for slug in slug_ids
            if (report := self.get_creator_report(slug, platform)).get("success")
        ]

    def unlock_creator_from_app(self, user_id: str, platform: str, search_data: Dict, token: str) -> Optional[Dict]:
        """从应用解锁创作者"""
        payload = {
            "userID": user_id,
            "platform": platform,
            "workspaceSlug": self.workspace_slug,
            "searchData": search_data,
            "token": token
        }
        return self._make_request("POST", "advertiser/unlock-creator-from-app", json=payload)


if __name__ == "__main__":
    creable = CreableService(token="eyJhbGciOiJSUzI1NiIsImtpZCI6ImEwODA2N2Q4M2YwY2Y5YzcxNjQyNjUwYzUyMWQ0ZWZhNWI2YTNlMDkiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.sHebkhdDnJuXIC3khE9lVn1mxgQPlKm51j4g_JuJDcu3Gx5izc6YlMsHf63LSbks0IrRGGkkuqVksIzHJ3Qx4gQO6JgCHrTZFFhJC2wa3IqLMhPuDdD-TUCAvMgRZB_diCivpkDv03S3hteHy03GfcH40gZq0nVGvtfGAoOwGgTlgCHAHg4UxiA-BUGNLLD7yNw0DhFhTS_jJzDYIgXAt9yoQQv492Zpdo9r9_sMNLfJC9b-hPvvGnQtbvUZssg874MqI_u3pQI9vo08FiUkvW2U6-U0OGX5bE8l7QweDHWsmAv425M_gOtTcklHFRAQBnmPRiJgcBXHEDOw_V8rnw")
    filter_data = {
        "page": 0,
        "organizationUID": "CVDSvv2soiVJjRHusX58",
        "filter": {
            "followers": {
                "left_number": 150000,
                "right_number": 250000
            },
            "views": {
                "left_number": 100000,
                "right_number": None
            },
            "geo": [
                {
                    "id": 148838
                }
            ],
            "audience_geo": [
                {
                    "id": 148838,
                    "weight": 0.5
                }
            ],
            "with_contact": [
                {
                    "type": "email",
                    "action": "must"
                }
            ],
            "engagement_rate": {
                "value": 0.03,
                "operator": "gte"
            },
            "text": "Athlete",
            "last_posted": 30
        },
        "platform": "tiktok",
        "sort": {
            "field": "followers",
            "direction": "desc"
        }
    }
    results = creable.search_creators(filter_data)
    import json
    print(json.dumps(results, indent=2))
