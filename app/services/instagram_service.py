#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project : kol-python
<AUTHOR> 小林同学
@Date    : 2025/3/25 下午6:16 
@Docs    : Instagram服务模块, 提供封装好的Instagram数据爬取服务。
'''
import os
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlparse

# 导入第三方依赖
from dotenv import load_dotenv
from instagrapi import Client
from tenacity import retry, stop_after_attempt, wait_exponential

# ===== 工具函数 =====

def extract_username_from_url(url: str) -> str:
    """从 Instagram URL 中提取用户名"""
    try:
        parsed_url = urlparse(url)
        if not parsed_url.netloc or "instagram.com" not in parsed_url.netloc:
            raise ValueError("无效的 Instagram URL")
            
        path_parts = [p for p in parsed_url.path.split('/') if p]
        if not path_parts:
            raise ValueError("无法从 URL 中提取用户名")
            
        username = path_parts[0]
        return username
        
    except Exception as e:
        raise ValueError(f"URL 解析失败: {str(e)}")


def validate_instagram_url(url: str) -> bool:
    """验证是否为有效的 Instagram URL"""
    try:
        parsed_url = urlparse(url)
        return bool(parsed_url.netloc and "instagram.com" in parsed_url.netloc)
    except:
        return False


# ===== 客户端类 =====

class InstagramClient:
    """Instagram 客户端单例类"""
    
    _instance = None
    _client = None
    _last_request_time = 0
    _min_request_interval = 1.0  # 最小请求间隔（秒）
    _is_logged_in = False
    
    def __new__(cls) -> 'InstagramClient':
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化客户端（只在第一次创建实例时执行）"""
        if not self._client:
            load_dotenv()
            self.username = os.getenv('IG_USERNAME')
            self.password = os.getenv('IG_PASSWORD')
            
            if not self.username or not self.password:
                raise ValueError("请在 .env 文件中设置 IG_USERNAME 和 IG_PASSWORD")
                
            self._client = Client()
            
    def _ensure_login(self) -> None:
        """确保客户端已登录"""
        if not self._is_logged_in or not self._check_session():
            self._login()
            
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _login(self) -> None:
        """登录到 Instagram，带有重试机制"""
        try:
            self._client.login(self.username, self.password)
            self._is_logged_in = True
        except Exception as e:
            self._is_logged_in = False
            raise
            
    def _check_session(self) -> bool:
        """检查当前会话是否有效"""
        try:
            self._client.user_info(self._client.user_id)
            return True
        except Exception:
            return False
            
    def _wait_for_rate_limit(self) -> None:
        """等待以遵守速率限制"""
        elapsed = time.time() - self._last_request_time
        if elapsed < self._min_request_interval:
            time.sleep(self._min_request_interval - elapsed)
        self._last_request_time = time.time()
        
    def execute(self, operation, *args, **kwargs):
        """执行 Instagram API 操作"""
        self._ensure_login()
        self._wait_for_rate_limit()
        
        try:
            return operation(*args, **kwargs)
        except Exception as e:
            self._is_logged_in = False
            raise
            
    def cleanup(self) -> None:
        """清理资源"""
        if self._is_logged_in and self._client:
            try:
                self._client.logout()
                self._is_logged_in = False
            except Exception:
                pass
                
    @property
    def client(self) -> Client:
        """获取底层的 Instagram 客户端实例"""
        return self._client


# ===== 爬虫服务类 =====

class InstagramScraperService:
    """Instagram 爬虫服务类
    
    支持简单的调用方式：
    
    ```python
    # 方式1：使用 with 语句
    with InstagramScraperService(username_or_url) as scraper:
        user_info, reels = scraper.scrape_sync()
        
    # 方式2：手动管理资源
    scraper = InstagramScraperService(username_or_url)
    try:
        user_info, reels = scraper.scrape_sync()
    finally:
        scraper.cleanup()
    ```
    """
    
    def __init__(self, username_or_url: str, max_reels: int = 50):
        """初始化 Instagram 爬虫服务
        
        Args:
            username_or_url: Instagram 用户名或用户主页 URL
            max_reels: 最大获取 Reels 数量，默认为50
        """
        # 处理输入可能是 URL 的情况
        if validate_instagram_url(username_or_url):
            self._username = extract_username_from_url(username_or_url)
        else:
            self._username = username_or_url
            
        self._max_reels = max(1, min(max_reels, 100))  # 限制在 1-100 之间
        self._client = InstagramClient()
        
    def __enter__(self):
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        pass
    
    @classmethod
    def create(cls, username_or_url: str, max_reels: int = 50) -> 'InstagramScraperService':
        """创建爬虫实例的工厂方法
        
        Args:
            username_or_url: Instagram 用户名或主页 URL
            max_reels: 最大获取 Reels 数量
            
        Returns:
            InstagramScraperService 实例
        """
        return cls(username_or_url, max_reels)
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _get_user_info(self) -> Dict[str, Any]:
        """获取用户基本信息"""
        def _get_user_info():
            user_id = self._client.client.user_id_from_username(self._username)
            return self._client.client.user_info(user_id)
            
        user_info = self._client.execute(_get_user_info)
        
        return {
            "username": user_info.username,
            "full_name": user_info.full_name,
            "followers": user_info.follower_count,
            "following": user_info.following_count,
            "biography": user_info.biography
        }
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _get_user_reels(self) -> List[Dict[str, Any]]:
        """获取用户 Reels 视频信息"""
        def _get_user_reels():
            user_id = self._client.client.user_id_from_username(self._username)
            return self._client.client.user_clips(user_id, amount=self._max_reels)
            
        reels = self._client.execute(_get_user_reels)
        
        reels_info = []
        for reel in reels:
            reel_data = {
                "id": str(reel.pk),
                "code": reel.code,
                "url": f"https://www.instagram.com/reel/{reel.code}/",
                "caption": reel.caption_text,
                "play_count": reel.play_count,
                "like_count": reel.like_count,
                "comment_count": reel.comment_count,
                "created_at": reel.taken_at.strftime("%Y-%m-%d %H:%M:%S") if reel.taken_at else None
            }
            reels_info.append(reel_data)
        
        # 按发布时间排序
        reels_info.sort(key=lambda x: x["created_at"] if x["created_at"] else "", reverse=True)
        return reels_info
            
    def scrape_sync(self) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """同步方式获取用户信息和 Reels 列表
        
        Returns:
            包含用户信息和 Reels 列表的元组
            
        Raises:
            ValueError: 获取数据失败
        """
        try:
            # 获取用户信息
            user_info = self._get_user_info()
            
            # 获取 Reels 列表
            reels = self._get_user_reels()
            
            return user_info, reels
            
        except Exception as e:
            raise ValueError(f"获取数据失败: {str(e)}")
    
    def scrape(self) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """获取用户信息和 Reels 列表（与 scrape_sync 相同，保持接口一致性）
        
        Returns:
            包含用户信息和 Reels 列表的元组
        """
        return self.scrape_sync()
        
    def cleanup(self):
        """清理资源（为保持接口一致性）"""
        pass


# ===== 简易服务函数 =====

def scrape_instagram_user(username_or_url: str, max_reels: int = 50) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
    """爬取 Instagram 用户信息和 Reels 列表
    
    Args:
        username_or_url: Instagram 用户名或主页 URL
        max_reels: 最大获取 Reels 数量，默认为50
        
    Returns:
        包含用户信息和 Reels 列表的元组
        
    Raises:
        ValueError: 获取数据失败
    """
    with InstagramScraperService(username_or_url, max_reels) as scraper:
        return scraper.scrape_sync()


if __name__ == "__main__":
    user_info, reels = scrape_instagram_user("https://www.instagram.com/loseitwithnat/")
    print("用户信息:", user_info)
    print(f"获取到 {len(reels)} 个视频")
