import json
from typing import List, <PERSON><PERSON>
from openai import OpenAI
from loguru import logger


def email_and_keywords_from_bio(bio_text: str = "") -> Tu<PERSON>[List[str], str]:
    """
    使用 OpenAI 从 bio 文本中提取关键词
    
    Args:
        bio_text: 要分析的 bio 文本
        
    Returns:
        关键词列表，如果处理失败则返回空列表
    """
    if not bio_text or not isinstance(bio_text, str):
        logger.warning("简介为空或者不是字符串")
        return [], ""

    try:
        client = OpenAI(
            api_key="sk-srqzisugylaszaqrxmskqopgrqjtotyaaljquhlzawdsgfqa",
            base_url="https://api.siliconflow.cn/v1"
        )
        # 使用 OpenAI 提取关键词
        response = client.chat.completions.create(
            model="deepseek-ai/DeepSeek-V3",
            messages=[
                {
                    "role": "system",
                    "content": "你是一名营销专家。请根据这位网红的简介 (bio) 总结其内容的主要话题和摘取email信息。主要话题限制为三个,每个话题用一个词概括。"
                },
                {
                    "role": "user", 
                    "content": """
                    要求：
                    - 如果简介不包含相关信息（例如，只有姓名、大学、邮箱等），则不进行总结。
                    - 尽可能使用网红的原文。
                    - 常用话题包括：健身 (fitness)、生活方式 (lifestyle)、饮食 (diet)、健康 (health)、教练 (coach)（可供使用）。
                    - 使用简介中的原语种输出。
                    - 提取简介中的邮箱, 如果没有邮箱返回字符串 "null"。
                    - 注意如果简介中不包含邮箱，一定要返回 "null", 不能凭空捏造邮箱（这很重要）。
                    - 如果简介中只存在邮箱, topics话题返回空数组即可。

                    输出格式：
                    {
                        "topics":["Topic 1", "Topic 2", "Topic 3"],
                        "email": "<EMAIL>"
                    }
                    如果没有相关话题或者email,则输出:
                    {
                        "topics":[],
                        "email": "null"
                    }
                    以下是网红的简介：
                    bio_text: %s
                    """ % bio_text
                }
            ],
            response_format={"type": "json_object"}
        )

        # 解析响应数据
        result = response.choices[0].message.content
        json_data = json.loads(result)
        topics = json_data.get("topics", [])
        email = json_data.get("email", "null")
        if email == "null":
            email = None
        logger.debug(f"大模型从 bio 中提取 {len(topics)} 个关键词和 email: {email}")
        return topics, email
    except Exception as e:
        logger.error(f"提取关键词时发生错误: {type(e).__name__}: {str(e)}")
        return [], ""


# 示例用法
if __name__ == "__main__":
    result, email = email_and_keywords_from_bio("""
Story telling, Horror Fan, it's not that serious. <EMAIL>                              
    """)
    print(result)
    print(email)
