#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
@Project : kol-python
<AUTHOR> Swift
@Date    : 2025/3/25
@Docs    : 通过 Instagram 用户名获取用户信息和Reels（第三方API）
'''

from typing import Any, Dict, List, Optional
import requests
from loguru import logger
import datetime
import time
import threading
from functools import wraps


class RateLimiter:
    """全局速率限制器，确保API调用不超过限制"""

    def __init__(self, max_calls_per_second: int = 10):
        self.max_calls_per_second = max_calls_per_second
        self.min_interval = 1.0 / max_calls_per_second  # 最小间隔时间
        self.last_call_time = 0
        self.lock = threading.Lock()

    def wait_if_needed(self):
        """如果需要，等待以遵守速率限制"""
        with self.lock:
            current_time = time.time()
            elapsed = current_time - self.last_call_time

            if elapsed < self.min_interval:
                sleep_time = self.min_interval - elapsed
                time.sleep(sleep_time)

            self.last_call_time = time.time()


# 全局速率限制器实例，所有InstagramAPIClient实例共享
_global_rate_limiter = RateLimiter(max_calls_per_second=10)


def retry_on_rate_limit(max_retries: int = 3, base_delay: float = 1.0):
    """重试装饰器，处理速率限制和其他错误"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries):
                try:
                    # 应用速率限制
                    _global_rate_limiter.wait_if_needed()
                    return func(*args, **kwargs)
                except requests.exceptions.HTTPError as e:
                    last_exception = e
                    status_code = e.response.status_code if e.response else 0

                    if status_code in [400, 429]:  # Bad Request 或 Too Many Requests
                        delay = base_delay * (2 ** attempt)  # 指数退避
                        if status_code == 429:
                            logger.warning(f"API速率限制，等待 {delay:.2f} 秒后重试 (尝试 {attempt + 1}/{max_retries})")
                        elif status_code == 400:
                            logger.warning(f"API请求错误(400)，等待 {delay:.2f} 秒后重试 (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(delay)
                        continue
                    else:
                        # 其他HTTP错误直接抛出
                        raise
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        delay = base_delay * (2 ** attempt)
                        logger.warning(f"请求失败，等待 {delay:.2f} 秒后重试 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                        time.sleep(delay)
                        continue
                    else:
                        raise

            # 所有重试都失败了
            raise last_exception
        return wrapper
    return decorator


class InstagramAPIClient:
    """
    Instagram API客户端类

    支持简单的调用方式：

    ```python
    # 方式1：使用 with 语句
    with InstagramAPIClient(username) as client:
        user_info = client.fetch_user_info(username)
        reels = client.fetch_user_reels(username, max_reels=20)

    # 方式2：手动管理资源
    client = InstagramAPIClient(username)
    try:
        user_info = client.fetch_user_info(username)
        reels = client.fetch_user_reels(username, max_reels=20)
    finally:
        client.cleanup_sync()

    # 方式3：一次性获取用户信息和视频
    client = InstagramAPIClient(username)
    user_info, reels = client.scrape_sync(username, max_reels=20)
    ```
    """
    # API 配置常量
    BASE_URL = "https://api.tikhub.io/api/v1/instagram/web_app"
    API_TOKEN = "Bearer xacqaFZO6SIXljVf6mKMul9v/SEVtrzmuotC+SzipUZ5r1dVYtduzyCZUw=="  # KOL

    def __init__(self, username: str):
        """
        初始化API客户端实例

        Args:
            username: Instagram 用户名
        """
        self._username = username
        self._session: Optional[requests.Session] = None
        self._initialized: bool = False

    def __enter__(self) -> "InstagramAPIClient":
        """上下文管理器入口"""
        self.init_sync()
        return self

    def __exit__(self, _exc_type, _exc_val, _exc_tb) -> None:
        """上下文管理器退出"""
        self.cleanup_sync()

    @classmethod
    def configure_rate_limit(cls, max_calls_per_second: int = 10):
        """
        配置全局速率限制

        Args:
            max_calls_per_second: 每秒最大请求数，默认10
        """
        global _global_rate_limiter
        _global_rate_limiter = RateLimiter(max_calls_per_second=max_calls_per_second)
        logger.info(f"Instagram API 速率限制已设置为每秒 {max_calls_per_second} 次请求")

    def init_sync(self) -> None:
        """初始化API客户端资源"""
        if not self._initialized:
            self._session = requests.Session()
            self._session.headers.update({
                "accept": "application/json",
                "Authorization": self.API_TOKEN,
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                              "AppleWebKit/537.36 (KHTML, like Gecko) "
                              "Chrome/122.0.0.0 Safari/537.36",
            })
            self._initialized = True

    def cleanup_sync(self) -> None:
        """清理API客户端资源"""
        if self._session:
            self._session.close()
            self._session = None
        self._initialized = False

    def _extract_user_info(self, user_info_data: dict) -> dict:
        """
        从Instagram用户信息API返回中提取标准化用户信息

        Args:
            user_info_data: Instagram用户信息API返回的data字段

        Returns:
            标准化的用户信息字典
        """
        return {
            "username": user_info_data.get("full_name", ""),  # Instagram用户名
            "kol_id": user_info_data.get("username", ""),   # 真实姓名
            "followers_count": user_info_data.get("follower_count", 0),  # 粉丝数
            "likes_count": 0,  # Instagram接口无总获赞数，统一填0
            "bio": user_info_data.get("biography", ""),      # 简介
        }

    def _extract_video_info(self, reels_data: list) -> list:
        """
        从Instagram Reels数据中提取标准化视频信息列表，字段与TikTok保持一致

        Args:
            reels_data: Instagram Reels API返回的items列表

        Returns:
            标准化的视频信息字典列表
        """
        videos = []  # 用于存储所有视频信息
        for item in reels_data:
            media = item.get("media", {})  # 获取视频的media信息
            video_id = media.get("pk", "")  # 视频唯一ID
            shortcode = media.get("code", "")  # Instagram视频短码
            play_count = media.get("play_count", media.get("ig_play_count", 0))  # 播放量
            is_pinned = False  # Instagram无置顶，统一为False
            # 创建视频信息字典，字段与TikTok保持一致
            video_info = {
                "video_id": video_id,  # 视频唯一ID
                "play_count": play_count,  # 播放量
                "is_pinned": is_pinned,  # 是否置顶，Instagram无置顶
            }
            # 视频分享链接，格式与TikTok保持一致
            video_info["share_url"] = f"https://www.instagram.com/reel/{shortcode}/"
            # 视频描述
            desc = ""
            if media.get("caption") and isinstance(media["caption"], dict):
                desc = media["caption"].get("text", "")
            video_info["desc"] = desc  # 视频描述
            video_info["desc_language"] = ""  # Instagram无语言字段
            # 视频播放地址
            video_url = ""
            video_versions = media.get("video_versions", [])
            if video_versions and isinstance(video_versions, list):
                video_url = video_versions[0].get("url", "")
            video_info["video_url"] = video_url  # 视频播放地址
            # 音乐信息，Instagram接口无，留空
            video_info["music_url"] = ""
            # 详细的互动数据
            video_info["likes_count"] = media.get("like_count", 0)  # 点赞数
            video_info["comments_count"] = media.get("comment_count", 0)  # 评论数
            video_info["shares_count"] = 0  # Instagram无，统一为0
            video_info["collect_count"] = 0  # Instagram无，统一为0
            # 创建时间(将时间戳转换为数据库DateTime格式)
            create_time = media.get("taken_at", 0)
            if create_time:
                video_info["create_time"] = datetime.datetime.fromtimestamp(create_time)
            else:
                video_info["create_time"] = None
            # hashtag 话题标签，正则提取
            import re
            hashtags = re.findall(r"#(\w+)", desc) if desc else []
            if hashtags:
                video_info["hashtags"] = hashtags
            videos.append(video_info)
        return videos

    @retry_on_rate_limit(max_retries=3, base_delay=1.0)
    def fetch_user_info(self, username: str) -> dict:
        """
        获取用户信息

        Args:
            username: Instagram 用户名

        Returns:
            标准化的用户信息字典
        """
        if not self._initialized:
            self.init_sync()

        try:
            url = f"{self.BASE_URL}/fetch_user_info_by_username_v2"
            user_info_resp = self._session.get(
                url,
                params={"username": username},
                timeout=30,
            )
            user_info_resp.raise_for_status()
            user_info_data = user_info_resp.json().get("data", {})
            return self._extract_user_info(user_info_data)
        except Exception as e:
            logger.error(f"获取Instagram用户信息失败: {str(e)}")
            raise ValueError(f"获取Instagram用户信息失败: {str(e)}")

    @retry_on_rate_limit(max_retries=3, base_delay=1.0)
    def _fetch_reels_page(self, username: str, max_id: str = "", count: int = 12) -> tuple:
        """
        获取单页Reels数据（内部方法，带速率限制）

        Args:
            username: Instagram 用户名
            max_id: 分页参数
            count: 每页数量

        Returns:
            (reels_data, new_max_id) 元组
        """
        url = f"{self.BASE_URL}/fetch_user_reels_by_username_v3"
        params = {"username": username, "count": count}
        if max_id:
            params["max_id"] = max_id

        reels_resp = self._session.get(url, params=params, timeout=30)
        reels_resp.raise_for_status()
        data = reels_resp.json().get("data", {})
        reels_data = data.get("items", [])

        # 获取新的max_id
        paging_info = data.get("paging_info", {})
        new_max_id = paging_info.get("max_id", "")

        return reels_data, new_max_id

    def fetch_user_reels(self, username: str, max_reels: int = 12) -> list:
        """
        获取用户Reels视频列表

        Args:
            username: Instagram 用户名
            max_reels: 获取Reels数量，默认20

        Returns:
            标准化的视频信息字典列表
        """
        if not self._initialized:
            self.init_sync()

        try:
            reels = []  # 用于存储所有获取到的视频信息
            max_id = ""  # 分页参数，初始为空

            while len(reels) < max_reels:
                # 每次最多请求12条，防止超出API限制
                count = min(12, max_reels - len(reels))

                # 调用带速率限制的内部方法
                reels_data, new_max_id = self._fetch_reels_page(username, max_id, count)

                # 如果本次没有获取到数据，说明没有更多数据，提前终止循环
                if not reels_data:
                    break

                # 提取并合并本次获取到的视频信息
                reels.extend(self._extract_video_info(reels_data))

                # 如果没有新的max_id，说明没有更多数据
                if not new_max_id:
                    break

                max_id = new_max_id

            # 最终只返回max_reels数量的视频
            return reels[:max_reels]
        except Exception as e:
            logger.error(f"获取Instagram Reels失败: {str(e)}")
            raise ValueError(f"获取Instagram Reels失败: {str(e)}")

    def scrape_sync(self, username: str, max_reels: int = 12) -> tuple:
        """
        同步方式获取用户信息和Reels视频列表，返回结构与TikTok对齐

        Args:
            username: Instagram 用户名
            max_reels: 获取Reels数量，默认20

        Returns:
            (user_info, reels) 元组
        """
        user_info = self.fetch_user_info(username)
        reels = self.fetch_user_reels(username, max_reels)
        return user_info, reels
