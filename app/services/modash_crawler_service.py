#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project : kol-python
<AUTHOR> 小林同学
@Date    : 2025/3/25 下午6:16 
@Docs    : 通过查询条件从 Modash 聚合平台获取 kol 数据
'''

import requests
from loguru import logger
import time


class ModashWebCrawler:
    def __init__(self, cookie: str, request_body: dict, platform: str):
        self.headers = {"cookie": cookie}
        self.request_body = request_body
        platform_urls = {
            "instagram": "https://marketer.modash.io/api/discovery/search/instagram",
            "tiktok": "https://marketer.modash.io/api/discovery/search/tiktok",
            "youtube": "https://marketer.modash.io/api/discovery/search/youtube"
        }
        self.url = platform_urls.get(platform)

    def batch_search_modash_kol_info(self):
        """批量搜索modash网站满足筛选条件的kol信息"""
        response_kol_info_list = []
        page = 0
        failed_pages = []  # 记录失败的页面
        max_retries = 3    # 最大重试次数

        # 获取第一页数据
        search_response = self.search_modash_page_data(page)
        if not search_response:
            return None

        response_json = search_response.json()
        if "lookalikes" not in response_json:
            logger.warning(f"API响应缺少'lookalikes'键; 请检查 cookie 是否过期;")
            return []

        response_kol_info_list.extend(response_json["lookalikes"])
        total = response_json["total"]
        pages = response_json["pages"]
        
        logger.info(f"共查询到 {total} 条数据，分为 {pages} 页")
        if total == 0:
            return []

        # 获取后续页面数据
        for page in range(1, pages):
            retry_count = 0
            success = False
            
            # 添加重试逻辑
            while retry_count < max_retries and not success:
                self.request_body["page"] = page
                search_response = self.search_modash_page_data(page)
                
                if search_response:
                    response_json = search_response.json()
                    if "lookalikes" in response_json:
                        response_kol_info_list.extend(response_json["lookalikes"])
                        success = True
                        logger.info(f"成功获取第 {page} 页数据，已获取 {len(response_kol_info_list)}/{total} 条")
                    else:
                        # Cookie 失效，终止爬取过程
                        logger.error(f"第 {page} 页响应缺少'lookalikes'键，疑似cookie已过期，终止爬取")
                        logger.info(f"已成功获取 {len(response_kol_info_list)}/{total} 条数据，提前结束爬取")
                        return []
                else:
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.warning(f"第 {page} 页请求失败，正在进行第 {retry_count + 1} 次重试...")
                        time.sleep(2)  # 重试前短暂等待
            
            # 记录失败的页面但继续获取后续页面
            if not success:
                failed_pages.append(page)
                logger.error(f"第 {page} 页在重试 {max_retries} 次后仍然失败，继续获取后续页面")

        # 记录失败页面汇总信息
        if failed_pages:
            logger.warning(f"爬取过程中有 {len(failed_pages)} 页失败: {failed_pages}")
            
        logger.info(f"共成功获取 {len(response_kol_info_list)}/{total} 条数据")
        return response_kol_info_list

    def search_modash_page_data(self, page: int) -> requests.Response:
        """搜索单页数据"""
        logger.info(f"查询 Modash 第 {page} 页")
        try:
            r = requests.post(url=self.url, headers=self.headers, json=self.request_body)
            if r.status_code != 200:
                logger.error(f"请求失败，状态码: {r.status_code}")
                return None

            response_json = r.json()
            if response_json.get("error"):
                logger.error(f"API错误: {response_json.get('message')}")
                return None

            return r
        except Exception as e:
            logger.error(f"搜索Modash第{page}页失败: {e}")
            return None

    def modash_unlock_kol(self, slug: str, platform: str) -> str:
        """获取KOL的Email信息"""
        try:
            url = f"https://marketer.modash.io/api/influencer/{slug}/emails/unlock"
            referer = f"https://marketer.modash.io/discovery/{platform.lower()}?influencer={slug}&channel={platform.upper()}"
            
            headers = {**self.headers, "Referer": referer}
            r = requests.post(url=url, headers=headers)
            
            if r.status_code != 200:
                return ""

            response_json = r.json()
            creator = response_json.get("creator", {})
            profiles = creator.get("connectedProfiles", [])
            
            for profile in profiles:
                contacts = profile.get("profileData", {}).get("profile", {}).get("contacts", [])
                if contacts:
                    return contacts[0]["value"]
            
            return ""
        except Exception as e:
            logger.error(f"获取KOL {slug} Email失败: {e}")
            return ""


if __name__ == "__main__":
    cookie = """__stripe_mid=72f67177-ef7c-436f-b485-07187eba079aef1911; g_state={"i_l":0}; intercom-id-hgbst103=6f14dc10-0d9b-45da-9f1b-a689d608c58b; intercom-device-id-hgbst103=eb265177-8d35-49b1-b2d8-839dc998998d; _gcl_gs=2.1.k1$i1735361230$u133036050; cwr_u=4ee56400-72af-41b5-9a43-e3f289c40717; _gac_UA-117910399-2=1.1740727324.Cj0KCQiAuou6BhDhARIsAIfgrn5HybyEA1I6y3hndk9WgLKqYlcWDFo-XyHiVpNLrADZT2T-BhLkpHcaAhcDEALw_wcB; _gcl_aw=GCL.1740727324.Cj0KCQiAuou6BhDhARIsAIfgrn5HybyEA1I6y3hndk9WgLKqYlcWDFo-XyHiVpNLrADZT2T-BhLkpHcaAhcDEALw_wcB; _fbp=fb.1.1740727324125.850944518430102554; _gid=GA1.2.2121804431.1743043268; rl_anonymous_id=RS_ENC_v3_ImI5ZmQzZGFkLWRmY2ItNDVhYi1iOGY0LWM2MDFjYmQxNTkwOSI%3D; rl_page_init_referrer=RS_ENC_v3_Imh0dHBzOi8vd3d3Lmdvb2dsZS5jb20vIg%3D%3D; rl_page_init_referring_domain=RS_ENC_v3_Ind3dy5nb29nbGUuY29tIg%3D%3D; _clck=1ku7q6x%7C2%7Cfuk%7C0%7C1790; __hstc=189910132.9189dadf6b34cdff67fc8e9407bf638d.1743043560545.1743043560545.1743043560545.1; hubspotutk=9189dadf6b34cdff67fc8e9407bf638d; __hssrc=1; rl_session=RS_ENC_v3_eyJpZCI6MTc0MzA0MzI2ODgxMywiZXhwaXJlc0F0IjoxNzQzMDQ1MzY3Njk1LCJ0aW1lb3V0IjoxODAwMDAwLCJhdXRvVHJhY2siOnRydWUsInNlc3Npb25TdGFydCI6ZmFsc2V9; _clsk=9ckpaa%7C1743043569227%7C3%7C1%7Cx.clarity.ms%2Fcollect; __stripe_sid=cb5be5b7-dbf9-4f70-9663-25cd8e59894c4572c2; modash.xsid2=s%3AD59KhFszoRYTGuTK46-vE3fpjXBCxFPr.hPgtWX8Lzb%2BH8SH5BwwqYrl0Qk%2FSvpMTP362sIXYWks; ajs_user_id=67a9a282af9ac25d7c06ebdc; ajs_group_id=674d1f64309da8a5f2e84c73; ajs_anonymous_id=394ecadc-29dc-4b10-9d8f-4b639268d83d; analytics_session_id=1743044351415; _ga=GA1.1.1554182586.1732520641; amp_session_id=1743044351415; intercom-session-hgbst103=OUNTdGhLcEZaejJ3dnIyNmFRWnNRZHpjVGttNE5USyttTDN0aCtiTWxXdm1KS0JSa0JQaHJlMTZJb2tWTEM0V25sZHloSEJvTHZqQ3FJdzRKenR4Y0pSN3lxWlB1S01XS1E2N0gyWnZ5OHc9LS02OE1LTHJVZVBSZDlGOFFCUGZFY3d3PT0=--05685172da1182b4c0238152106e09a0c447fab9; fs_lua=1.1743044991553; fs_uid=#RVTE7#d2040d27-3b72-4f4a-bc6c-0517e9d96510:998e19f9-273c-4527-88e5-762f181f902f:1743044385845::1#33860ee6#/1774580403; analytics_session_id.last_access=1743045561171; _ga_6FKKZM7XTB=GS1.1.1743043268.9.1.1743045561.60.0.39888325; cwr_s=eyJzZXNzaW9uSWQiOiI5ZjNlZmQ2My04YWJkLTRlNTQtYTg3Ni03YzU4OTFiYzVjZTYiLCJyZWNvcmQiOnRydWUsImV2ZW50Q291bnQiOjY1NiwicGFnZSI6eyJwYWdlSWQiOiIvZGlzY292ZXJ5L3Rpa3RvayIsInBhcmVudFBhZ2VJZCI6Ii9kaXNjb3ZlcnkveW91dHViZSIsImludGVyYWN0aW9uIjo0LCJyZWZlcnJlciI6Imh0dHBzOi8vbWFya2V0ZXIubW9kYXNoLmlvL2xvZ2luL21hcmtldGVyP3JlZGlyZWN0PS9kaXNjb3ZlcnkvaW5zdGFncmFtP19nYT0yLjEyMzM1MDk4NC4yMTIxODA0NDMxLjE3NDMwNDMyNjgtMTU1NDE4MjU4Ni4xNzMyNTIwNjQxJTI2X2dhYz0xLjg2OTg4OTA2LjE3NDA3MjczMjQuQ2owS0NRaUF1b3U2QmhEaEFSSXNBSWZncm41SHlieUVBMUk2eTNobmRrOVdnTEtxWWxjV0RGby1YeUhpVnBOTHJBRFpUMlQtQmhMa3BIY2FBaGNERUFMd193Y0IiLCJyZWZlcnJlckRvbWFpbiI6Im1hcmtldGVyLm1vZGFzaC5pbyIsInN0YXJ0IjoxNzQzMDQ1NTYwMzExfX0=; AWSALB=XExyHtVA/GSTXjKu7+iz8fnIv8yEqJeDTQNWf1rw4gUgRqK3SWoGe+wkY8Gwi6qqw9sFNtO4p/Epmz7wHgV1PlwsnNDm+4/bF1MqpFDi/Emjfbwiz38Y/pR/jLgzDu1SypmBQMVj8YyRJWYg1IYD9GkYLIW+26xbRCoCgLAfKGLOdwv5B3pjrXeUoGZmCkIKmfjT6h+dELmsLymBJVfZN7ZELEuzSHCHagLM7jjVR8moXO6liXBTemj4OaLLs9o=; AWSALBCORS=XExyHtVA/GSTXjKu7+iz8fnIv8yEqJeDTQNWf1rw4gUgRqK3SWoGe+wkY8Gwi6qqw9sFNtO4p/Epmz7wHgV1PlwsnNDm+4/bF1MqpFDi/Emjfbwiz38Y/pR/jLgzDu1SypmBQMVj8YyRJWYg1IYD9GkYLIW+26xbRCoCgLAfKGLOdwv5B3pjrXeUoGZmCkIKmfjT6h+dELmsLymBJVfZN7ZELEuzSHCHagLM7jjVR8moXO6liXBTemj4OaLLs9o="""
    # 定义搜索请求体，这里以 TikTok 平台上粉丝数在 10K-100K 之间的美国 KOL 为例
    request_body = {
        "page": 0,
        "filters": {
            "influencer": {
            "location": [
                80500,
                16239,
                52411,
                1428125,
                51684,
                50046,
                54224,
                62273,
                556706,
                49715,
                51701,
                62149,
                148838,
                307763,
                1473946,
                299133,
                2323309,
                2202162,
                51477,
                295480,
                218657,
                2978650
            ],
            "relevance": {
                "usernames": [],
                "hashtags": []
            },
            "textTags": [],
            "keywords": "",
            "hasContactDetails": [
                {
                "contactType": "email",
                "filterAction": "must"
                }
            ],
            "lastposted": 30,
            "followers": {
                "min": 1000,
                "max": 500000
            },
            "engagementRate": 0.03,
            "views": {
                "min": 5000,
                "max": 150000
            },
            "bio": "yoga"
            },
            "audience": {
            "location": [
                {
                "id": 16239,
                "weight": 0.15
                },
                {
                "id": 52411,
                "weight": 0.15
                },
                {
                "id": 1428125,
                "weight": 0.15
                },
                {
                "id": 51684,
                "weight": 0.15
                },
                {
                "id": 50046,
                "weight": 0.15
                },
                {
                "id": 54224,
                "weight": 0.15
                },
                {
                "id": 148838,
                "weight": 0.15
                },
                {
                "id": 62149,
                "weight": 0.15
                },
                {
                "id": 51701,
                "weight": 0.15
                },
                {
                "id": 307763,
                "weight": 0.15
                },
                {
                "id": 295480,
                "weight": 0.15
                },
                {
                "id": 51477,
                "weight": 0.15
                },
                {
                "id": 1473946,
                "weight": 0.15
                },
                {
                "id": 299133,
                "weight": 0.15
                },
                {
                "id": 2323309,
                "weight": 0.15
                },
                {
                "id": 218657,
                "weight": 0.15
                },
                {
                "id": 556706,
                "weight": 0.15
                },
                {
                "id": 2978650,
                "weight": 0.15
                },
                {
                "id": 49715,
                "weight": 0.15
                },
                {
                "id": 80500,
                "weight": 0.15
                },
                {
                "id": 2202162,
                "weight": 0.15
                },
                {
                "id": 62273,
                "weight": 0.15
                }
            ],
            "age": [
                {
                "id": "25-34",
                "weight": 0.3
                },
                {
                "id": "35-44",
                "weight": 0.3
                },
                {
                "id": "45-64",
                "weight": 0.3
                }
            ]
            },
            "actions": [],
            "options": {
            "showSavedProfiles": True
            }
        },
        "sort": {}
        }

    crawler = ModashWebCrawler(cookie=cookie, request_body=request_body, platform="tiktok")
    results = crawler.batch_search_modash_kol_info()
    print(len(results))
    import json
    print(json.dumps(results[0], indent=2))
