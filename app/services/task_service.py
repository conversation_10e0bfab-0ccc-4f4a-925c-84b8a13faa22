"""
任务服务模块
负责处理任务队列状态查询等任务相关功能
"""
from typing import Any, Dict, List, Optional
import json
import redis
from celery.result import AsyncResult
from loguru import logger
import time

from app.worker.celery_app import celery_app
from app.core.config import settings


def get_queue_status(limit: int = 20, queue_name: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
    """
    获取队列中的任务状态信息
    
    Args:
        limit: 每种状态返回的最大任务数量
        queue_name: 队列名称，不提供则查询所有队列
        
    Returns:
        包含四种状态任务的字典
    """
    # 初始化响应
    response = {
        "active_tasks": [],
        "reserved_tasks": [],
        "completed_tasks": [],
        "failed_tasks": []
    }
    
    # 获取正在执行和等待的任务
    inspector = celery_app.control.inspect()
    
    # 尝试多次获取活跃任务，提高可靠性
    active_tasks = {}
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # 增加超时处理
            active_tasks = inspector.active() or {}
            if active_tasks:
                break
            retry_count += 1
            if retry_count < max_retries:
                time.sleep(0.5)  # 短暂延迟后重试
        except Exception as e:
            logger.warning(f"获取活跃任务第{retry_count+1}次失败: {e}")
            retry_count += 1
            if retry_count < max_retries:
                time.sleep(0.5)
    
    # 处理活跃任务
    for worker, tasks in active_tasks.items():
        for task in tasks[:limit]:
            # 提取任务ID
            task_id = task.get("id")
            if not task_id:
                continue
                
            # 创建基础任务信息
            task_info = {
                "task_id": task_id,
                "filter_name": None
            }
            
            # 从任务参数中提取filter_name
            filter_name = extract_filter_name_from_task(task)
            if filter_name:
                task_info["filter_name"] = filter_name
            
            # 只有指定队列的任务或未指定队列时
            if not queue_name or task.get("delivery_info", {}).get("routing_key") == queue_name:
                response["active_tasks"].append(task_info)
                
            # 限制任务数量
            if len(response["active_tasks"]) >= limit:
                break
    
    # 获取等待任务
    reserved_tasks = inspector.reserved() or {}
    for worker, tasks in reserved_tasks.items():
        for task in tasks[:limit]:
            # 提取任务ID
            task_id = task.get("id")
            if not task_id:
                continue
                
            # 创建基础任务信息
            task_info = {
                "task_id": task_id,
                "filter_name": None
            }
            
            # 从任务参数中提取filter_name
            filter_name = extract_filter_name_from_task(task)
            if filter_name:
                task_info["filter_name"] = filter_name
            
            # 只有指定队列的任务或未指定队列时
            if not queue_name or task.get("delivery_info", {}).get("routing_key") == queue_name:
                response["reserved_tasks"].append(task_info)
                
            # 限制任务数量
            if len(response["reserved_tasks"]) >= limit:
                break
    
    # 获取已完成和失败的任务
    # 注意：这需要使用Redis作为结果后端，并假设已经配置了结果后端
    try:
        # 连接到Redis
        redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=int(settings.REDIS_PORT),
            db=int(settings.REDIS_DB),
            password=settings.REDIS_PASSWORD or None,
            decode_responses=True
        )
        
        # 查找Celery结果键（通常以'celery-task-meta-'为前缀）
        task_keys = redis_client.keys('celery-task-meta-*')
        
        # 限制返回数量
        task_keys = task_keys[:limit * 2]  # 获取足够的键以便过滤后仍有足够的结果
        
        completed_count = 0
        failed_count = 0
        
        # 记录我们已经添加到活跃任务中的任务ID，避免重复
        active_task_ids = {task["task_id"] for task in response["active_tasks"]}
        
        for key in task_keys:
            if completed_count >= limit and failed_count >= limit:
                break
                
            task_id = key.replace('celery-task-meta-', '')
            
            # 如果任务ID已经在活跃任务中，跳过处理
            if task_id in active_task_ids:
                continue
                
            # 获取任务结果数据
            task_data_str = redis_client.get(key)
            if not task_data_str:
                continue
                
            try:
                task_data = json.loads(task_data_str)
                
                # 创建任务信息
                task_info = {
                    "task_id": task_id,
                    "filter_name": None
                }
                
                # 尝试提取filter_name - 先检查原始任务信息
                original_task = AsyncResult(task_id, app=celery_app)
                if original_task.state == "SUCCESS" or original_task.state == "FAILURE":
                    # 尝试直接从任务的返回值中获取filter_name
                    if original_task.state == "SUCCESS" and original_task.result:
                        result = original_task.result
                        if isinstance(result, dict) and "filter_name" in result:
                            task_info["filter_name"] = result["filter_name"]
                    
                    # 尝试从任务记录获取 filter_name
                    if not task_info["filter_name"]:
                        task_info["filter_name"] = get_task_filter_name_from_async_result(original_task)
                        
                    # 根据状态区分完成和失败的任务
                    if original_task.state == "SUCCESS":
                        if completed_count < limit:
                            response["completed_tasks"].append(task_info)
                            completed_count += 1
                    else:  # FAILURE
                        if failed_count < limit:
                            response["failed_tasks"].append(task_info)
                            failed_count += 1
                
            except (json.JSONDecodeError, Exception) as e:
                logger.error(f"解析任务结果失败: {str(e)}")
                    
    except Exception as e:
        logger.error(f"获取历史任务失败: {str(e)}")
        # 出错时不返回错误，仅记录日志并返回空列表
    
    return response


def extract_filter_name_from_task(task: Dict[str, Any]) -> Optional[str]:
    """
    从Celery任务信息中提取filter_name
    
    Args:
        task: Celery任务信息字典
        
    Returns:
        提取到的filter_name或None
    """
    try:
        # 方法1: 尝试从kwargs中获取query_params.filter_name
        kwargs = task.get("kwargs", {})
        if kwargs:
            # 如果kwargs是字符串(JSON格式)，尝试解析
            if isinstance(kwargs, str):
                try:
                    kwargs_dict = json.loads(kwargs)
                    query_params = kwargs_dict.get("query_params", {})
                    if query_params and isinstance(query_params, dict):
                        filter_name = query_params.get("filter_name")
                        if filter_name:
                            return filter_name
                except Exception:
                    pass
            # 如果kwargs已经是字典
            elif isinstance(kwargs, dict):
                query_params = kwargs.get("query_params", {})
                if query_params and isinstance(query_params, dict):
                    filter_name = query_params.get("filter_name")
                    if filter_name:
                        return filter_name
        
        # 方法2: 尝试从args中获取(如果有查询参数作为位置参数传递)
        args = task.get("args", [])
        if args and isinstance(args, list):
            for arg in args:
                if isinstance(arg, dict) and "filter_name" in arg:
                    return arg["filter_name"]
                    
        # 方法3: 尝试从返回值中获取
        result = task.get("result")
        if result and isinstance(result, dict):
            return result.get("filter_name")
            
        # 方法4: 根据任务名称尝试从特定字段获取
        task_name = task.get("name")
        if task_name == "kol.collect_kol_data":
            # 为KOL任务尝试查看任务详情
            task_id = task.get("id")
            if task_id:
                try:
                    # 获取任务详情
                    task_result = AsyncResult(task_id, app=celery_app)
                    return get_task_filter_name_from_async_result(task_result)
                except Exception:
                    pass
    except Exception as e:
        logger.debug(f"提取filter_name失败: {str(e)}")
        
    return None


def get_task_filter_name_from_async_result(task_result: AsyncResult) -> Optional[str]:
    """
    从AsyncResult对象中获取filter_name
    
    Args:
        task_result: AsyncResult对象
        
    Returns:
        提取到的filter_name或None
    """
    try:
        # 尝试从info中获取
        if hasattr(task_result, 'info') and task_result.info:
            info = task_result.info
            if isinstance(info, dict):
                if "filter_name" in info:
                    return info["filter_name"]
                elif "query_params" in info and isinstance(info["query_params"], dict):
                    return info["query_params"].get("filter_name")
        
        # 尝试从kwargs中获取
        if hasattr(task_result, 'kwargs') and task_result.kwargs:
            kwargs = task_result.kwargs
            if isinstance(kwargs, dict):
                query_params = kwargs.get("query_params", {})
                if query_params and isinstance(query_params, dict):
                    return query_params.get("filter_name")
    except Exception as e:
        logger.debug(f"从AsyncResult获取filter_name失败: {str(e)}")
        
    return None


def check_task_health(task_id: str, timeout_seconds: int = 120) -> Dict[str, Any]:
    """
    检查任务的健康状态，判断是否为僵尸任务
    
    Args:
        task_id: 任务ID
        timeout_seconds: 超时时间（秒），超过这个时间没有心跳则视为僵尸任务
        
    Returns:
        任务健康状态信息
    """
    try:
        # 获取任务状态
        task_result = AsyncResult(task_id, app=celery_app)
        
        health_info = {
            "task_id": task_id,
            "status": task_result.status,
            "is_zombie": False,
            "last_heartbeat": None,
            "seconds_since_update": None,
            "progress": None,
        }
        
        # 如果任务已完成或失败，不是僵尸任务
        if task_result.ready():
            return health_info
        
        # 检查任务是否有状态更新
        if task_result.status == "PROGRESS" and task_result.info:
            # 获取最后心跳时间
            last_heartbeat = task_result.info.get("last_heartbeat")
            if last_heartbeat:
                current_time = time.time()
                seconds_since_update = current_time - last_heartbeat
                health_info["last_heartbeat"] = last_heartbeat
                health_info["seconds_since_update"] = seconds_since_update
                health_info["progress"] = task_result.info.get("current")
                
                # 判断是否为僵尸任务
                if seconds_since_update > timeout_seconds:
                    health_info["is_zombie"] = True
        
        # 如果任务状态为STARTED但没有进度更新，也可能是僵尸任务
        elif task_result.status == "STARTED":
            # 检查任务创建时间
            if hasattr(task_result, "date_done") and task_result.date_done:
                current_time = time.time()
                task_time = task_result.date_done.timestamp() if hasattr(task_result.date_done, "timestamp") else 0
                seconds_since_start = current_time - task_time
                health_info["seconds_since_update"] = seconds_since_start
                
                # 判断是否为僵尸任务
                if seconds_since_start > timeout_seconds:
                    health_info["is_zombie"] = True
        
        return health_info
        
    except Exception as e:
        logger.error(f"检查任务健康状态失败: {str(e)}")
        return {
            "task_id": task_id,
            "status": "UNKNOWN",
            "is_zombie": False,
            "error": str(e)
        }


def revoke_zombie_tasks(timeout_seconds: int = 300) -> Dict[str, Any]:
    """
    检测并撤销所有僵尸任务
    
    Args:
        timeout_seconds: 超时时间（秒），超过这个时间没有心跳则视为僵尸任务
        
    Returns:
        撤销的任务状态信息
    """
    results = {
        "revoked_count": 0,
        "revoked_tasks": [],
        "errors": []
    }
    
    try:
        # 获取所有活跃任务
        inspector = celery_app.control.inspect()
        active_tasks = inspector.active() or {}
        
        # 遍历所有活跃任务
        for worker, tasks in active_tasks.items():
            for task in tasks:
                task_id = task.get("id")
                if not task_id:
                    continue
                
                # 检查任务健康状态
                health_info = check_task_health(task_id, timeout_seconds)
                
                # 如果是僵尸任务，撤销它
                if health_info.get("is_zombie"):
                    try:
                        # 撤销任务
                        celery_app.control.revoke(task_id, terminate=True, signal='SIGKILL')
                        
                        # 记录被撤销的任务
                        results["revoked_count"] += 1
                        results["revoked_tasks"].append({
                            "task_id": task_id,
                            "worker": worker,
                            "seconds_since_update": health_info.get("seconds_since_update")
                        })
                        
                        logger.warning(f"撤销僵尸任务: {task_id}, 无更新时间: {health_info.get('seconds_since_update')}秒")
                    except Exception as e:
                        results["errors"].append({
                            "task_id": task_id,
                            "error": str(e)
                        })
        
        return results
        
    except Exception as e:
        logger.error(f"撤销僵尸任务失败: {str(e)}")
        results["errors"].append({"error": str(e)})
        return results 