from datetime import datetime

from sqlalchemy import ForeignKey, DateTime, String
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func

from app.db.base import Base


class FilterKolAssociation(Base):
    """筛选条件和KOL信息的多对多关联表"""
    __tablename__ = "filter_kol_association"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)  # 主键ID
    filter_id: Mapped[int] = mapped_column(ForeignKey("filter_data.id", ondelete="CASCADE"), index=True)  # 关联的筛选条件ID
    kol_id: Mapped[str] = mapped_column(ForeignKey("kol_info.kol_id", ondelete="CASCADE"), index=True)  # 关联的KOL ID
    project_code: Mapped[str | None] = mapped_column(String)  # 项目代码，便于查询

    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())  # 创建时间
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())  # 更新时间 
