from datetime import datetime

from sqlalchemy import J<PERSON><PERSON>, DateTime, Float, ForeignKey, String, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import JSONB

from app.db.base import Base


class KOLInfo(Base):
    """KOL信息表, 包含基本信息和指标数据"""
    __tablename__ = "kol_info"

    kol_id: Mapped[str] = mapped_column(String, primary_key=True, index=True)  # KOL唯一标识
    kol_name: Mapped[str] = mapped_column(String, index=True, nullable=False)  # KOL ID（原kol_id）
    username: Mapped[str | None] = mapped_column(String, index=True)  # 用户名（原name）
    email: Mapped[str | None] = mapped_column(String, index=True)  # 电子邮箱
    bio: Mapped[str | None] = mapped_column(String)  # 个人简介
    account_link: Mapped[str | None] = mapped_column(String)  # 社交账号链接
    followers_k: Mapped[float | None] = mapped_column(Float, index=True)  # 粉丝数(K)
    likes_k: Mapped[float | None] = mapped_column(Float, index=True)  # 点赞数(K)
    platform: Mapped[str | None] = mapped_column(String, index=True)  # 社交平台
    source: Mapped[str | None] = mapped_column(String, index=True)  # 数据来源
    slug: Mapped[str | None] = mapped_column(String, index=True)  # 用于获取unlock邮箱的ID
    creator_id: Mapped[str | None] = mapped_column(String, index=True)  # 创建者ID，用于获取 slug 的ID

    # 指标数据
    mean_views_k: Mapped[float | None] = mapped_column(Float, index=True)  # 近15天播放平均数(K) 计算得出
    median_views_k: Mapped[float | None] = mapped_column(Float, index=True)  # 近15天视频播放中位观看数(K) 计算得出
    engagement_rate: Mapped[float | None] = mapped_column(Float, index=True)  # 互动率
    average_views_k: Mapped[float | None] = mapped_column(Float, index=True)  # 平均观看数(K)
    average_likes_k: Mapped[float | None] = mapped_column(Float, index=True)  # 平均点赞数(K)
    average_comments_k: Mapped[float | None] = mapped_column(Float, index=True)  # 平均评论数(K)
    most_used_hashtags: Mapped[list[str] | None] = mapped_column(JSONB)  # 最常用的标签
    level: Mapped[str | None] = mapped_column(String, index=True)  # KOL等级
    keywords_ai: Mapped[list[str] | None] = mapped_column(JSONB)  # AI生成的关键词

    # 外键关系 - 多对多关系  
    # 注意：多对多关系依赖数据库外键的 ondelete="CASCADE" 来处理关联表的删除
    # SQLAlchemy的 cascade 配置对 secondary 表不起作用
    filter_datas = relationship("FilterData", secondary="filter_kol_association", back_populates="kol_infos")
    tags = relationship("Tag", secondary="kol_tag_association", back_populates="kol_infos", overlaps="tag,kol_info")  # 关联的标签
    
    # 视频关系
    videos = relationship("VideoInfo", back_populates="kol", cascade="all, delete-orphan")

    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), index=True)  # 创建时间
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now(), index=True)  # 更新时间
    
    # 为JSON字段添加GIN索引，提高复杂查询性能
    __table_args__ = (
        Index('ix_kol_info_most_used_hashtags', most_used_hashtags, postgresql_using='gin'),
        Index('ix_kol_info_keywords_ai', keywords_ai, postgresql_using='gin'),
    )
