from datetime import datetime

from sqlalchemy import Integer, String, DateTime
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func

from app.db.base import Base


class CandidateData(Base):
    """KOL候选人数据, 记录潜在合作KOL的沟通跟进状态"""
    __tablename__ = "candidate_data"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)  # 主键ID
    kol_id: Mapped[str] = mapped_column(String, index=True, nullable=False)  # KOL ID（非外键）
    kol_name: Mapped[str | None] = mapped_column(String, index=True)  # KOL名称
    first_contact_date: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))  # 首次联系日期
    last_contact_date: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))  # 最近联系日期
    follow_up: Mapped[str | None] = mapped_column(String)  # 跟进状态
    label: Mapped[str | None] = mapped_column(String, index=True)  # 标签
    sublabel: Mapped[str | None] = mapped_column(String)  # 子标签
    thread_id: Mapped[str | None] = mapped_column(String)  # 对话线程ID
    project: Mapped[str | None] = mapped_column(String, index=True)  # 项目
    reply_email: Mapped[str | None] = mapped_column(String)  # 回复邮箱

    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())  # 创建时间
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())  # 更新时间
