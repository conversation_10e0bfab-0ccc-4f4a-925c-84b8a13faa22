from datetime import datetime

from sqlalchemy import String, DateTime, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.db.base import Base


class Tag(Base):
    """标签表，存储所有标签的基础信息"""
    __tablename__ = "tag"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)  # 标签ID
    name: Mapped[str] = mapped_column(String, nullable=False, index=True, unique=True)  # 标签名称，唯一索引
    description: Mapped[str] = mapped_column(Text, nullable=True, server_default="")  # 标签描述，可为空

    # 关系
    kol_infos = relationship("KOLInfo", secondary="kol_tag_association", back_populates="tags", overlaps="kol_info,tag")  # 关联的KOL信息
    projects = relationship("ProjectTagAssociation", back_populates="tag")  # 关联的项目

    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())  # 创建时间
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())  # 更新时间