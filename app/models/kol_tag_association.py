from datetime import datetime
from sqlalchemy import DateTime, ForeignKey, String
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.db.base import Base


class KolTagAssociation(Base):
    """KOL标签关联表，记录KOL与标签之间的多对多关系"""
    __tablename__ = "kol_tag_association"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)  # 主键ID
    kol_id: Mapped[str] = mapped_column(ForeignKey("kol_info.kol_id", ondelete="CASCADE"), index=True)  # KOL ID，外键关联到KOL信息表
    tag_id: Mapped[int] = mapped_column(ForeignKey("tag.id", ondelete="CASCADE"), index=True)  # 标签ID，外键关联到标签表
    project_code: Mapped[str | None] = mapped_column(String, index=True)  # 项目编码，便于查询
    
    # 关系定义
    tag = relationship("Tag", lazy="select")  # 关联的标签
    kol_info = relationship("KOLInfo", lazy="select")  # 关联的KOL信息
    
    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())  # 创建时间
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())  # 更新时间