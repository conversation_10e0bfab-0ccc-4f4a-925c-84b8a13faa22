from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON>, Integer, Foreign<PERSON>ey, String
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.db.base import Base


class VideoInfo(Base):
    """博主视频信息表, 与KOL是多对一关系"""
    __tablename__ = "video_info"

    video_id: Mapped[str] = mapped_column(String, primary_key=True, index=True)  # 视频唯一标识
    kol_id: Mapped[str] = mapped_column(ForeignKey("kol_info.kol_id", ondelete="CASCADE"), index=True)  # 关联的KOL ID
    
    # 视频基本信息
    play_count: Mapped[int | None] = mapped_column(Integer)  # 播放数
    is_pinned: Mapped[bool] = mapped_column(Boolean, default=False)  # 是否置顶
    share_url: Mapped[str | None] = mapped_column(String)  # 分享链接
    desc: Mapped[str | None] = mapped_column(String)  # 视频描述
    desc_language: Mapped[str | None] = mapped_column(String)  # 描述语言
    video_url: Mapped[str | None] = mapped_column(String)  # 视频链接
    music_url: Mapped[str | None] = mapped_column(String)  # 音乐链接
    
    # 互动数据
    likes_count: Mapped[int | None] = mapped_column(Integer)  # 点赞数
    comments_count: Mapped[int | None] = mapped_column(Integer)  # 评论数
    shares_count: Mapped[int | None] = mapped_column(Integer)  # 分享数
    collect_count: Mapped[int | None] = mapped_column(Integer)  # 收藏数
    
    # 平台信息
    platform: Mapped[str | None] = mapped_column(String)  # 视频发布平台
    
    # 标签和内容分析
    hashtags: Mapped[list[str] | None] = mapped_column(JSON)  # 视频标签
    
    # 关系
    kol = relationship("KOLInfo", back_populates="videos")
    
    # 时间信息
    create_time: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))  # 视频创建时间
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())  # 数据创建时间
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())  # 数据更新时间 
