from datetime import datetime

from sqlalchemy import Integer, String, DateTime, Float
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func

from app.db.base import Base


class CollaborationPerformance(Base):
    """KOL合作表现数据, 记录合作KOL的内容表现数据"""
    __tablename__ = "collaboration_performance"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)  # 主键ID
    kol_id: Mapped[str] = mapped_column(String, index=True, nullable=False)  # KOL ID（非外键）
    project: Mapped[str | None] = mapped_column(String, index=True)  # 项目
    paypal_accounts: Mapped[str | None] = mapped_column(String)  # Paypal账户
    collab_status: Mapped[str | None] = mapped_column(String)  # 合作状态（1st/2nd/3rd）
    follow_up: Mapped[str | None] = mapped_column(String)  # 跟进人（Niall/Frances）
    post_link: Mapped[str | None] = mapped_column(String)  # 帖子链接
    payment: Mapped[str | None] = mapped_column(String)  # 支付信息
    payout_date: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))  # 支付日期
    fund_source: Mapped[str | None] = mapped_column(String)  # 资金来源（Kevin/Niall备用金）
    kol_priority: Mapped[str | None] = mapped_column(String)  # KOL优先级
    post_date: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))  # 发布日期
    
    # 内容表现数据
    views_total: Mapped[int | None] = mapped_column(Integer)  # 总观看数
    likes_total: Mapped[int | None] = mapped_column(Integer)  # 总点赞数
    comments_total: Mapped[int | None] = mapped_column(Integer)  # 总评论数
    shares_total: Mapped[int | None] = mapped_column(Integer)  # 总分享数
    
    # 不同时间段的表现数据
    views_day1: Mapped[int | None] = mapped_column(Integer)  # 1天观看数
    likes_day1: Mapped[int | None] = mapped_column(Integer)  # 1天点赞数
    comments_day1: Mapped[int | None] = mapped_column(Integer)  # 1天评论数
    shares_day1: Mapped[int | None] = mapped_column(Integer)  # 1天分享数
    
    views_day3: Mapped[int | None] = mapped_column(Integer)  # 3天观看数
    likes_day3: Mapped[int | None] = mapped_column(Integer)  # 3天点赞数
    comments_day3: Mapped[int | None] = mapped_column(Integer)  # 3天评论数
    shares_day3: Mapped[int | None] = mapped_column(Integer)  # 3天分享数
    
    views_day7: Mapped[int | None] = mapped_column(Integer)  # 7天观看数
    likes_day7: Mapped[int | None] = mapped_column(Integer)  # 7天点赞数
    comments_day7: Mapped[int | None] = mapped_column(Integer)  # 7天评论数
    shares_day7: Mapped[int | None] = mapped_column(Integer)  # 7天分享数
    
    # 衍生指标
    engagement_rate: Mapped[float | None] = mapped_column(Float)  # 互动率
    cpm: Mapped[float | None] = mapped_column(Float)  # CPM(千人成本)

    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())  # 创建时间
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())  # 更新时间
