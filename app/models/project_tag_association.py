from datetime import datetime

from sqlalchemy import String, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.db.base import Base


class ProjectTagAssociation(Base):
    """项目标签关联表，记录项目与标签之间的多对多关系"""
    __tablename__ = "project_tag_association"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)  # 主键ID
    project_code: Mapped[str] = mapped_column(String, nullable=False, index=True)  # 项目编码
    tag_id: Mapped[int] = mapped_column(ForeignKey("tag.id", ondelete="CASCADE"), index=True)  # 标签ID，外键关联到标签表

    # 关系
    tag = relationship("Tag", back_populates="projects")  # 关联的标签

    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())  # 创建时间
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())  # 更新时间 