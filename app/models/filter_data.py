from datetime import datetime

from sqlalchemy import J<PERSON><PERSON>, DateTime, String
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.db.base import Base


class FilterData(Base):
    """筛选条件表, 存储KOL的标签、语言、性别、地理位置等筛选信息"""
    __tablename__ = "filter_data"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)  # 主键ID
    language: Mapped[str | None] = mapped_column(String)  # 使用语言
    gender: Mapped[str | None] = mapped_column(String)  # 性别
    location: Mapped[str | None] = mapped_column(String)  # 地理位置
    filter_body: Mapped[dict | None] = mapped_column(JSON)  # 筛选条件
    filter_name: Mapped[str | None] = mapped_column(String)   # 筛选条件名称
    project_code: Mapped[str | None] = mapped_column(String)  # 项目ID

    # 关系
    kol_infos = relationship("KOLInfo", secondary="filter_kol_association", back_populates="filter_datas")

    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())  # 创建时间
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())  # 更新时间 
