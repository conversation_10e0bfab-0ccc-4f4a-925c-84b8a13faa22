from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, String
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func

from app.db.base import Base


class SendData(Base):
    """发送数据表，记录消息发送状态和相关信息"""
    __tablename__ = "send_data"

    id: Mapped[str] = mapped_column(String, primary_key=True, index=True)  # 消息唯一标识
    kol_id: Mapped[str] = mapped_column(String, index=True, nullable=False)  # KOL ID（非外键）
    send_status: Mapped[str | None] = mapped_column(String)  # 发送状态
    platform: Mapped[str | None] = mapped_column(String)  # 发送平台
    send_date: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))  # 发送日期
    export_date: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))  # 导出日期
    
    # 新增字段
    app_code: Mapped[str | None] = mapped_column(String)  # 应用代码 / project_code
    template_id: Mapped[str | None] = mapped_column(String)  # 模板ID
    read_status: Mapped[bool] = mapped_column(Boolean, default=False)  # 阅读状态
    success: Mapped[bool] = mapped_column(Boolean, default=False)  # 发送成功状态
    from_email: Mapped[str | None] = mapped_column(String)      # 发送者邮箱
    to_email: Mapped[str | None] = mapped_column(String)        # 接收者邮箱

    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now())  # 创建时间
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())  # 更新时间 
