from datetime import datetime
from typing import List, Optional, Union, Dict, Any, Literal
from enum import Enum

from pydantic import BaseModel, Field


# 哈希标签模型
class HashTag(BaseModel):
    """哈希标签模型，支持字符串和复杂对象两种形式"""
    text: str
    type: Optional[str] = None
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        """验证并转换标签数据"""
        if isinstance(v, str):
            # 如果是字符串，直接创建标签对象
            return cls(text=v)
        elif isinstance(v, dict) and "text" in v:
            # 如果是字典且包含text字段，创建标签对象
            return cls(text=v["text"], type=v.get("type"))
        elif isinstance(v, cls):
            # 如果已经是HashTag对象，直接返回
            return v
        else:
            # 其他情况尝试转换为字符串
            return cls(text=str(v))


# 共享属性
class KOLInfoBase(BaseModel):
    """KOL信息的基础模型"""
    kol_name: str
    username: Optional[str] = None
    email: Optional[str] = None
    bio: Optional[str] = None
    account_link: Optional[str] = None
    followers_k: Optional[float] = None
    likes_k: Optional[float] = None
    platform: Optional[str] = None
    source: Optional[str] = None
    slug: Optional[str] = None
    creator_id: Optional[str] = None
    
    # 指标数据
    mean_views_k: Optional[float] = None
    median_views_k: Optional[float] = None
    engagement_rate: Optional[float] = None
    average_views_k: Optional[float] = None
    average_likes_k: Optional[float] = None
    average_comments_k: Optional[float] = None
    most_used_hashtags: Optional[List[Union[str, HashTag, Dict[str, Any]]]] = None
    level: Optional[str] = None
    keywords_ai: Optional[List[str]] = None


# 创建时的数据模型
class KOLInfoCreate(KOLInfoBase):
    """创建KOL信息时的数据模型"""
    kol_id: str


# 筛选条件详情模型
class FilterDetails(BaseModel):
    """筛选条件详情模型"""
    language: Optional[str] = None
    gender: Optional[str] = None
    location: Optional[str] = None
    filter_body: Optional[Dict[str, Any]] = Field(default_factory=dict)
    filter_name: str = Field(..., description="筛选条件名称，必填")
    project_code: str = Field(..., description="项目代码，必填")


# 扩展的创建KOL信息模型，支持嵌套结构
class KOLInfoCreateExtended(KOLInfoCreate):
    """扩展的KOL信息创建模型，支持嵌套结构"""
    tag_names: Optional[List[str]] = None  # 标签名称列表
    filter_details: Optional[FilterDetails] = None  # 筛选条件详情


# 更新时的数据模型
class KOLInfoUpdate(BaseModel):
    """更新KOL信息时的数据模型"""
    kol_name: Optional[str] = None
    username: Optional[str] = None
    email: Optional[str] = None
    bio: Optional[str] = None
    account_link: Optional[str] = None
    followers_k: Optional[float] = None
    likes_k: Optional[float] = None
    platform: Optional[str] = None
    source: Optional[str] = None
    slug: Optional[str] = None
    creator_id: Optional[str] = None
    
    # 指标数据
    mean_views_k: Optional[float] = None
    median_views_k: Optional[float] = None
    engagement_rate: Optional[float] = None
    average_views_k: Optional[float] = None
    average_likes_k: Optional[float] = None
    average_comments_k: Optional[float] = None
    most_used_hashtags: Optional[List[Union[str, HashTag, Dict[str, Any]]]] = None
    level: Optional[str] = None
    keywords_ai: Optional[List[str]] = None


# 返回时的数据模型
class KOLInfoResponse(KOLInfoBase):
    """KOL信息的响应模型"""
    kol_id: str
    created_at: datetime
    updated_at: datetime
    filter_names: Optional[List[str]] = None
    tag_names: Optional[List[str]] = None  # 标签名称列表
    project_codes: Optional[List[str]] = None  # 项目代码列表

    class Config:
        """配置类"""
        from_attributes = True
        populate_by_name = True  # 允许使用字段名和别名


# 分页响应模型
class KOLInfoPaginatedResponse(BaseModel):
    """带分页信息的KOL信息列表响应模型"""
    items: List[KOLInfoResponse]
    total: int
    page: int
    size: int
    pages: int

    class Config:
        """配置类"""
        from_attributes = True


# 操作符枚举
class SearchOperator(str, Enum):
    """搜索操作符枚举"""
    EQUAL = "eq"           # 等于
    NOT_EQUAL = "ne"       # 不等于
    CONTAINS = "contains"  # 包含
    NOT_CONTAINS = "not_contains"  # 不包含
    IS_NULL = "is_null"    # 为空
    IS_NOT_NULL = "is_not_null"  # 不为空
    GREATER_THAN = "gt"    # 大于
    LESS_THAN = "lt"       # 小于
    GREATER_EQUAL = "gte"   # 大于等于
    LESS_EQUAL = "lte"      # 小于等于


# 搜索条件模型
class SearchCondition(BaseModel):
    """单个搜索条件模型"""
    field: str  # 字段名
    operator: SearchOperator  # 操作符
    value: Optional[Any] = None  # 搜索值，对于 IS_NULL 和 IS_NOT_NULL 可以为 None


# 高级搜索请求模型
class KOLInfoAdvancedSearchRequest(BaseModel):
    """KOL信息高级搜索请求模型"""
    conditions: List[SearchCondition]  # 搜索条件列表
    platform: Optional[str] = None  # 兼容现有 platform 字段
    project_code: Optional[str] = None  # 兼容现有 project_code 字段
    skip: int = 0  # 分页偏移
    limit: int = 100  # 每页数量 
    conjunction: Literal["and", "or"] = "and"  # 条件之间的逻辑连接词，默认and