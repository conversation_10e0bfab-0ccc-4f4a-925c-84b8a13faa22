from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field


# 共享属性
class CollaborationPerformanceBase(BaseModel):
    """KOL合作表现数据的基础模型"""
    kol_id: str = Field(..., description="KOL ID")
    project: Optional[str] = Field(None, description="项目名称")
    paypal_accounts: Optional[str] = Field(None, description="Paypal账户信息")
    collab_status: Optional[str] = Field(None, description="合作状态")
    follow_up: Optional[str] = Field(None, description="跟进人")
    post_link: Optional[str] = Field(None, description="帖子链接")
    payment: Optional[str] = Field(None, description="支付金额，默认$，保留两位小数")
    payout_date: Optional[datetime] = Field(None, description="支付日期")
    fund_source: Optional[str] = Field(None, description="资金来源")
    kol_priority: Optional[str] = Field(None, description="KOL优先级")
    post_date: Optional[datetime] = Field(None, description="发布日期")
    
    # 内容表现数据
    views_total: Optional[int] = None
    likes_total: Optional[int] = None
    comments_total: Optional[int] = None
    shares_total: Optional[int] = None
    
    # 不同时间段的表现数据
    views_day1: Optional[int] = None
    likes_day1: Optional[int] = None
    comments_day1: Optional[int] = None
    shares_day1: Optional[int] = None
    
    views_day3: Optional[int] = None
    likes_day3: Optional[int] = None
    comments_day3: Optional[int] = None
    shares_day3: Optional[int] = None
    
    views_day7: Optional[int] = None
    likes_day7: Optional[int] = None
    comments_day7: Optional[int] = None
    shares_day7: Optional[int] = None
    
    # 衍生指标
    engagement_rate: Optional[float] = None
    cpm: Optional[float] = None


# 创建时的数据模型
class CollaborationPerformanceCreate(CollaborationPerformanceBase):
    """
    创建合作表现数据时使用的数据模型
    
    字段说明：
    - kol_id: KOL ID（必填）
    - project: 项目名称
    - paypal_accounts: Paypal账户信息
    - collab_status: 合作状态
    - follow_up: 跟进人
    - post_link: 帖子链接
    - payment: 支付金额
    - payout_date: 支付日期
    - fund_source: 资金来源
    """
    # kol_id是必填字段，其他字段为可选
    kol_id: str = Field(..., description="KOL ID")
    project: Optional[str] = Field(None, description="项目名称")
    paypal_accounts: Optional[str] = Field(None, description="Paypal账户信息")
    collab_status: Optional[str] = Field(None, description="合作状态")
    follow_up: Optional[str] = Field(None, description="跟进人")
    post_link: Optional[str] = Field(None, description="帖子链接")
    payment: Optional[str] = Field(None, description="支付金额，默认$，保留两位小数，只需要填入数字")
    payout_date: Optional[datetime] = Field(None, description="支付日期")
    fund_source: Optional[str] = Field(None, description="资金来源")


# 更新时的数据模型
class CollaborationPerformanceUpdate(BaseModel):
    """更新合作表现数据时的数据模型
    
    可更新字段：
    - kol_id: KOL ID
    - project: 项目
    - paypal_accounts: Paypal账户
    - collab_status: 合作状态
    - follow_up: 跟进人
    - post_link: 帖子链接
    - payment: 支付金额（默认$，保留两位小数，只需要填入数字）
    - payout_date: 支付日期
    - fund_source: 资金来源
    """
    kol_id: Optional[str] = Field(None, description="KOL ID")
    project: Optional[str] = Field(None, description="项目名称")
    paypal_accounts: Optional[str] = Field(None, description="Paypal账户信息")
    collab_status: Optional[str] = Field(None, description="合作状态")
    follow_up: Optional[str] = Field(None, description="跟进人")
    post_link: Optional[str] = Field(None, description="帖子链接")
    payment: Optional[str] = Field(None, description="支付金额，默认$，保留两位小数，只需要填入数字")
    payout_date: Optional[datetime] = Field(None, description="支付日期")
    fund_source: Optional[str] = Field(None, description="资金来源")
    kol_priority: Optional[str] = Field(None, description="KOL优先级")
    post_date: Optional[datetime] = Field(None, description="发布日期")
    
    # 内容表现数据
    views_total: Optional[int] = None
    likes_total: Optional[int] = None
    comments_total: Optional[int] = None
    shares_total: Optional[int] = None
    
    # 不同时间段的表现数据
    views_day1: Optional[int] = None
    likes_day1: Optional[int] = None
    comments_day1: Optional[int] = None
    shares_day1: Optional[int] = None
    
    views_day3: Optional[int] = None
    likes_day3: Optional[int] = None
    comments_day3: Optional[int] = None
    shares_day3: Optional[int] = None
    
    views_day7: Optional[int] = None
    likes_day7: Optional[int] = None
    comments_day7: Optional[int] = None
    shares_day7: Optional[int] = None
    
    # 衍生指标
    engagement_rate: Optional[float] = None
    cpm: Optional[float] = None


# 返回时的数据模型
class CollaborationPerformanceResponse(CollaborationPerformanceBase):
    """合作表现数据的响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        """配置类"""
        from_attributes = True


# 分页返回的数据模型
class CollaborationPerformancePaginatedResponse(BaseModel):
    """分页返回的合作表现数据模型"""
    items: List[CollaborationPerformanceResponse]
    total: int
    page: int
    size: int
    pages: int
    
    class Config:
        """配置类"""
        from_attributes = True


# 高级搜索请求模型
class CollaborationPerformanceAdvancedSearchRequest(BaseModel):
    """合作表现高级搜索请求模型"""
    kol_id: Optional[str] = None
    project: Optional[str] = None
    paypal_accounts: Optional[str] = None
    collab_status: Optional[str] = None
    follow_up: Optional[str] = None
    fund_source: Optional[str] = None
    kol_priority: Optional[str] = None
    post_date_from: Optional[datetime] = None
    post_date_to: Optional[datetime] = None
    payout_date_from: Optional[datetime] = None
    payout_date_to: Optional[datetime] = None
    
    # 筛选条件
    min_views: Optional[int] = None
    min_likes: Optional[int] = None
    min_comments: Optional[int] = None
    min_shares: Optional[int] = None
    
    min_engagement_rate: Optional[float] = None
    max_cpm: Optional[float] = None
    
    page: int = 1
    size: int = 10
    sort_by: Optional[str] = None
    sort_desc: bool = False