from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field

from app.schemas.tag import TagResponse


class ProjectTagAssociationBase(BaseModel):
    """项目标签关联的基础模型"""
    project_code: str = Field(..., description="项目编码")
    tag_id: int = Field(..., description="标签ID")


class ProjectTagAssociationCreate(ProjectTagAssociationBase):
    """创建项目标签关联的模型"""
    pass


class ProjectTagAssociationUpdate(BaseModel):
    """更新项目标签关联的模型"""
    project_code: Optional[str] = Field(None, description="项目编码")
    tag_id: Optional[int] = Field(None, description="标签ID")


class ProjectTagAssociationResponse(ProjectTagAssociationBase):
    """项目标签关联响应模型"""
    id: int = Field(..., description="关联ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class ProjectTagAssociationWithTagResponse(ProjectTagAssociationResponse):
    """包含标签信息的项目标签关联响应模型"""
    tag: Optional[TagResponse] = Field(None, description="关联的标签信息")


class ProjectTagAssociationPaginatedResponse(BaseModel):
    """项目标签关联分页响应模型"""
    items: List[ProjectTagAssociationResponse] = Field(..., description="关联列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class BatchProjectTagAssociationCreate(BaseModel):
    """批量创建项目标签关联的模型"""
    project_code: str = Field(..., description="项目编码")
    tag_ids: List[int] = Field(..., description="标签ID列表")


class BatchProjectTagAssociationDelete(BaseModel):
    """批量删除项目标签关联的模型"""
    project_code: str = Field(..., description="项目编码")
    tag_ids: List[int] = Field(..., description="标签ID列表")


class ProjectTagsResponse(BaseModel):
    """项目的所有标签响应模型"""
    project_code: str = Field(..., description="项目编码")
    tags: List[TagResponse] = Field(..., description="项目关联的标签列表")