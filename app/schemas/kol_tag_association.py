from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field

from app.schemas.tag import TagResponse


class KolTagAssociationBase(BaseModel):
    """KOL标签关联的基础模型"""
    kol_id: str = Field(..., description="KOL ID")
    tag_id: int = Field(..., description="标签ID")
    project_code: Optional[str] = Field(None, description="项目编码")


class KolTagAssociationCreate(KolTagAssociationBase):
    """创建KOL标签关联的模型"""
    pass


class KolTagAssociationUpdate(BaseModel):
    """更新KOL标签关联的模型"""
    project_code: Optional[str] = Field(None, description="项目编码")


class KolTagAssociationResponse(KolTagAssociationBase):
    """KOL标签关联响应模型"""
    id: int = Field(..., description="关联ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class KolTagAssociationWithTagResponse(KolTagAssociationResponse):
    """包含标签信息的KOL标签关联响应模型"""
    tag: Optional[TagResponse] = Field(None, description="关联的标签信息")


class KolTagAssociationPaginatedResponse(BaseModel):
    """KOL标签关联分页响应模型"""
    items: List[KolTagAssociationResponse] = Field(..., description="关联列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class BatchKolTagAssociationCreate(BaseModel):
    """批量创建KOL标签关联的模型"""
    kol_id: str = Field(..., description="KOL ID")
    tag_ids: List[int] = Field(..., description="标签ID列表")
    project_code: Optional[str] = Field(None, description="项目编码")


class BatchKolTagAssociationDelete(BaseModel):
    """批量删除KOL标签关联的模型"""
    kol_id: str = Field(..., description="KOL ID")
    tag_ids: List[int] = Field(..., description="标签ID列表")