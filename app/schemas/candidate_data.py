from datetime import datetime
from typing import List, Optional, Any, Literal

from pydantic import BaseModel


# 共享属性
class CandidateDataBase(BaseModel):
    """KOL候选人数据的基础模型"""
    kol_name: Optional[str] = None
    first_contact_date: Optional[datetime] = None
    last_contact_date: Optional[datetime] = None
    follow_up: Optional[str] = None
    label: Optional[str] = None
    sublabel: Optional[str] = None
    thread_id: Optional[str] = None
    project: Optional[str] = None
    reply_email: Optional[str] = None


# 创建时的数据模型
class CandidateDataCreate(CandidateDataBase):
    """创建候选人数据时的数据模型"""
    kol_id: str


# 更新时的数据模型
class CandidateDataUpdate(CandidateDataBase):
    """更新候选人数据时的数据模型"""
    kol_id: Optional[str] = None


# 返回时的数据模型
class CandidateDataResponse(CandidateDataBase):
    """候选人数据的响应模型"""
    id: int
    kol_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        """配置类"""
        from_attributes = True
        populate_by_name = True


# 分页响应模型
class CandidateDataPaginatedResponse(BaseModel):
    """带分页信息的候选人数据列表响应模型"""
    items: List[CandidateDataResponse]
    total: int
    page: int
    size: int
    pages: int

    class Config:
        """配置类"""
        from_attributes = True


# 搜索条件模型
class SearchCondition(BaseModel):
    """单个搜索条件模型"""
    field: str  # 字段名
    operator: str  # 操作符
    value: Optional[Any] = None  # 搜索值


# 高级搜索请求模型
class CandidateDataAdvancedSearchRequest(BaseModel):
    """候选人数据高级搜索请求模型"""
    conditions: List[SearchCondition]  # 搜索条件列表
    project: Optional[str] = None  # 按项目筛选
    label: Optional[str] = None  # 按标签筛选
    skip: int = 0  # 分页偏移
    limit: int = 100  # 每页数量
    conjunction: Literal["and", "or"] = "and"  # 条件之间的逻辑连接词，默认and


# 聚合候选人数据模型
class CandidateDataAggregated(BaseModel):
    """按kol_id聚合的候选人数据模型"""
    kol_id: str
    kol_name: Optional[str] = None
    # 相同字段显示一个值，不同字段聚合成数组
    first_contact_dates: List[Optional[datetime]] = []
    last_contact_dates: List[Optional[datetime]] = []
    follow_ups: List[Optional[str]] = []
    labels: List[Optional[str]] = []
    sublabels: List[Optional[str]] = []
    thread_ids: List[Optional[str]] = []
    projects: List[Optional[str]] = []
    reply_emails: List[Optional[str]] = []
    ids: List[int] = []
    created_ats: List[datetime] = []
    updated_ats: List[datetime] = []
    record_count: int = 0  # 该kol_id的记录数量

    class Config:
        """配置类"""
        from_attributes = True
        populate_by_name = True


# 聚合分页响应模型
class CandidateDataAggregatedPaginatedResponse(BaseModel):
    """按kol_id聚合的带分页信息的候选人数据列表响应模型"""
    items: List[CandidateDataAggregated]
    total: int
    page: int
    size: int
    pages: int

    class Config:
        """配置类"""
        from_attributes = True