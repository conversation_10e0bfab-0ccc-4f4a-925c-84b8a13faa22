from datetime import datetime
from typing import Any, List, Optional

from pydantic import BaseModel, Field, field_validator


# 共享属性
class VideoInfoBase(BaseModel):
    """视频信息的基础模型"""
    kol_id: str
    play_count: Optional[int] = None
    is_pinned: bool = False
    share_url: Optional[str] = None
    desc: Optional[str] = None
    desc_language: Optional[str] = None
    video_url: Optional[str] = None
    music_url: Optional[str] = None
    
    # 互动数据
    likes_count: Optional[int] = None
    comments_count: Optional[int] = None
    shares_count: Optional[int] = None
    collect_count: Optional[int] = None
    
    # 平台信息
    platform: Optional[str] = None
    
    # 标签和内容分析
    hashtags: Optional[List[str]] = None
    
    # 时间信息
    create_time: Optional[datetime] = None
    
    @field_validator('platform', mode='before')
    @classmethod
    def normalize_platform(cls, value: Any) -> Any:
        """将平台名称转换为小写以匹配枚举值"""
        if isinstance(value, str):
            return value.lower()
        return value


# 创建时的数据模型
class VideoInfoCreate(VideoInfoBase):
    """创建视频信息时的数据模型"""
    video_id: str


# 更新时的数据模型
class VideoInfoUpdate(BaseModel):
    """更新视频信息时的数据模型"""
    play_count: Optional[int] = None
    is_pinned: Optional[bool] = None
    share_url: Optional[str] = None
    desc: Optional[str] = None
    desc_language: Optional[str] = None
    video_url: Optional[str] = None
    music_url: Optional[str] = None
    
    # 互动数据
    likes_count: Optional[int] = None
    comments_count: Optional[int] = None
    shares_count: Optional[int] = None
    collect_count: Optional[int] = None
    
    # 平台信息
    platform: Optional[str] = None
    
    # 标签和内容分析
    hashtags: Optional[List[str]] = None
    
    # 时间信息
    create_time: Optional[datetime] = None


# 返回时的数据模型
class VideoInfoResponse(VideoInfoBase):
    """视频信息的响应模型"""
    video_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        """配置类"""
        from_attributes = True