from datetime import datetime
from typing import Optional

from pydantic import BaseModel


# 共享属性
class FilterKolAssociationBase(BaseModel):
    """筛选条件和KOL关联的基础模型"""
    filter_id: int
    kol_id: str
    project_code: Optional[str] = None


# 创建时的数据模型
class FilterKolAssociationCreate(FilterKolAssociationBase):
    """创建关联时的数据模型"""
    pass


# 更新时的数据模型
class FilterKolAssociationUpdate(BaseModel):
    """更新关联时的数据模型"""
    filter_id: Optional[int] = None
    kol_id: Optional[str] = None
    project_code: Optional[str] = None


# 返回时的数据模型
class FilterKolAssociationResponse(FilterKolAssociationBase):
    """关联的响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        """配置类"""
        from_attributes = True 