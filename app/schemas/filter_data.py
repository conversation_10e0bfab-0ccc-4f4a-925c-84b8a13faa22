from datetime import datetime
from typing import Dict, Optional, List, TypeVar, Generic

from pydantic import BaseModel, Field


# 定义一个泛型类型变量
T = TypeVar('T')


# 通用分页响应模型
class PaginatedResponse(BaseModel, Generic[T]):
    """通用分页响应模型"""
    items: List[T]  # 当前页的数据项
    total: int  # 总条目数
    page: int = 1  # 当前页码
    size: int = 100  # 每页大小
    pages: int = 1  # 总页数

    class Config:
        """配置类"""
        from_attributes = True


# 共享属性
class FilterDataBase(BaseModel):
    """筛选条件的基础模型"""
    language: Optional[str] = None
    gender: Optional[str] = None
    location: Optional[str] = None
    filter_body: Optional[Dict] = None
    filter_name: Optional[str] = None
    project_code: Optional[str] = None


# 创建时的数据模型
class FilterDataCreate(FilterDataBase):
    """创建筛选条件时的数据模型"""
    pass


# 更新时的数据模型
class FilterDataUpdate(FilterDataBase):
    """更新筛选条件时的数据模型"""
    pass


# 返回时的数据模型
class FilterDataResponse(FilterDataBase):
    """筛选条件的响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        """配置类"""
        from_attributes = True


# 带分页的筛选条件响应模型
class FilterDataPaginatedResponse(PaginatedResponse[FilterDataResponse]):
    """带分页信息的筛选条件列表响应模型"""
    pass


# 简化的返回数据模型，仅包含 project_code 和 filter_name
class FilterSimpleResponse(BaseModel):
    """简化的筛选条件响应模型，仅包含 filter_name 和 project_code"""
    filter_name: Optional[str] = None
    project_code: Optional[str] = None

    class Config:
        """配置类"""
        from_attributes = True


# 返回两个数组的响应模型
class FilterArraysResponse(BaseModel):
    """返回 filter_names 和 project_codes 两个数组的响应模型"""
    filter_names: List[str] = []
    project_codes: List[str] = [] 