from datetime import datetime
from typing import Optional, List
from datetime import datetime

from pydantic import BaseModel, Field


# 共享属性
class SendDataBase(BaseModel):
    """发送数据的基础模型"""
    kol_id: str
    send_status: Optional[str] = None
    platform: Optional[str] = None
    send_date: Optional[datetime] = None
    export_date: Optional[datetime] = None
    
    # 新增字段
    app_code: Optional[str] = None
    template_id: Optional[str] = None
    read_status: bool = False
    success: bool = False
    from_email: Optional[str] = None
    to_email: Optional[str] = None


# 创建时的数据模型
class SendDataCreate(SendDataBase):
    """创建发送数据时的数据模型"""
    id: str


# 更新时的数据模型
class SendDataUpdate(BaseModel):
    """更新发送数据时的数据模型"""
    send_status: Optional[str] = None
    platform: Optional[str] = None
    send_date: Optional[datetime] = None
    export_date: Optional[datetime] = None
    
    # 新增字段
    app_code: Optional[str] = None
    template_id: Optional[str] = None
    read_status: Optional[bool] = None
    success: Optional[bool] = None
    from_email: Optional[str] = None
    to_email: Optional[str] = None


# 返回时的数据模型
class SendDataResponse(SendDataBase):
    """发送数据的响应模型"""
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        """配置类"""
        from_attributes = True


# 分页返回的数据模型
class SendDataPaginatedResponse(BaseModel):
    """分页返回的发送数据模型"""
    items: List[SendDataResponse]
    total: int
    page: int
    size: int
    pages: int
    
    class Config:
        """配置类"""
        from_attributes = True


# 发送状态列表响应模型
class SendStatusListResponse(BaseModel):
    """发送状态列表的响应模型"""
    statuses: List[str]