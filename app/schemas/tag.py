from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


class TagBase(BaseModel):
    """标签的基础模型"""
    name: str = Field(..., description="标签名称", max_length=255)
    description: Optional[str] = Field("", description="标签描述")


class TagCreate(TagBase):
    """创建标签的模型"""
    pass


class TagUpdate(BaseModel):
    """更新标签的模型"""
    name: Optional[str] = Field(None, description="标签名称", max_length=255)
    description: Optional[str] = Field(None, description="标签描述")


class TagResponse(TagBase):
    """标签响应模型"""
    id: int = Field(..., description="标签ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class TagPaginatedResponse(BaseModel):
    """标签分页响应模型"""
    items: List[TagResponse] = Field(..., description="标签列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")