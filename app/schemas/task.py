"""
任务相关模式
用于API请求和响应的数据验证
"""
from typing import Optional, Any, Dict, List
from pydantic import BaseModel, Field


class TaskBase(BaseModel):
    """任务基础模型"""
    task_name: str = Field(..., description="任务名称")


class TaskCreate(TaskBase):
    """创建任务请求模型"""
    args: Optional[List[Any]] = Field(default=None, description="任务参数列表")
    kwargs: Optional[Dict[str, Any]] = Field(default=None, description="任务命名参数")
    queue: Optional[str] = Field(default=None, description="指定队列名称")
    countdown: Optional[int] = Field(default=None, description="延迟执行时间(秒)")


class TaskStatus(BaseModel):
    """任务状态响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态 (PENDING, STARTED, SUCCESS, FAILURE, REVOKED, RETRY, PROGRESS)")
    result: Optional[Any] = Field(default=None, description="任务结果，仅当任务成功完成时存在")
    error: Optional[str] = Field(default=None, description="错误信息，仅当任务失败时存在")
    progress: Optional[int] = Field(default=None, description="任务进度百分比，仅当任务处于PROGRESS状态时存在")
    
    class Config:
        """配置"""
        json_schema_extra = {
            "example": {
                "task_id": "7b1a0d1c-c3e5-4c7b-9a3b-0e3f5a9a7d2c",
                "status": "SUCCESS",
                "result": {"processed": True, "message": "任务完成"},
                "error": None,
                "progress": None
            }
        }


class QueueTaskInfo(BaseModel):
    """队列中的任务信息"""
    task_id: str = Field(..., description="任务ID")
    filter_name: Optional[str] = Field(default=None, description="筛选器名称")
    
    class Config:
        """配置"""
        json_schema_extra = {
            "example": {
                "task_id": "7b1a0d1c-c3e5-4c7b-9a3b-0e3f5a9a7d2c",
                "filter_name": "modash-Test-US-64"
            }
        }


class QueueStatusResponse(BaseModel):
    """队列状态响应模型"""
    active_tasks: List[QueueTaskInfo] = Field(default_factory=list, description="正在执行的任务")
    reserved_tasks: List[QueueTaskInfo] = Field(default_factory=list, description="等待执行的任务")
    completed_tasks: List[QueueTaskInfo] = Field(default_factory=list, description="已完成的任务")
    failed_tasks: List[QueueTaskInfo] = Field(default_factory=list, description="执行失败的任务")
    
    class Config:
        """配置"""
        json_schema_extra = {
            "example": {
                "active_tasks": [
                    {
                        "task_id": "7b1a0d1c-c3e5-4c7b-9a3b-0e3f5a9a7d2c",
                        "filter_name": "modash-Test-US-64"
                    }
                ],
                "reserved_tasks": [],
                "completed_tasks": [
                    {
                        "task_id": "8c2b1e2d-d4f6-5d8c-0b4c-1f4g6b0b8e3d",
                        "filter_name": "modash-Test-US-63"
                    }
                ],
                "failed_tasks": []
            }
        }


class KOLDataTask(BaseModel):
    """KOL数据采集任务请求模型"""
    source: str = Field(..., description="数据源，例如'creable'")
    platform: str = Field(..., description="平台，例如'Tiktok'")
    filter_name: str = Field(..., description="筛选器名称")
    filter_body: str = Field(..., description="筛选条件正文")
    cookie: str = Field(..., description="认证cookie")
    project_code: Optional[str] = Field(default="", description="项目代码")
    auto_send: Optional[str] = Field(default="no", description="是否自动发送 (yes/no)")
    template: Optional[str] = Field(default="", description="模板名称")
    high_potential: Optional[str] = Field(default="no", description="是否为高潜力KOL (yes/no)")
    table_save: Optional[str] = Field(default="", description="保存表格的URL")
    table_duplicate: Optional[str] = Field(default="", description="重复表格的URL")
    click: Optional[str] = Field(default="false", description="是否点击 (true/false)")
    
    class Config:
        """配置"""
        json_schema_extra = {
            "example": {
                "source": "creable",
                "platform": "Tiktok",
                "filter_name": "modash-Test-US-64",
                "filter_body": "",
                "cookie": "example_cookie_value",
                "project_code": "test001",
                "auto_send": "yes",
                "high_potential": "yes",
                "click": "false"
            }
        }


class TaskHealthStatus(BaseModel):
    """任务健康状态模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    is_zombie: bool = Field(..., description="是否为僵尸任务")
    last_heartbeat: Optional[float] = Field(None, description="最后一次心跳时间戳")
    seconds_since_update: Optional[float] = Field(None, description="自上次更新以来的秒数")
    progress: Optional[int] = Field(None, description="任务进度")
    error: Optional[str] = Field(None, description="错误信息")
    
    class Config:
        """配置"""
        json_schema_extra = {
            "example": {
                "task_id": "7b1a0d1c-c3e5-4c7b-9a3b-0e3f5a9a7d2c",
                "status": "PROGRESS",
                "is_zombie": False,
                "last_heartbeat": **********.45,
                "seconds_since_update": 15.3,
                "progress": 45
            }
        }


class TaskCancelResponse(BaseModel):
    """任务取消响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    
    class Config:
        """配置"""
        json_schema_extra = {
            "example": {
                "task_id": "7b1a0d1c-c3e5-4c7b-9a3b-0e3f5a9a7d2c",
                "status": "REVOKED"
            }
        }
