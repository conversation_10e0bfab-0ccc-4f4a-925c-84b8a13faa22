import os
import tomli
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, PostgresDsn, RedisDsn, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """应用配置类"""
    # 项目基本信息
    PROJECT_NAME: str = "KOL 数据管理与分析平台"
    API_V1_STR: str = "/api/v1"
    VERSION: str = "2.1.0"
    
    # CORS配置
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []
    
    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # 数据库配置
    USE_SQLITE: bool = False
    POSTGRES_SERVER: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str
    POSTGRES_PORT: int
    DATABASE_URI: Optional[Union[str, PostgresDsn]] = None

    @field_validator("DATABASE_URI", mode="before")
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
            
        # 如果USE_SQLITE为True，则使用SQLite数据库
        if values.data.get("USE_SQLITE", False):
            sqlite_file = os.path.join(os.getcwd(), "kol_platform.db")
            return f"sqlite:///{sqlite_file}"
        
        # 检查所有必要的PostgreSQL配置项
        required_fields = ["POSTGRES_SERVER", "POSTGRES_USER", "POSTGRES_PASSWORD", "POSTGRES_DB", "POSTGRES_PORT"]
        for field in required_fields:
            if field not in values.data or not values.data.get(field):
                raise ValueError(f"缺少必要的数据库配置项: {field}")
            
        return PostgresDsn.build(
            scheme="postgresql+psycopg2",
            username=values.data.get("POSTGRES_USER"),
            password=values.data.get("POSTGRES_PASSWORD"),
            host=values.data.get("POSTGRES_SERVER"),
            port=values.data.get("POSTGRES_PORT"),
            path=f"{values.data.get('POSTGRES_DB') or ''}",
        )
    
    # Redis配置
    REDIS_HOST: str
    REDIS_PORT: int
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int
    REDIS_URI: Optional[Union[str, RedisDsn]] = None
    
    @field_validator("REDIS_URI", mode="before")
    def assemble_redis_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        
        # 检查所有必要的Redis配置项
        required_fields = ["REDIS_HOST", "REDIS_PORT", "REDIS_DB"]
        for field in required_fields:
            if field not in values.data or values.data.get(field) is None:
                raise ValueError(f"缺少必要的Redis配置项: {field}")
        
        # 构建Redis连接字符串而不是RedisDsn对象
        host = values.data.get("REDIS_HOST")
        port = values.data.get("REDIS_PORT")
        password = values.data.get("REDIS_PASSWORD", "")
        db = values.data.get("REDIS_DB")
        
        # 组装Redis URL字符串
        if password:
            return f"redis://:{password}@{host}:{port}/{db}"
        else:
            return f"redis://{host}:{port}/{db}"
    
    # RabbitMQ配置
    RABBITMQ_HOST: str
    RABBITMQ_PORT: int
    RABBITMQ_USER: str
    RABBITMQ_PASSWORD: str
    RABBITMQ_VHOST: str
    RABBITMQ_URI: Optional[str] = None
    
    @field_validator("RABBITMQ_URI", mode="before")
    def assemble_rabbitmq_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        
        # 检查所有必要的RabbitMQ配置项
        required_fields = ["RABBITMQ_HOST", "RABBITMQ_PORT", "RABBITMQ_USER", "RABBITMQ_PASSWORD", "RABBITMQ_VHOST"]
        for field in required_fields:
            if field not in values.data or not values.data.get(field):
                raise ValueError(f"缺少必要的RabbitMQ配置项: {field}")
            
        username = values.data.get("RABBITMQ_USER")
        password = values.data.get("RABBITMQ_PASSWORD")
        host = values.data.get("RABBITMQ_HOST")
        port = values.data.get("RABBITMQ_PORT")
        vhost = values.data.get("RABBITMQ_VHOST")
        
        # 如果vhost不是以/开头，则添加/
        if vhost and not vhost.startswith("/"):
            vhost = f"/{vhost}"
            
        return f"amqp://{username}:{password}@{host}:{port}{vhost}"
    
    # Celery配置
    CELERY_BROKER_URL: Optional[str] = None
    CELERY_RESULT_BACKEND: Optional[str] = None
    CELERY_WORKER_CONCURRENCY: int = 1
    CELERY_MONITORING_ENABLED: bool = True
    CELERY_TASK_TRACK_STARTED: bool = True
    CELERY_RESULT_EXPIRES: int = 86400  # 结果过期时间，单位秒(1天)
    
    @field_validator("CELERY_BROKER_URL", mode="before")
    def assemble_celery_broker_url(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        """组装Celery Broker URL (RabbitMQ)"""
        if isinstance(v, str):
            return v
        
        # 使用已经组装好的RabbitMQ URI作为Broker URL
        rabbitmq_uri = values.data.get("RABBITMQ_URI")
        if rabbitmq_uri:
            return rabbitmq_uri
        
        # 如果没有URI，尝试手动组装，缺少配置项会在RABBITMQ_URI验证时抛出异常
        return cls.assemble_rabbitmq_connection(None, values)
    
    @field_validator("CELERY_RESULT_BACKEND", mode="before")
    def assemble_celery_result_backend(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        """组装Celery Result Backend URL (Redis)"""
        if isinstance(v, str):
            return v
        
        # 直接使用Redis配置构建字符串URL
        host = values.data.get("REDIS_HOST")
        port = values.data.get("REDIS_PORT")
        password = values.data.get("REDIS_PASSWORD", "")
        db = values.data.get("REDIS_DB")
        
        if not all([host, port, db is not None]):
            required_fields = ["REDIS_HOST", "REDIS_PORT", "REDIS_DB"]
            for field in required_fields:
                if field not in values.data or values.data.get(field) is None:
                    raise ValueError(f"缺少必要的Redis配置项: {field}")
        
        # 组装Redis URL字符串
        if password:
            return f"redis://:{password}@{host}:{port}/{db}"
        else:
            return f"redis://{host}:{port}/{db}"
    
    # RedBeat配置
    REDBEAT_KEY_PREFIX: str = "redbeat:"
    REDBEAT_LOCK_KEY: str = "redbeat:lock:"
    REDBEAT_LOCK_TIMEOUT: int = 60
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE_PATH: str = "logs"
    LOG_FILE_NAME: str = "kol_platform.log"
    LOG_MAX_BYTES: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5  # 保留5个备份文件
    
    # 飞书配置
    LARK_APP_ID: str
    LARK_APP_SECRET: str
    FEISHU_WEB_HOOK_URL: str
    FEISHU_EMAIL_APP_TOKEN: str
    FEISHU_EMAIL_TABLE_ID: str
    UPDATE: bool
    
    @property
    def log_file(self) -> str:
        """获取完整的日志文件路径"""
        os.makedirs(self.LOG_FILE_PATH, exist_ok=True)
        return os.path.join(self.LOG_FILE_PATH, self.LOG_FILE_NAME)
    
    model_config = SettingsConfigDict(
        case_sensitive=True,
        extra="ignore",
        env_file=".env",
        env_file_encoding="utf-8",
    )


def load_toml_config(config_path: str = "config.toml") -> Dict[str, Any]:
    """加载TOML配置文件"""
    try:
        with open(config_path, "rb") as f:
            return tomli.load(f)
    except FileNotFoundError:
        print(f"警告: 配置文件 {config_path} 不存在，将使用默认配置")
        return {}
    except Exception as e:
        print(f"错误: 加载配置文件 {config_path} 失败: {e}")
        return {}


def create_settings_from_toml() -> Settings:
    """从TOML配置文件创建Settings实例"""
    # 先检查环境变量中是否有配置路径
    config_path = os.environ.get("CONFIG_PATH", "config.toml")
    config_data = load_toml_config(config_path)
    
    # 展平嵌套的配置项
    flat_config = {}
    for key, value in config_data.items():
        if isinstance(value, dict):
            for subkey, subvalue in value.items():
                flat_config[subkey] = subvalue
        else:
            flat_config[key] = value
    
    # 检查端口号类型
    port_fields = ["POSTGRES_PORT", "REDIS_PORT", "RABBITMQ_PORT"]
    for field in port_fields:
        if field in flat_config and not isinstance(flat_config[field], int):
            try:
                flat_config[field] = int(flat_config[field])
            except (ValueError, TypeError):
                raise ValueError(f"错误: 无法将 {field} 的值 '{flat_config[field]}' 转换为整数")
    
    # 首先创建Settings实例，此时会自动从环境变量中读取值
    settings_instance = Settings(**flat_config)
    return settings_instance


# 创建全局设置实例
settings = create_settings_from_toml()
