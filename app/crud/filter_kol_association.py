from typing import List, Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.filter_kol_association import FilterKolAssociation
from app.schemas.filter_kol_association import FilterKolAssociationCreate, FilterKolAssociationUpdate


class CRUDFilterKolAssociation(CRUDBase[FilterKolAssociation, FilterKolAssociationCreate, FilterKolAssociationUpdate]):
    """筛选条件和KOL关联的CRUD操作"""
    
    def get_by_filter_id_and_kol_id(self, db: Session, *, filter_id: int, kol_id: str) -> Optional[FilterKolAssociation]:
        """通过筛选条件ID和KOL ID获取关联对象"""
        return db.query(self.model).filter(
            self.model.filter_id == filter_id,
            self.model.kol_id == kol_id
        ).first()
    
    def get_multi_by_filter_id(
        self, db: Session, *, filter_id: int, skip: int = 0, limit: int = 100
    ) -> List[FilterKolAssociation]:
        """通过筛选条件ID获取多个关联对象"""
        return (
            db.query(self.model)
            .filter(self.model.filter_id == filter_id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_kol_id(
        self, db: Session, *, kol_id: str, skip: int = 0, limit: int = 100
    ) -> List[FilterKolAssociation]:
        """通过KOL ID获取多个关联对象"""
        return (
            db.query(self.model)
            .filter(self.model.kol_id == kol_id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_project_code(
        self, db: Session, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> List[FilterKolAssociation]:
        """通过项目代码获取多个关联对象"""
        return (
            db.query(self.model)
            .filter(self.model.project_code == project_code)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_kol_and_project(
        self, db: Session, *, kol_id: str, project_code: str
    ) -> Optional[FilterKolAssociation]:
        """通过KOL ID和项目代码获取关联对象"""
        return db.query(self.model).filter(
            self.model.kol_id == kol_id,
            self.model.project_code == project_code
        ).first()
    
    def get_by_filter_kol_project(
        self, db: Session, *, filter_id: int, kol_id: str, project_code: str
    ) -> Optional[FilterKolAssociation]:
        """通过Filter ID、KOL ID和项目代码获取关联对象"""
        return db.query(self.model).filter(
            self.model.filter_id == filter_id,
            self.model.kol_id == kol_id,
            self.model.project_code == project_code
        ).first()
    
    # 异步方法
    async def async_get_by_filter_id_and_kol_id(
        self, db: AsyncSession, *, filter_id: int, kol_id: str
    ) -> Optional[FilterKolAssociation]:
        """异步通过筛选条件ID和KOL ID获取关联对象"""
        result = await db.execute(
            select(self.model).filter(
                self.model.filter_id == filter_id,
                self.model.kol_id == kol_id
            )
        )
        return result.scalars().first()
    
    async def async_get_multi_by_filter_id(
        self, db: AsyncSession, *, filter_id: int, skip: int = 0, limit: int = 100
    ) -> List[FilterKolAssociation]:
        """异步通过筛选条件ID获取多个关联对象"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.filter_id == filter_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_multi_by_kol_id(
        self, db: AsyncSession, *, kol_id: str, skip: int = 0, limit: int = 100
    ) -> List[FilterKolAssociation]:
        """异步通过KOL ID获取多个关联对象"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.kol_id == kol_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_multi_by_project_code(
        self, db: AsyncSession, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> List[FilterKolAssociation]:
        """异步通过项目代码获取多个关联对象"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.project_code == project_code)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    def delete_by_kol_id(self, db: Session, *, kol_id: str) -> int:
        """删除KOL的所有filter关联"""
        deleted_count = (
            db.query(self.model)
            .filter(self.model.kol_id == kol_id)
            .delete(synchronize_session=False)
        )
        db.commit()
        return deleted_count
    
    def delete_by_kol_id_without_commit(self, db: Session, *, kol_id: str) -> int:
        """删除KOL的所有filter关联但不提交事务"""
        deleted_count = (
            db.query(self.model)
            .filter(self.model.kol_id == kol_id)
            .delete(synchronize_session=False)
        )
        db.flush()  # 刷新以确保删除操作被执行，但不提交
        return deleted_count


filter_kol_association = CRUDFilterKolAssociation(FilterKolAssociation)