from typing import List, Optional
from datetime import datetime

from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.collaboration_performance import CollaborationPerformance
from app.schemas.collaboration_performance import CollaborationPerformanceCreate, CollaborationPerformanceUpdate


class CRUDCollaborationPerformance(CRUDBase[CollaborationPerformance, CollaborationPerformanceCreate, CollaborationPerformanceUpdate]):
    """KOL合作表现数据的CRUD操作"""
    
    def get_by_kol_id(
        self, db: Session, *, kol_id: str, skip: int = 0, limit: int = 100
    ) -> List[CollaborationPerformance]:
        """通过KOL ID获取合作表现数据（支持分页）"""
        return (
            db.query(self.model)
            .filter(self.model.kol_id == kol_id)
            .order_by(self.model.updated_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_project(
        self, db: Session, *, project: str, skip: int = 0, limit: int = 100
    ) -> List[CollaborationPerformance]:
        """通过项目名称获取合作表现数据（支持分页）"""
        return (
            db.query(self.model)
            .filter(self.model.project == project)
            .order_by(self.model.updated_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_post_date_range(
        self, db: Session, *, start_date: datetime, end_date: datetime, skip: int = 0, limit: int = 100
    ) -> List[CollaborationPerformance]:
        """通过发布日期范围获取合作表现数据（支持分页）"""
        return (
            db.query(self.model)
            .filter(
                and_(
                    self.model.post_date >= start_date,
                    self.model.post_date <= end_date
                )
            )
            .order_by(self.model.updated_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_kol_id_and_project(
        self, db: Session, *, kol_id: str, project: str, skip: int = 0, limit: int = 100
    ) -> List[CollaborationPerformance]:
        """通过KOL ID和项目名称获取合作表现数据（支持分页）"""
        return (
            db.query(self.model)
            .filter(
                and_(
                    self.model.kol_id == kol_id,
                    self.model.project == project
                )
            )
            .order_by(self.model.updated_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    # 计数方法
    def count(self, db: Session) -> int:
        """获取总记录数"""
        return db.query(func.count(self.model.id)).scalar()
    
    def count_by_kol_id(self, db: Session, *, kol_id: str) -> int:
        """通过KOL ID获取记录数"""
        return (
            db.query(func.count(self.model.id))
            .filter(self.model.kol_id == kol_id)
            .scalar()
        )
    
    def count_by_project(self, db: Session, *, project: str) -> int:
        """通过项目名称获取记录数"""
        return (
            db.query(func.count(self.model.id))
            .filter(self.model.project == project)
            .scalar()
        )
    
    def count_by_post_date_range(
        self, db: Session, *, start_date: datetime, end_date: datetime
    ) -> int:
        """通过发布日期范围获取记录数"""
        return (
            db.query(func.count(self.model.id))
            .filter(
                and_(
                    self.model.post_date >= start_date,
                    self.model.post_date <= end_date
                )
            )
            .scalar()
        )
    
    def count_by_kol_id_and_project(
        self, db: Session, *, kol_id: str, project: str
    ) -> int:
        """通过KOL ID和项目名称获取记录数"""
        return (
            db.query(func.count(self.model.id))
            .filter(
                and_(
                    self.model.kol_id == kol_id,
                    self.model.project == project
                )
            )
            .scalar()
        )
    
    # 异步方法
    async def async_get_by_kol_id(
        self, db: AsyncSession, *, kol_id: str, skip: int = 0, limit: int = 100
    ) -> List[CollaborationPerformance]:
        """异步通过KOL ID获取合作表现数据（支持分页）"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.kol_id == kol_id)
            .order_by(self.model.updated_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_by_project(
        self, db: AsyncSession, *, project: str, skip: int = 0, limit: int = 100
    ) -> List[CollaborationPerformance]:
        """异步通过项目名称获取合作表现数据（支持分页）"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.project == project)
            .order_by(self.model.updated_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_by_post_date_range(
        self, db: AsyncSession, *, start_date: datetime, end_date: datetime, skip: int = 0, limit: int = 100
    ) -> List[CollaborationPerformance]:
        """异步通过发布日期范围获取合作表现数据（支持分页）"""
        result = await db.execute(
            select(self.model)
            .filter(
                and_(
                    self.model.post_date >= start_date,
                    self.model.post_date <= end_date
                )
            )
            .order_by(self.model.updated_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_by_kol_id_and_project(
        self, db: AsyncSession, *, kol_id: str, project: str, skip: int = 0, limit: int = 100
    ) -> List[CollaborationPerformance]:
        """异步通过KOL ID和项目名称获取合作表现数据（支持分页）"""
        result = await db.execute(
            select(self.model)
            .filter(
                and_(
                    self.model.kol_id == kol_id,
                    self.model.project == project
                )
            )
            .order_by(self.model.updated_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    # 异步计数方法
    async def async_count(self, db: AsyncSession) -> int:
        """异步获取总记录数"""
        result = await db.execute(select(func.count(self.model.id)))
        return result.scalar()
    
    async def async_count_by_kol_id(self, db: AsyncSession, *, kol_id: str) -> int:
        """异步通过KOL ID获取记录数"""
        result = await db.execute(
            select(func.count(self.model.id))
            .filter(self.model.kol_id == kol_id)
        )
        return result.scalar()
    
    def update_by_kol_id(
        self, db: Session, *, kol_id: str, obj_in: CollaborationPerformanceUpdate
    ) -> List[CollaborationPerformance]:
        """根据KOL ID更新所有相关的合作表现数据"""
        # 获取所有匹配的记录
        performances = db.query(self.model).filter(self.model.kol_id == kol_id).all()
        
        if not performances:
            return []
        
        # 获取更新数据，排除未设置的字段
        update_data = obj_in.dict(exclude_unset=True)
        
        # 更新所有匹配的记录
        updated_performances = []
        for performance in performances:
            for field, value in update_data.items():
                setattr(performance, field, value)
            
            # 更新时间戳
            performance.updated_at = datetime.utcnow()
            updated_performances.append(performance)
        
        db.commit()
        
        # 刷新所有对象以获取最新数据
        for performance in updated_performances:
            db.refresh(performance)
        
        return updated_performances
    
    async def async_count_by_project(self, db: AsyncSession, *, project: str) -> int:
        """异步通过项目名称获取记录数"""
        result = await db.execute(
            select(func.count(self.model.id))
            .filter(self.model.project == project)
        )
        return result.scalar()
    
    async def async_count_by_post_date_range(
        self, db: AsyncSession, *, start_date: datetime, end_date: datetime
    ) -> int:
        """异步通过发布日期范围获取记录数"""
        result = await db.execute(
            select(func.count(self.model.id))
            .filter(
                and_(
                    self.model.post_date >= start_date,
                    self.model.post_date <= end_date
                )
            )
        )
        return result.scalar()
    
    async def async_count_by_kol_id_and_project(
        self, db: AsyncSession, *, kol_id: str, project: str
    ) -> int:
        """异步通过KOL ID和项目名称获取记录数"""
        result = await db.execute(
            select(func.count(self.model.id))
            .filter(
                and_(
                    self.model.kol_id == kol_id,
                    self.model.project == project
                )
            )
        )
        return result.scalar()


collaboration_performance = CRUDCollaborationPerformance(CollaborationPerformance)