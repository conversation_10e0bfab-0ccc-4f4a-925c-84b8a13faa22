from typing import List, Optional, Dict, Tuple

from sqlalchemy import select, cast, JSON, func, distinct
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy.dialects.postgresql import JSONB

from app.crud.base import CRUDBase
from app.models.filter_data import FilterData
from app.models.filter_kol_association import FilterKolAssociation
from app.models.kol_info import KOLInfo
from app.schemas.filter_data import FilterDataCreate, FilterDataUpdate
import json


class CRUDFilterData(CRUDBase[FilterData, FilterDataCreate, FilterDataUpdate]):
    """筛选条件数据的CRUD操作"""
    
    def get_by_name(self, db: Session, *, name: str) -> Optional[FilterData]:
        """通过筛选条件名称获取对象"""
        return db.query(self.model).filter(self.model.filter_name == name).first()
    
    def get_by_name_and_project_code_and_body(self, db: Session, *, name: str, project_code: str, filter_body: dict) -> Optional[FilterData]:
        """通过筛选条件名称、项目代码和筛选条件主体获取对象"""
        # 将 filter_body 转换为 JSON 字符串
        filter_body_str = json.dumps(filter_body, sort_keys=True)
        
        # 查询所有符合条件的记录
        candidates = (
            db.query(self.model)
            .filter(self.model.filter_name == name, self.model.project_code == project_code)
            .all()
        )
        
        # 在 Python 中比较 JSON 内容
        for candidate in candidates:
            # 将数据库中的 filter_body 转换为相同格式的 JSON 字符串
            db_filter_body_str = json.dumps(candidate.filter_body, sort_keys=True)
            if db_filter_body_str == filter_body_str:
                return candidate
                
        return None
    
    def get_multi_by_language(
        self, db: Session, *, language: str, skip: int = 0, limit: int = 100
    ) -> List[FilterData]:
        """通过语言获取多个筛选条件对象"""
        return (
            db.query(self.model)
            .filter(self.model.language == language)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_project_code(
        self, db: Session, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> List[FilterData]:
        """通过项目代码获取多个筛选条件对象"""
        return (
            db.query(self.model)
            .filter(self.model.project_code == project_code)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_kol_id(
        self, db: Session, *, kol_id: str, skip: int = 0, limit: int = 100
    ) -> List[FilterData]:
        """通过KOL ID获取多个筛选条件对象"""
        return (
            db.query(self.model)
            .join(FilterKolAssociation, FilterKolAssociation.filter_id == self.model.id)
            .filter(FilterKolAssociation.kol_id == kol_id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_with_count(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> Tuple[List[FilterData], int]:
        """
        获取多个筛选条件对象并返回总数
        
        返回:
            元组 (筛选条件列表, 总数)
        """
        # 获取总数
        total = db.query(func.count(self.model.id)).scalar()
        
        # 获取数据
        items = db.query(self.model).offset(skip).limit(limit).all()
        
        return items, total
    
    def get_multi_by_project_code_with_count(
        self, db: Session, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[FilterData], int]:
        """
        通过项目代码获取多个筛选条件对象并返回总数
        
        返回:
            元组 (筛选条件列表, 总数)
        """
        # 获取总数
        total = db.query(func.count(self.model.id)).filter(
            self.model.project_code == project_code
        ).scalar()
        
        # 获取数据
        items = db.query(self.model).filter(
            self.model.project_code == project_code
        ).offset(skip).limit(limit).all()
        
        return items, total
    
    def get_multi_by_kol_id_with_count(
        self, db: Session, *, kol_id: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[FilterData], int]:
        """
        通过KOL ID获取多个筛选条件对象并返回总数
        
        返回:
            元组 (筛选条件列表, 总数)
        """
        # 准备查询
        query = db.query(self.model).join(
            FilterKolAssociation, 
            FilterKolAssociation.filter_id == self.model.id
        ).filter(
            FilterKolAssociation.kol_id == kol_id
        )
        
        # 获取总数
        total = query.with_entities(func.count()).scalar()
        
        # 获取数据
        items = query.offset(skip).limit(limit).all()
        
        return items, total
    
    # 异步方法
    async def async_get_by_name(self, db: AsyncSession, *, name: str) -> Optional[FilterData]:
        """异步通过筛选条件名称获取对象"""
        result = await db.execute(select(self.model).filter(self.model.filter_name == name))
        return result.scalars().first()
    
    async def async_get_multi_by_language(
        self, db: AsyncSession, *, language: str, skip: int = 0, limit: int = 100
    ) -> List[FilterData]:
        """异步通过语言获取多个筛选条件对象"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.language == language)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_multi_by_project_code(
        self, db: AsyncSession, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> List[FilterData]:
        """异步通过项目代码获取多个筛选条件对象"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.project_code == project_code)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_multi_by_kol_id(
        self, db: AsyncSession, *, kol_id: str, skip: int = 0, limit: int = 100
    ) -> List[FilterData]:
        """异步通过KOL ID获取多个筛选条件对象"""
        result = await db.execute(
            select(self.model)
            .join(FilterKolAssociation, FilterKolAssociation.filter_id == self.model.id)
            .filter(FilterKolAssociation.kol_id == kol_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_multi_with_count(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> Tuple[List[FilterData], int]:
        """
        异步获取多个筛选条件对象并返回总数
        
        返回:
            元组 (筛选条件列表, 总数)
        """
        # 获取总数
        total_result = await db.execute(select(func.count(self.model.id)))
        total = total_result.scalar()
        
        # 获取数据
        result = await db.execute(
            select(self.model)
            .offset(skip)
            .limit(limit)
        )
        items = result.scalars().all()
        
        return items, total
    
    async def async_get_multi_by_project_code_with_count(
        self, db: AsyncSession, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[FilterData], int]:
        """
        异步通过项目代码获取多个筛选条件对象并返回总数
        
        返回:
            元组 (筛选条件列表, 总数)
        """
        # 获取总数
        total_result = await db.execute(
            select(func.count(self.model.id))
            .filter(self.model.project_code == project_code)
        )
        total = total_result.scalar()
        
        # 获取数据
        result = await db.execute(
            select(self.model)
            .filter(self.model.project_code == project_code)
            .offset(skip)
            .limit(limit)
        )
        items = result.scalars().all()
        
        return items, total
    
    async def async_get_multi_by_kol_id_with_count(
        self, db: AsyncSession, *, kol_id: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[FilterData], int]:
        """
        异步通过KOL ID获取多个筛选条件对象并返回总数
        
        返回:
            元组 (筛选条件列表, 总数)
        """
        # 准备查询
        query = select(self.model).join(
            FilterKolAssociation, 
            FilterKolAssociation.filter_id == self.model.id
        ).filter(
            FilterKolAssociation.kol_id == kol_id
        )
        
        # 获取总数
        total_result = await db.execute(
            select(func.count())
            .select_from(query.subquery())
        )
        total = total_result.scalar()
        
        # 获取数据
        result = await db.execute(
            query
            .offset(skip)
            .limit(limit)
        )
        items = result.scalars().all()
        
        return items, total

    def get_all_names_and_project_codes(self, db: Session) -> List[Dict[str, str]]:
        """
        获取所有不同的筛选条件名称和项目代码
        
        返回:
            包含 filter_name 和 project_code 的字典列表
        """
        # 使用 distinct 查询不同的 filter_name 和 project_code 组合
        result = db.query(
            self.model.filter_name, 
            self.model.project_code
        ).distinct().all()
        
        # 将结果转换为字典列表
        return [
            {"filter_name": name, "project_code": code} 
            for name, code in result
        ]
    
    async def async_get_all_names_and_project_codes(self, db: AsyncSession) -> List[Dict[str, str]]:
        """
        异步获取所有不同的筛选条件名称和项目代码
        
        返回:
            包含 filter_name 和 project_code 的字典列表
        """
        # 使用 distinct 查询不同的 filter_name 和 project_code 组合
        result = await db.execute(
            select(
                self.model.filter_name, 
                self.model.project_code
            ).distinct()
        )
        
        # 将结果转换为字典列表
        return [
            {"filter_name": name, "project_code": code} 
            for name, code in result.all()
        ]

    def get_filter_names_and_project_codes_as_arrays(self, db: Session) -> Dict[str, List[str]]:
        """
        获取所有不同的筛选条件名称和项目代码，并分别以数组形式返回
        
        返回:
            包含 filter_names 和 project_codes 两个数组的字典
        """
        # 查询所有不同的 filter_name
        filter_names = db.query(self.model.filter_name).distinct().all()
        filter_names = [name[0] for name in filter_names if name[0] is not None]
        
        # 查询所有不同的 project_code
        project_codes = db.query(self.model.project_code).distinct().all()
        project_codes = [code[0] for code in project_codes if code[0] is not None]
        
        return {
            "filter_names": filter_names,
            "project_codes": project_codes
        }
    
    async def async_get_filter_names_and_project_codes_as_arrays(self, db: AsyncSession) -> Dict[str, List[str]]:
        """
        异步获取所有不同的筛选条件名称和项目代码，并分别以数组形式返回
        
        返回:
            包含 filter_names 和 project_codes 两个数组的字典
        """
        # 异步查询所有不同的 filter_name
        result_names = await db.execute(
            select(self.model.filter_name).distinct()
        )
        filter_names = [name[0] for name in result_names.all() if name[0] is not None]
        
        # 异步查询所有不同的 project_code
        result_codes = await db.execute(
            select(self.model.project_code).distinct()
        )
        project_codes = [code[0] for code in result_codes.all() if code[0] is not None]
        
        return {
            "filter_names": filter_names,
            "project_codes": project_codes
        }


filter_data = CRUDFilterData(FilterData) 