from typing import List, Optional, Tuple, Dict, Any

from sqlalchemy import select, func, or_, and_, cast, String, inspect, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.sql.expression import null

from app.crud.base import CRUDBase
from app.models.filter_kol_association import FilterKolAssociation
from app.models.kol_info import KOLInfo
from app.schemas.kol_info import KOLInfoCreate, KOLInfoUpdate, SearchOperator
from app.models.kol_tag_association import KolTagAssociation
from app.models.tag import Tag


class CRUDKOLInfo(CRUDBase[KOLInfo, KOLInfoCreate, KOLInfoUpdate]):
    """KOL信息的CRUD操作"""
    
    def get(self, db: Session, id: str) -> Optional[KOLInfo]:
        """通过KOL ID获取对象，这里覆盖基类方法来适应主键名称"""
        return db.query(self.model).options(joinedload(self.model.filter_datas), joinedload(self.model.tags)).filter(self.model.kol_id == id).first()
    
    def get_by_kol_name(self, db: Session, *, kol_name: str) -> Optional[KOLInfo]:
        """通过KOL名称获取对象"""
        return db.query(self.model).options(joinedload(self.model.filter_datas), joinedload(self.model.tags)).filter(self.model.kol_name == kol_name).first()
    
    def get_by_email(self, db: Session, *, email: str) -> Optional[KOLInfo]:
        """通过电子邮箱获取对象"""
        return db.query(self.model).options(joinedload(self.model.filter_datas), joinedload(self.model.tags)).filter(self.model.email == email).first()
    
    def count(self, db: Session) -> int:
        """获取KOL信息的总数量"""
        return db.query(func.count(self.model.kol_id)).scalar()
    
    def count_by_platform(self, db: Session, *, platform: str) -> int:
        """获取指定平台的KOL信息总数量"""
        return db.query(func.count(self.model.kol_id)).filter(
            self.model.platform == platform
        ).scalar()
    
    def count_by_filter_id(self, db: Session, *, filter_id: int) -> int:
        """获取指定筛选条件的KOL信息总数量"""
        return db.query(func.count(self.model.kol_id)).join(
            FilterKolAssociation, FilterKolAssociation.kol_id == self.model.kol_id
        ).filter(
            FilterKolAssociation.filter_id == filter_id
        ).scalar()
    
    def count_by_filter_name(self, db: Session, *, filter_name: str) -> int:
        """
        获取指定筛选条件名称的KOL信息总数量
        
        参数:
            db: 数据库会话
            filter_name: 筛选条件名称
            
        返回:
            满足条件的KOL总数
            
        性能说明:
            此方法首先通过filter_name查询到对应的filter_id，然后再计数
            如果filter_name对应多个筛选条件，会合并计数
        """
        from app.models.filter_data import FilterData
        
        # 先查询筛选条件获取ID列表
        filter_ids = db.query(FilterData.id).filter(
            FilterData.filter_name == filter_name
        ).all()
        
        if not filter_ids:
            return 0
            
        # 将查询结果转换为ID列表
        filter_ids = [id[0] for id in filter_ids]
        
        # 通过筛选条件ID列表查询关联的KOL数量
        return db.query(func.count(self.model.kol_id.distinct())).join(
            FilterKolAssociation, FilterKolAssociation.kol_id == self.model.kol_id
        ).filter(
            FilterKolAssociation.filter_id.in_(filter_ids)
        ).scalar()
    
    def count_by_project_code(self, db: Session, *, project_code: str) -> int:
        """获取指定项目代码的KOL信息总数量"""
        return db.query(func.count(self.model.kol_id)).join(
            FilterKolAssociation, FilterKolAssociation.kol_id == self.model.kol_id
        ).filter(
            FilterKolAssociation.project_code == project_code
        ).scalar()
    
    def get_with_pagination(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> Tuple[List[KOLInfo], int]:
        """获取分页的KOL信息列表"""
        total = self.count(db)
        items = db.query(self.model).options(joinedload(self.model.filter_datas), joinedload(self.model.tags)).offset(skip).limit(limit).all()
        return items, total
    
    def get_multi_by_platform(
        self, db: Session, *, platform: str, skip: int = 0, limit: int = 100
    ) -> List[KOLInfo]:
        """通过平台获取多个KOL信息对象"""
        return (
            db.query(self.model)
            .options(joinedload(self.model.filter_datas), joinedload(self.model.tags))
            .filter(self.model.platform == platform)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_platform_with_pagination(
        self, db: Session, *, platform: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[KOLInfo], int]:
        """通过平台获取分页的KOL信息列表"""
        total = self.count_by_platform(db, platform=platform)
        items = self.get_multi_by_platform(db, platform=platform, skip=skip, limit=limit)
        return items, total
    
    def get_multi_by_filter_id(
        self, db: Session, *, filter_id: int, skip: int = 0, limit: int = 100
    ) -> List[KOLInfo]:
        """通过筛选条件ID获取多个KOL信息对象"""
        return (
            db.query(self.model)
            .join(FilterKolAssociation, FilterKolAssociation.kol_id == self.model.kol_id)
            .filter(FilterKolAssociation.filter_id == filter_id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_filter_name(
        self, db: Session, *, filter_name: str, skip: int = 0, limit: int = 100
    ) -> List[KOLInfo]:
        """
        通过筛选条件名称获取多个KOL信息对象
        
        参数:
            db: 数据库会话
            filter_name: 筛选条件名称
            skip: 分页偏移量
            limit: 分页大小
            
        返回:
            KOL信息对象列表
            
        性能说明:
            此方法使用子查询避免多次查询数据库
            先查询符合filter_name的filter_id，然后查询关联的KOL
            使用distinct确保结果不重复
        """
        from app.models.filter_data import FilterData
        
        # 使用子查询获取筛选条件ID
        filter_subquery = db.query(FilterData.id).filter(
            FilterData.filter_name == filter_name
        ).subquery()
        
        # 先查询满足条件的去重KOL ID
        kol_id_subquery = (
            db.query(self.model.kol_id).distinct()
            .join(FilterKolAssociation, FilterKolAssociation.kol_id == self.model.kol_id)
            .filter(FilterKolAssociation.filter_id.in_(filter_subquery))
            .subquery()
        )
        
        # 然后用这些KOL ID查询完整的KOL信息
        return (
            db.query(self.model)
            .options(joinedload(self.model.filter_datas), joinedload(self.model.tags))
            .filter(self.model.kol_id.in_(kol_id_subquery))
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_filter_name_with_pagination(
        self, db: Session, *, filter_name: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[KOLInfo], int]:
        """
        通过筛选条件名称获取分页的KOL信息列表
        
        参数:
            db: 数据库会话
            filter_name: 筛选条件名称
            skip: 分页偏移量
            limit: 分页大小
            
        返回:
            包含KOL信息列表和总记录数的元组
            
        性能说明:
            此方法会分别调用计数和获取数据的方法
            可以通过缓存优化频繁查询的场景
        """
        total = self.count_by_filter_name(db, filter_name=filter_name)
        items = self.get_multi_by_filter_name(db, filter_name=filter_name, skip=skip, limit=limit)
        return items, total
    
    def get_multi_by_filter_id_with_pagination(
        self, db: Session, *, filter_id: int, skip: int = 0, limit: int = 100
    ) -> Tuple[List[KOLInfo], int]:
        """通过筛选条件ID获取分页的KOL信息列表"""
        total = self.count_by_filter_id(db, filter_id=filter_id)
        items = self.get_multi_by_filter_id(db, filter_id=filter_id, skip=skip, limit=limit)
        return items, total
    
    def get_multi_by_project_code(
        self, db: Session, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> List[KOLInfo]:
        """通过项目代码获取多个KOL信息对象"""
        return (
            db.query(self.model)
            .options(joinedload(self.model.filter_datas), joinedload(self.model.tags))
            .join(FilterKolAssociation, FilterKolAssociation.kol_id == self.model.kol_id)
            .filter(FilterKolAssociation.project_code == project_code)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_project_code_with_pagination(
        self, db: Session, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[KOLInfo], int]:
        """通过项目代码获取分页的KOL信息列表"""
        total = self.count_by_project_code(db, project_code=project_code)
        items = self.get_multi_by_project_code(db, project_code=project_code, skip=skip, limit=limit)
        return items, total
    
    def remove(self, db: Session, *, id: str) -> KOLInfo:
        """删除KOL信息对象，适配主键名称"""
        obj = db.query(self.model).filter(self.model.kol_id == id).first()
        if obj is None:
            raise ValueError(f"No KOL found with kol_id: {id}")
        
        # 使用直接的SQL删除来避免SQLAlchemy的ORM级联处理
        # 依赖数据库的外键约束级联删除关联表记录
        db.execute(
            text("DELETE FROM kol_info WHERE kol_id = :kol_id"),
            {"kol_id": id}
        )
        db.commit()
        return obj
    
    def remove_without_commit(self, db: Session, *, id: str) -> KOLInfo:
        """删除KOL信息对象但不提交事务，适配主键名称"""
        obj = db.query(self.model).filter(self.model.kol_id == id).first()
        if obj is None:
            raise ValueError(f"No KOL found with kol_id: {id}")
        
        # 使用直接的SQL删除来避免SQLAlchemy的ORM级联处理
        # 依赖数据库的外键约束级联删除关联表记录
        db.execute(
            text("DELETE FROM kol_info WHERE kol_id = :kol_id"),
            {"kol_id": id}
        )
        db.flush()  # 刷新以确保删除操作被执行，但不提交
        return obj
    
    # 异步方法
    async def async_get(self, db: AsyncSession, id: str) -> Optional[KOLInfo]:
        """异步通过KOL ID获取对象"""
        result = await db.execute(select(self.model).filter(self.model.kol_id == id))
        return result.scalars().first()
    
    async def async_get_by_kol_name(self, db: AsyncSession, *, kol_name: str) -> Optional[KOLInfo]:
        """异步通过KOL名称获取对象"""
        result = await db.execute(select(self.model).filter(self.model.kol_name == kol_name))
        return result.scalars().first()
    
    async def async_get_by_email(self, db: AsyncSession, *, email: str) -> Optional[KOLInfo]:
        """异步通过电子邮箱获取对象"""
        result = await db.execute(select(self.model).filter(self.model.email == email))
        return result.scalars().first()
    
    async def async_get_multi_by_platform(
        self, db: AsyncSession, *, platform: str, skip: int = 0, limit: int = 100
    ) -> List[KOLInfo]:
        """异步通过平台获取多个KOL信息对象"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.platform == platform)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_multi_by_filter_id(
        self, db: AsyncSession, *, filter_id: int, skip: int = 0, limit: int = 100
    ) -> List[KOLInfo]:
        """异步通过筛选条件ID获取多个KOL信息对象"""
        result = await db.execute(
            select(self.model)
            .join(FilterKolAssociation, FilterKolAssociation.kol_id == self.model.kol_id)
            .filter(FilterKolAssociation.filter_id == filter_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_multi_by_filter_name(
        self, db: AsyncSession, *, filter_name: str, skip: int = 0, limit: int = 100
    ) -> List[KOLInfo]:
        """
        异步通过筛选条件名称获取多个KOL信息对象
        
        参数:
            db: 异步数据库会话
            filter_name: 筛选条件名称
            skip: 分页偏移量
            limit: 分页大小
            
        返回:
            KOL信息对象列表
            
        性能说明:
            此异步方法使用子查询优化性能
            使用distinct()确保结果不重复
        """
        from app.models.filter_data import FilterData
        
        # 使用异步子查询获取筛选条件ID
        filter_subquery = select(FilterData.id).where(
            FilterData.filter_name == filter_name
        ).scalar_subquery()
        
        # 先查询满足条件的去重KOL ID
        kol_id_query = select(self.model.kol_id).distinct()\
            .join(FilterKolAssociation, FilterKolAssociation.kol_id == self.model.kol_id)\
            .where(FilterKolAssociation.filter_id.in_(filter_subquery))
        
        kol_id_result = await db.execute(kol_id_query)
        kol_ids = [row[0] for row in kol_id_result.all()]
        
        if not kol_ids:
            return []
        
        # 然后用这些KOL ID查询完整的KOL信息
        result = await db.execute(
            select(self.model)
            .where(self.model.kol_id.in_(kol_ids))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_multi_by_project_code(
        self, db: AsyncSession, *, project_code: str, skip: int = 0, limit: int = 100
    ) -> List[KOLInfo]:
        """异步通过项目代码获取多个KOL信息对象"""
        result = await db.execute(
            select(self.model)
            .join(FilterKolAssociation, FilterKolAssociation.kol_id == self.model.kol_id)
            .filter(FilterKolAssociation.project_code == project_code)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_remove(self, db: AsyncSession, *, id: str) -> KOLInfo:
        """异步删除KOL信息对象"""
        obj = await self.async_get(db, id=id)
        await db.delete(obj)
        await db.commit()
        return obj

    def apply_search_conditions(self, query, conditions: List[Dict], conjunction: str = "and") -> Any:
        """
        应用高级搜索条件到查询对象，支持 filter_name 复杂查询
        """
        from sqlalchemy.dialects.postgresql import JSONB
        from sqlalchemy import and_, or_, exists
        from app.models.filter_kol_association import FilterKolAssociation
        from app.models.filter_data import FilterData
        from app.models.kol_tag_association import KolTagAssociation
        from app.models.tag import Tag
        model_columns = {c.name: c for c in self.model.__table__.columns}
        expressions = []
        join_filter_name = False
        filter_name_exprs = []
        join_tags = False
        tag_exprs = []
        
        for condition in conditions:
            field = condition.get("field")
            operator = condition.get("operator")
            value = condition.get("value")
            
            # 处理标签筛选
            if field == "tag_names" or field == "tags":
                expr = None
                if operator == SearchOperator.IS_NULL:
                    # 对于标签的情况，is_null表示没有任何标签
                    # 这需要通过子查询实现，不需要JOIN
                    has_tags_subquery = exists().where(
                        KolTagAssociation.kol_id == self.model.kol_id
                    )
                    expr = ~has_tags_subquery
                    # IS_NULL 和 IS_NOT_NULL 操作直接添加到 expressions，不需要 JOIN
                    expressions.append(expr)
                elif operator == SearchOperator.IS_NOT_NULL:
                    # is_not_null表示至少有一个标签
                    has_tags_subquery = exists().where(
                        KolTagAssociation.kol_id == self.model.kol_id
                    )
                    expr = has_tags_subquery
                    # IS_NULL 和 IS_NOT_NULL 操作直接添加到 expressions，不需要 JOIN
                    expressions.append(expr)
                else:
                    # 其他操作需要 JOIN 到 tag 表
                    join_tags = True
                    column = Tag.name
                    if operator == SearchOperator.EQUAL:
                        expr = (column == value)
                    elif operator == SearchOperator.NOT_EQUAL:
                        expr = (column != value)
                    elif operator == SearchOperator.CONTAINS:
                        expr = column.ilike(f"%{value}%")
                    elif operator == SearchOperator.NOT_CONTAINS:
                        expr = ~column.ilike(f"%{value}%")
                    
                    if expr is not None:
                        tag_exprs.append(expr)
                continue
            
            if field == "filter_name":
                # 优化：使用与 advanced_search 一致的逻辑处理 filter_name
                expr = None
                if operator == SearchOperator.IS_NOT_NULL:
                    # filter_name IS_NOT_NULL: 查找有关联 filter 的 KOL
                    has_filter_subquery = exists().where(
                        and_(
                            FilterKolAssociation.kol_id == self.model.kol_id,
                            FilterKolAssociation.filter_id == FilterData.id,
                            FilterData.filter_name.isnot(None)
                        )
                    )
                    expr = has_filter_subquery
                elif operator == SearchOperator.IS_NULL:
                    # filter_name IS_NULL: 查找没有关联 filter 的 KOL
                    has_filter_subquery = exists().where(
                        FilterKolAssociation.kol_id == self.model.kol_id
                    )
                    expr = ~has_filter_subquery
                else:
                    # 其他操作符需要 JOIN 到 filter_data 表
                    join_filter_name = True
                    column = FilterData.filter_name
                    if operator == SearchOperator.EQUAL:
                        expr = (column == value)
                    elif operator == SearchOperator.NOT_EQUAL:
                        expr = (column != value)
                    elif operator == SearchOperator.CONTAINS:
                        expr = column.ilike(f"%{value}%")
                    elif operator == SearchOperator.NOT_CONTAINS:
                        expr = ~column.ilike(f"%{value}%")
                
                if expr is not None:
                    if operator in [SearchOperator.IS_NULL, SearchOperator.IS_NOT_NULL]:
                        # IS_NULL 和 IS_NOT_NULL 直接添加到 expressions
                        expressions.append(expr)
                    else:
                        # 其他操作符添加到 filter_name_exprs，需要 JOIN
                        filter_name_exprs.append(expr)
                continue
                
            if field not in model_columns and field != "kol_id":
                continue
            column = getattr(self.model, field)
            expr = None
            if operator == SearchOperator.EQUAL:
                # 对于时间字段的等于操作，特殊处理精确到天的逻辑
                if field in ['created_at', 'updated_at'] and isinstance(value, str):
                    from datetime import datetime, timedelta
                    try:
                        # 解析日期字符串
                        if 'T' in value:
                            date_value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        else:
                            date_value = datetime.strptime(value, '%Y-%m-%d')
                        # 等于指定日期：当日00:00:00到次日00:00:00之间
                        start_of_day = date_value.replace(hour=0, minute=0, second=0, microsecond=0)
                        end_of_day = start_of_day + timedelta(days=1)
                        expr = and_(column >= start_of_day, column < end_of_day)
                    except (ValueError, TypeError):
                        expr = (column == value)
                else:
                    expr = (column == value)
            elif operator == SearchOperator.NOT_EQUAL:
                expr = (column != value)
            elif operator == SearchOperator.CONTAINS:
                if isinstance(model_columns[field].type, String):
                    expr = column.ilike(f"%{value}%")
                elif hasattr(model_columns[field].type, "python_type") and (
                    model_columns[field].type.python_type == list or 
                    model_columns[field].type.python_type == dict
                ):
                    if isinstance(value, list):
                        expr = cast(column, JSONB).contains(value)
                    else:
                        expr = cast(column, String).ilike(f"%{value}%")
                else:
                    expr = cast(column, String).ilike(f"%{value}%")
            elif operator == SearchOperator.NOT_CONTAINS:
                if isinstance(model_columns[field].type, String):
                    expr = ~column.ilike(f"%{value}%")
                elif hasattr(model_columns[field].type, "python_type") and (
                    model_columns[field].type.python_type == list or 
                    model_columns[field].type.python_type == dict
                ):
                    if isinstance(value, list):
                        expr = ~cast(column, JSONB).contains(value)
                    else:
                        expr = ~cast(column, String).ilike(f"%{value}%")
                else:
                    expr = ~cast(column, String).ilike(f"%{value}%")
            elif operator == SearchOperator.IS_NULL:
                expr = column.is_(None)
            elif operator == SearchOperator.IS_NOT_NULL:
                expr = column.isnot(None)
            # 添加数值比较操作符支持
            elif operator == SearchOperator.GREATER_THAN:
                # 对于时间字段，特殊处理精确到天的逻辑
                if field in ['created_at', 'updated_at'] and isinstance(value, str):
                    from datetime import datetime, timedelta
                    try:
                        # 解析日期字符串，支持多种格式
                        if 'T' in value:
                            date_value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        else:
                            date_value = datetime.strptime(value, '%Y-%m-%d')
                        # 大于指定日期：从次日00:00:00开始
                        next_day = date_value.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                        expr = column >= next_day
                    except (ValueError, TypeError):
                        expr = column > value
                else:
                    expr = column > value
            elif operator == SearchOperator.LESS_THAN:
                # 对于时间字段，特殊处理精确到天的逻辑
                if field in ['created_at', 'updated_at'] and isinstance(value, str):
                    from datetime import datetime
                    try:
                        # 解析日期字符串
                        if 'T' in value:
                            date_value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        else:
                            date_value = datetime.strptime(value, '%Y-%m-%d')
                        # 小于指定日期：到当日00:00:00为止
                        start_of_day = date_value.replace(hour=0, minute=0, second=0, microsecond=0)
                        expr = column < start_of_day
                    except (ValueError, TypeError):
                        expr = column < value
                else:
                    expr = column < value
            elif operator == SearchOperator.GREATER_EQUAL:
                # 对于时间字段，特殊处理精确到天的逻辑
                if field in ['created_at', 'updated_at'] and isinstance(value, str):
                    from datetime import datetime
                    try:
                        # 解析日期字符串
                        if 'T' in value:
                            date_value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        else:
                            date_value = datetime.strptime(value, '%Y-%m-%d')
                        # 大于等于指定日期：从当日00:00:00开始
                        start_of_day = date_value.replace(hour=0, minute=0, second=0, microsecond=0)
                        expr = column >= start_of_day
                    except (ValueError, TypeError):
                        expr = column >= value
                else:
                    expr = column >= value
            elif operator == SearchOperator.LESS_EQUAL:
                # 对于时间字段，特殊处理精确到天的逻辑
                if field in ['created_at', 'updated_at'] and isinstance(value, str):
                    from datetime import datetime, timedelta
                    try:
                        # 解析日期字符串
                        if 'T' in value:
                            date_value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        else:
                            date_value = datetime.strptime(value, '%Y-%m-%d')
                        # 小于等于指定日期：到次日00:00:00为止（不包含）
                        end_of_day = date_value.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                        expr = column < end_of_day
                    except (ValueError, TypeError):
                        expr = column <= value
                else:
                    expr = column <= value

            if expr is not None:
                expressions.append(expr)
                
        # 应用标签筛选
        if join_tags:
            query = query.join(KolTagAssociation, self.model.kol_id == KolTagAssociation.kol_id)
            query = query.join(Tag, KolTagAssociation.tag_id == Tag.id)
            if tag_exprs:
                if conjunction == "or":
                    query = query.filter(or_(*tag_exprs))
                else:
                    query = query.filter(and_(*tag_exprs))
                    
        # 应用筛选条件筛选
        if join_filter_name:
            query = query.join(FilterKolAssociation, self.model.kol_id == FilterKolAssociation.kol_id)
            query = query.join(FilterData, FilterKolAssociation.filter_id == FilterData.id)
            if filter_name_exprs:
                if conjunction == "or":
                    query = query.filter(or_(*filter_name_exprs))
                else:
                    query = query.filter(and_(*filter_name_exprs))
                    
        # 应用其他条件
        if expressions:
            if conjunction == "or":
                query = query.filter(or_(*expressions))
            else:
                query = query.filter(and_(*expressions))
                
        return query

    def advanced_search(
        self, 
        db: Session, 
        *, 
        conditions: List[Dict],
        platform: Optional[str] = None,
        filter_name: Optional[str] = None,
        project_code: Optional[str] = None,
        skip: int = 0, 
        limit: int = 100,
        conjunction: str = "and"
    ) -> Tuple[List[KOLInfo], int]:
        """
        KOL信息高级搜索
        
        优化说明：
        1. 修复了 filter_name 字段的处理逻辑
        2. 对于 filter_name IS_NOT_NULL，正确理解为查找有关联 filter 的 KOL
        3. 对于 filter_name IS_NULL，正确理解为查找没有关联 filter 的 KOL
        4. 改进了查询逻辑的清晰度和性能
        """
        from app.models.filter_kol_association import FilterKolAssociation
        from app.models.filter_data import FilterData
        from sqlalchemy import and_, or_, exists
        
        # 分离 filter_name 条件和其他条件
        filter_name_conditions = [c for c in conditions if c.get("field") == "filter_name"]
        other_conditions = [c for c in conditions if c.get("field") != "filter_name"]
        
        # 处理 filter_name 相关条件
        if filter_name_conditions:
            # 构建基础查询
            query = db.query(self.model)
            
            # 处理每个 filter_name 条件
            filter_name_exprs = []
            for condition in filter_name_conditions:
                operator = condition.get("operator")
                value = condition.get("value")
                
                if operator == SearchOperator.IS_NOT_NULL:
                    # filter_name IS_NOT_NULL: 查找有关联 filter 的 KOL
                    # 使用 EXISTS 子查询检查是否存在关联的 filter_data 记录
                    has_filter_subquery = exists().where(
                        and_(
                            FilterKolAssociation.kol_id == self.model.kol_id,
                            FilterKolAssociation.filter_id == FilterData.id,
                            FilterData.filter_name.isnot(None)
                        )
                    )
                    filter_name_exprs.append(has_filter_subquery)
                    
                elif operator == SearchOperator.IS_NULL:
                    # filter_name IS_NULL: 查找没有关联 filter 的 KOL
                    # 使用 NOT EXISTS 子查询
                    has_filter_subquery = exists().where(
                        FilterKolAssociation.kol_id == self.model.kol_id
                    )
                    filter_name_exprs.append(~has_filter_subquery)
                    
                else:
                    # 其他操作符：需要 JOIN 到 filter_data 表进行条件过滤
                    # 这种情况下，我们需要使用子查询来获取满足条件的 KOL ID
                    filter_data_column = FilterData.filter_name
                    filter_expr = None
                    
                    if operator == SearchOperator.EQUAL:
                        filter_expr = (filter_data_column == value)
                    elif operator == SearchOperator.NOT_EQUAL:
                        filter_expr = (filter_data_column != value)
                    elif operator == SearchOperator.CONTAINS:
                        filter_expr = filter_data_column.ilike(f"%{value}%")
                    elif operator == SearchOperator.NOT_CONTAINS:
                        filter_expr = ~filter_data_column.ilike(f"%{value}%")
                    
                    if filter_expr is not None:
                        # 使用子查询获取满足条件的 KOL ID
                        matching_kol_ids_subquery = (
                            db.query(FilterKolAssociation.kol_id)
                            .join(FilterData, FilterKolAssociation.filter_id == FilterData.id)
                            .filter(filter_expr)
                            .subquery()
                        )
                        filter_name_exprs.append(
                            self.model.kol_id.in_(
                                db.query(matching_kol_ids_subquery.c.kol_id)
                            )
                        )
            
            # 应用 filter_name 条件
            if filter_name_exprs:
                if conjunction == "or":
                    query = query.filter(or_(*filter_name_exprs))
                else:
                    query = query.filter(and_(*filter_name_exprs))
            
            # 应用平台过滤
            if platform:
                try:
                    query = query.filter(self.model.platform == platform)
                except (ValueError, TypeError):
                    pass
            
            # 应用项目代码过滤
            if project_code:
                # 对于有 filter_name 条件的查询，project_code 需要通过关联表过滤
                project_kol_ids_subquery = (
                    db.query(FilterKolAssociation.kol_id)
                    .filter(FilterKolAssociation.project_code == project_code)
                    .subquery()
                )
                query = query.filter(
                    self.model.kol_id.in_(
                        db.query(project_kol_ids_subquery.c.kol_id)
                    )
                )
            
            # 应用其他条件
            if other_conditions:
                query = self.apply_search_conditions(query, other_conditions, conjunction=conjunction)
            
            # 执行查询
            count_query = query.with_entities(func.count(self.model.kol_id.distinct()))
            total = count_query.scalar()
            items = query.distinct(self.model.kol_id).offset(skip).limit(limit).all()
            
            return items, total
        # 原有逻辑（无 filter_name 复杂条件）
        query = db.query(self.model)
        if platform:
            try:
                query = query.filter(self.model.platform == platform)
            except (ValueError, TypeError):
                pass
        
        if project_code:
            from app.models.filter_kol_association import FilterKolAssociation
            kol_id_subquery = (
                db.query(FilterKolAssociation.kol_id).distinct()
                .filter(FilterKolAssociation.project_code == project_code)
                .subquery()
            )
            query = query.filter(self.model.kol_id.in_(kol_id_subquery))
        if conditions:
            query = self.apply_search_conditions(query, conditions, conjunction=conjunction)
        count_query = query.with_entities(func.count(self.model.kol_id.distinct()))
        total = count_query.scalar()
        items = query.distinct(self.model.kol_id).offset(skip).limit(limit).all()
        return items, total
    
    # 异步高级搜索方法
    async def async_apply_search_conditions(self, query, conditions: List[Dict], conjunction: str = "and") -> Any:
        """
        应用搜索条件到查询（异步版本）
        """
        from app.models.filter_kol_association import FilterKolAssociation
        from app.models.filter_data import FilterData
        from app.models.kol_tag_association import KolTagAssociation
        from app.models.tag import Tag
        from sqlalchemy import and_, or_, cast, String, exists, select
        from sqlalchemy.dialects.postgresql import JSONB
        from app.schemas.kol_info import SearchOperator
        
        model_columns = {c.name: c for c in inspect(self.model).columns}
        expressions = []
        join_filter_name = False
        filter_name_exprs = []
        join_tags = False
        tag_exprs = []
        
        for condition in conditions:
            field = condition.get("field")
            operator = condition.get("operator")
            value = condition.get("value")
            
            # 处理标签筛选
            if field == "tag_names" or field == "tags":
                expr = None
                if operator == SearchOperator.IS_NULL:
                    # 对于标签的情况，is_null表示没有任何标签
                    # 这需要通过子查询实现，不需要JOIN
                    has_tags_subquery = exists().where(
                        KolTagAssociation.kol_id == self.model.kol_id
                    )
                    expr = ~has_tags_subquery
                    # IS_NULL 和 IS_NOT_NULL 操作直接添加到 expressions，不需要 JOIN
                    expressions.append(expr)
                elif operator == SearchOperator.IS_NOT_NULL:
                    # is_not_null表示至少有一个标签
                    has_tags_subquery = exists().where(
                        KolTagAssociation.kol_id == self.model.kol_id
                    )
                    expr = has_tags_subquery
                    # IS_NULL 和 IS_NOT_NULL 操作直接添加到 expressions，不需要 JOIN
                    expressions.append(expr)
                else:
                    # 其他操作需要 JOIN 到 tag 表
                    join_tags = True
                    column = Tag.name
                    if operator == SearchOperator.EQUAL:
                        expr = (column == value)
                    elif operator == SearchOperator.NOT_EQUAL:
                        expr = (column != value)
                    elif operator == SearchOperator.CONTAINS:
                        expr = column.ilike(f"%{value}%")
                    elif operator == SearchOperator.NOT_CONTAINS:
                        expr = ~column.ilike(f"%{value}%")
                    
                    if expr is not None:
                        tag_exprs.append(expr)
                continue
            
            if field == "filter_name":
                # 优化：使用与 advanced_search 一致的逻辑处理 filter_name
                expr = None
                if operator == SearchOperator.IS_NOT_NULL:
                    # filter_name IS_NOT_NULL: 查找有关联 filter 的 KOL
                    has_filter_subquery = exists().where(
                        and_(
                            FilterKolAssociation.kol_id == self.model.kol_id,
                            FilterKolAssociation.filter_id == FilterData.id,
                            FilterData.filter_name.isnot(None)
                        )
                    )
                    expr = has_filter_subquery
                elif operator == SearchOperator.IS_NULL:
                    # filter_name IS_NULL: 查找没有关联 filter 的 KOL
                    has_filter_subquery = exists().where(
                        FilterKolAssociation.kol_id == self.model.kol_id
                    )
                    expr = ~has_filter_subquery
                else:
                    # 其他操作符需要 JOIN 到 filter_data 表
                    join_filter_name = True
                    column = FilterData.filter_name
                    if operator == SearchOperator.EQUAL:
                        expr = (column == value)
                    elif operator == SearchOperator.NOT_EQUAL:
                        expr = (column != value)
                    elif operator == SearchOperator.CONTAINS:
                        expr = column.ilike(f"%{value}%")
                    elif operator == SearchOperator.NOT_CONTAINS:
                        expr = ~column.ilike(f"%{value}%")
                
                if expr is not None:
                    if operator in [SearchOperator.IS_NULL, SearchOperator.IS_NOT_NULL]:
                        # IS_NULL 和 IS_NOT_NULL 直接添加到 expressions
                        expressions.append(expr)
                    else:
                        # 其他操作符添加到 filter_name_exprs，需要 JOIN
                        filter_name_exprs.append(expr)
                continue
            
            if not hasattr(self.model, field):
                continue
                
            column = getattr(self.model, field)
            expr = None
            
            if operator == SearchOperator.EQUAL:
                expr = (column == value)
            elif operator == SearchOperator.NOT_EQUAL:
                expr = (column != value)
            elif operator == SearchOperator.CONTAINS:
                if isinstance(model_columns[field].type, String):
                    expr = column.ilike(f"%{value}%")
                elif hasattr(model_columns[field].type, "python_type") and (
                    model_columns[field].type.python_type == list or 
                    model_columns[field].type.python_type == dict
                ):
                    if isinstance(value, list):
                        expr = cast(column, JSONB).contains(value)
                    else:
                        expr = cast(column, String).ilike(f"%{value}%")
                else:
                    expr = cast(column, String).ilike(f"%{value}%")
            elif operator == SearchOperator.NOT_CONTAINS:
                if isinstance(model_columns[field].type, String):
                    expr = ~column.ilike(f"%{value}%")
                elif hasattr(model_columns[field].type, "python_type") and (
                    model_columns[field].type.python_type == list or 
                    model_columns[field].type.python_type == dict
                ):
                    if isinstance(value, list):
                        expr = ~cast(column, JSONB).contains(value)
                    else:
                        expr = ~cast(column, String).ilike(f"%{value}%")
                else:
                    expr = ~cast(column, String).ilike(f"%{value}%")
            elif operator == SearchOperator.IS_NULL:
                expr = column.is_(None)
            elif operator == SearchOperator.IS_NOT_NULL:
                expr = column.isnot(None)
            # 添加数值比较操作符支持
            elif operator == SearchOperator.GREATER_THAN:
                expr = column > value
            elif operator == SearchOperator.LESS_THAN:
                expr = column < value
            elif operator == SearchOperator.GREATER_EQUAL:
                expr = column >= value
            elif operator == SearchOperator.LESS_EQUAL:
                expr = column <= value
                
            if expr is not None:
                expressions.append(expr)
        
        # 应用标签筛选
        if join_tags:
            query = query.join(KolTagAssociation, self.model.kol_id == KolTagAssociation.kol_id)
            query = query.join(Tag, KolTagAssociation.tag_id == Tag.id)
            if tag_exprs:
                if conjunction == "or":
                    query = query.where(or_(*tag_exprs))
                else:
                    query = query.where(and_(*tag_exprs))
                
        # 应用筛选条件筛选
        if join_filter_name:
            query = query.join(FilterKolAssociation, self.model.kol_id == FilterKolAssociation.kol_id)
            query = query.join(FilterData, FilterKolAssociation.filter_id == FilterData.id)
            for expr in filter_name_exprs:
                query = query.where(expr)
                
        # 应用其他条件
        if expressions:
            if conjunction == "or":
                query = query.where(or_(*expressions))
            else:
                query = query.where(and_(*expressions))
        
        return query
        
    async def async_advanced_search(
        self, 
        db: AsyncSession, 
        *, 
        conditions: List[Dict],
        platform: Optional[str] = None,
        project_code: Optional[str] = None,
        skip: int = 0, 
        limit: int = 100,
        conjunction: str = "and"
    ) -> Tuple[List[KOLInfo], int]:
        """
        异步KOL信息高级搜索
        """
        # 导入必要的SQLAlchemy组件
        from sqlalchemy import select, func, and_, or_
        
        # 判断是否有 filter_name 相关条件
        has_filter_name = any(c.get("field") == "filter_name" for c in conditions)
        if has_filter_name:
            from app.models.filter_kol_association import FilterKolAssociation
            from app.models.filter_data import FilterData
            # 构造 filter_name 相关表达式
            filter_name_exprs = []
            for condition in conditions:
                if condition.get("field") != "filter_name":
                    continue
                operator = condition.get("operator")
                value = condition.get("value")
                column = FilterData.filter_name
                expr = None
                if operator == SearchOperator.EQUAL:
                    expr = (column == value)
                elif operator == SearchOperator.NOT_EQUAL:
                    expr = (column != value)
                elif operator == SearchOperator.CONTAINS:
                    expr = column.ilike(f"%{value}%")
                elif operator == SearchOperator.NOT_CONTAINS:
                    expr = ~column.ilike(f"%{value}%")
                elif operator == SearchOperator.IS_NULL:
                    expr = column.is_(None)
                elif operator == SearchOperator.IS_NOT_NULL:
                    expr = column.isnot(None)
                if expr is not None:
                    filter_name_exprs.append(expr)
            # 组合表达式
            if conjunction == "or":
                filter_name_filter = or_(*filter_name_exprs)
            else:
                filter_name_filter = and_(*filter_name_exprs)
            # 先查唯一 KOL ID
            kol_id_query = select(FilterKolAssociation.kol_id).join(FilterData, FilterKolAssociation.filter_id == FilterData.id).where(filter_name_filter)
            # 额外条件：platform/project_code/其他字段
            if platform:
                try:
                    kol_id_query = kol_id_query.join(KOLInfo, FilterKolAssociation.kol_id == KOLInfo.kol_id).where(KOLInfo.platform == platform)
                except (ValueError, TypeError):
                    pass
            if project_code:
                kol_id_query = kol_id_query.where(FilterKolAssociation.project_code == project_code)
            # 其他非 filter_name 条件
            other_conditions = [c for c in conditions if c.get("field") != "filter_name"]
            if other_conditions:
                kol_id_query = kol_id_query.join(KOLInfo, FilterKolAssociation.kol_id == KOLInfo.kol_id)
                for cond in other_conditions:
                    field = cond.get("field")
                    operator = cond.get("operator")
                    value = cond.get("value")
                    if not hasattr(KOLInfo, field):
                        continue
                    column = getattr(KOLInfo, field)
                    expr = None
                    if operator == SearchOperator.EQUAL:
                        # 对于时间字段的等于操作，特殊处理精确到天的逻辑
                        if field in ['created_at', 'updated_at'] and isinstance(value, str):
                            from datetime import datetime, timedelta
                            try:
                                # 解析日期字符串
                                if 'T' in value:
                                    date_value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                                else:
                                    date_value = datetime.strptime(value, '%Y-%m-%d')
                                # 等于指定日期：当日00:00:00到次日00:00:00之间
                                start_of_day = date_value.replace(hour=0, minute=0, second=0, microsecond=0)
                                end_of_day = start_of_day + timedelta(days=1)
                                expr = and_(column >= start_of_day, column < end_of_day)
                            except (ValueError, TypeError):
                                expr = (column == value)
                        else:
                            expr = (column == value)
                    elif operator == SearchOperator.NOT_EQUAL:
                        expr = (column != value)
                    elif operator == SearchOperator.CONTAINS:
                        expr = column.ilike(f"%{value}%")
                    elif operator == SearchOperator.NOT_CONTAINS:
                        expr = ~column.ilike(f"%{value}%")
                    elif operator == SearchOperator.IS_NULL:
                        expr = column.is_(None)
                    elif operator == SearchOperator.IS_NOT_NULL:
                        expr = column.isnot(None)
                    # 添加数值比较操作符支持
                    elif operator == SearchOperator.GREATER_THAN:
                        # 对于时间字段，特殊处理精确到天的逻辑
                        if field in ['created_at', 'updated_at'] and isinstance(value, str):
                            from datetime import datetime, timedelta
                            try:
                                # 解析日期字符串，支持多种格式
                                if 'T' in value:
                                    date_value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                                else:
                                    date_value = datetime.strptime(value, '%Y-%m-%d')
                                # 大于指定日期：从次日00:00:00开始
                                next_day = date_value.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                                expr = column >= next_day
                            except (ValueError, TypeError):
                                expr = column > value
                        else:
                            expr = column > value
                    elif operator == SearchOperator.LESS_THAN:
                        # 对于时间字段，特殊处理精确到天的逻辑
                        if field in ['created_at', 'updated_at'] and isinstance(value, str):
                            from datetime import datetime
                            try:
                                # 解析日期字符串
                                if 'T' in value:
                                    date_value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                                else:
                                    date_value = datetime.strptime(value, '%Y-%m-%d')
                                # 小于指定日期：到当日00:00:00为止
                                start_of_day = date_value.replace(hour=0, minute=0, second=0, microsecond=0)
                                expr = column < start_of_day
                            except (ValueError, TypeError):
                                expr = column < value
                        else:
                            expr = column < value
                    elif operator == SearchOperator.GREATER_EQUAL:
                        # 对于时间字段，特殊处理精确到天的逻辑
                        if field in ['created_at', 'updated_at'] and isinstance(value, str):
                            from datetime import datetime
                            try:
                                # 解析日期字符串
                                if 'T' in value:
                                    date_value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                                else:
                                    date_value = datetime.strptime(value, '%Y-%m-%d')
                                # 大于等于指定日期：从当日00:00:00开始
                                start_of_day = date_value.replace(hour=0, minute=0, second=0, microsecond=0)
                                expr = column >= start_of_day
                            except (ValueError, TypeError):
                                expr = column >= value
                        else:
                            expr = column >= value
                    elif operator == SearchOperator.LESS_EQUAL:
                        # 对于时间字段，特殊处理精确到天的逻辑
                        if field in ['created_at', 'updated_at'] and isinstance(value, str):
                            from datetime import datetime, timedelta
                            try:
                                # 解析日期字符串
                                if 'T' in value:
                                    date_value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                                else:
                                    date_value = datetime.strptime(value, '%Y-%m-%d')
                                # 小于等于指定日期：到次日00:00:00为止（不包含）
                                end_of_day = date_value.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                                expr = column < end_of_day
                            except (ValueError, TypeError):
                                expr = column <= value
                        else:
                            expr = column <= value
                    if expr is not None:
                        kol_id_query = kol_id_query.where(expr)
            kol_id_query = kol_id_query.distinct()
            result = await db.execute(kol_id_query.offset(skip).limit(limit))
            kol_ids = [row[0] for row in result.all()]
            # 计数
            count_query = select(func.count(FilterKolAssociation.kol_id.distinct())).select_from(FilterKolAssociation).join(FilterData, FilterKolAssociation.filter_id == FilterData.id).where(filter_name_filter)
            if platform:
                try:
                    count_query = count_query.join(KOLInfo, FilterKolAssociation.kol_id == KOLInfo.kol_id).where(KOLInfo.platform == platform)
                except (ValueError, TypeError):
                    pass
            if project_code:
                count_query = count_query.where(FilterKolAssociation.project_code == project_code)
            if other_conditions:
                count_query = count_query.join(KOLInfo, FilterKolAssociation.kol_id == KOLInfo.kol_id)
                for cond in other_conditions:
                    field = cond.get("field")
                    operator = cond.get("operator")
                    value = cond.get("value")
                    if not hasattr(KOLInfo, field):
                        continue
                    column = getattr(KOLInfo, field)
                    expr = None
                    if operator == SearchOperator.EQUAL:
                        expr = (column == value)
                    elif operator == SearchOperator.NOT_EQUAL:
                        expr = (column != value)
                    elif operator == SearchOperator.CONTAINS:
                        expr = column.ilike(f"%{value}%")
                    elif operator == SearchOperator.NOT_CONTAINS:
                        expr = ~column.ilike(f"%{value}%")
                    elif operator == SearchOperator.IS_NULL:
                        expr = column.is_(None)
                    elif operator == SearchOperator.IS_NOT_NULL:
                        expr = column.isnot(None)
                    if expr is not None:
                        count_query = count_query.where(expr)
            count_result = await db.execute(count_query)
            total = count_result.scalar()
            if not kol_ids:
                return [], 0
            # 再查详情
            items_query = select(self.model).options(
                selectinload(self.model.filter_datas),
                selectinload(self.model.tags)
            ).where(self.model.kol_id.in_(kol_ids))
            items_result = await db.execute(items_query)
            items = items_result.scalars().all()
            # 保证顺序与分页一致
            items_dict = {item.kol_id: item for item in items}
            items_ordered = [items_dict[kid] for kid in kol_ids if kid in items_dict]
            return items_ordered, total
        # 原有逻辑（无 filter_name 复杂条件）
        query = select(self.model).options(
            selectinload(self.model.filter_datas),
            selectinload(self.model.tags)
        )
        if platform:
            try:
                query = query.where(self.model.platform == platform)
            except (ValueError, TypeError):
                pass
        
        if project_code:
            from app.models.filter_kol_association import FilterKolAssociation
            kol_id_query = select(FilterKolAssociation.kol_id).distinct().where(
                FilterKolAssociation.project_code == project_code
            )
            kol_id_result = await db.execute(kol_id_query)
            kol_ids = [row[0] for row in kol_id_result.all()]
            if not kol_ids:
                return [], 0
            query = query.where(self.model.kol_id.in_(kol_ids))
        if conditions:
            query = await self.async_apply_search_conditions(query, conditions, conjunction=conjunction)
        count_query = select(func.count(self.model.kol_id.distinct())).select_from(self.model)
        if hasattr(query, '_where_criteria') and query._where_criteria:
            for criterion in query._where_criteria:
                count_query = count_query.where(criterion)
        count_result = await db.execute(count_query)
        total = count_result.scalar()
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        items = result.scalars().all()
        return items, total
    



kol_info = CRUDKOLInfo(KOLInfo)