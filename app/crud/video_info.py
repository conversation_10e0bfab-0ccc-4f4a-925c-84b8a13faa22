from typing import List, Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.video_info import VideoInfo
from app.schemas.video_info import VideoInfoCreate, VideoInfoUpdate


class CRUDVideoInfo(CRUDBase[VideoInfo, VideoInfoCreate, VideoInfoUpdate]):
    """视频信息的CRUD操作"""
    
    def get(self, db: Session, id: str) -> Optional[VideoInfo]:
        """通过视频ID获取对象，这里覆盖基类方法来适应主键名称"""
        return db.query(self.model).filter(self.model.video_id == id).first()
    
    def get_multi_by_kol_id(
        self, db: Session, *, kol_id: str
    ) -> List[VideoInfo]:
        """通过KOL ID获取所有视频信息对象"""
        return (
            db.query(self.model)
            .filter(self.model.kol_id == kol_id)
            .all()
        )
    
    def get_multi_by_platform(
        self, db: Session, *, platform: str, skip: int = 0, limit: int = 100
    ) -> List[VideoInfo]:
        """通过平台获取多个视频信息对象"""
        return (
            db.query(self.model)
            .filter(self.model.platform == platform)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def remove(self, db: Session, *, id: str) -> VideoInfo:
        """删除视频信息对象，适配主键名称"""
        obj = db.query(self.model).filter(self.model.video_id == id).first()
        db.delete(obj)
        db.commit()
        return obj
    
    def remove_by_kol_id(self, db: Session, *, kol_id: str) -> List[VideoInfo]:
        """删除指定KOL ID关联的所有视频信息对象"""
        videos = db.query(self.model).filter(self.model.kol_id == kol_id).all()
        for video in videos:
            db.delete(video)
        db.commit()
        return videos
    
    # 异步方法
    async def async_get(self, db: AsyncSession, id: str) -> Optional[VideoInfo]:
        """异步通过视频ID获取对象"""
        result = await db.execute(select(self.model).filter(self.model.video_id == id))
        return result.scalars().first()
    
    async def async_get_multi_by_kol_id(
        self, db: AsyncSession, *, kol_id: str
    ) -> List[VideoInfo]:
        """异步通过KOL ID获取所有视频信息对象"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.kol_id == kol_id)
        )
        return result.scalars().all()
    
    async def async_get_multi_by_platform(
        self, db: AsyncSession, *, platform: str, skip: int = 0, limit: int = 100
    ) -> List[VideoInfo]:
        """异步通过平台获取多个视频信息对象"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.platform == platform)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_remove(self, db: AsyncSession, *, id: str) -> VideoInfo:
        """异步删除视频信息对象"""
        obj = await self.async_get(db, id=id)
        await db.delete(obj)
        await db.commit()
        return obj


video_info = CRUDVideoInfo(VideoInfo)