from typing import Any, List, Optional
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.send_data import SendData
from app.schemas.send_data import SendDataCreate, SendDataUpdate


class CRUDSendData(CRUDBase[SendData, SendDataCreate, SendDataUpdate]):
    """发送数据的CRUD操作"""
    
    def get(self, db: Session, id: str) -> Optional[SendData]:
        """通过发送数据ID获取对象，这里覆盖基类方法来适应主键名称"""
        return db.query(self.model).filter(self.model.id == id).first()
    
    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[SendData]:
        """获取多个发送数据对象，按发送时间倒序排列"""
        return (
            db.query(self.model)
            .order_by(self.model.send_date.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_kol_id(
        self, db: Session, *, kol_id: str, skip: int = 0, limit: int = 100
    ) -> List[SendData]:
        """通过KOL ID获取多个发送数据对象，按发送时间倒序排列"""
        return (
            db.query(self.model)
            .filter(self.model.kol_id == kol_id)
            .order_by(self.model.send_date.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_status(
        self, db: Session, *, send_status: str, skip: int = 0, limit: int = 100
    ) -> List[SendData]:
        """通过发送状态获取多个发送数据对象，按发送时间倒序排列"""
        return (
            db.query(self.model)
            .filter(self.model.send_status == send_status)
            .order_by(self.model.send_date.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_platform(
        self, db: Session, *, platform: str, skip: int = 0, limit: int = 100
    ) -> List[SendData]:
        """通过平台获取多个发送数据对象，按发送时间倒序排列"""
        return (
            db.query(self.model)
            .filter(self.model.platform == platform)
            .order_by(self.model.send_date.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    # Count方法用于分页
    def count(self, db: Session) -> int:
        """获取发送数据总数"""
        return db.query(func.count(self.model.id)).scalar()
    
    def count_by_kol_id(self, db: Session, *, kol_id: str) -> int:
        """通过KOL ID获取发送数据总数"""
        return db.query(func.count(self.model.id)).filter(self.model.kol_id == kol_id).scalar()
    
    def count_by_status(self, db: Session, *, send_status: str) -> int:
        """通过发送状态获取发送数据总数"""
        return db.query(func.count(self.model.id)).filter(self.model.send_status == send_status).scalar()
    
    def count_by_platform(self, db: Session, *, platform: str) -> int:
        """通过平台获取发送数据总数"""
        return db.query(func.count(self.model.id)).filter(self.model.platform == platform).scalar()
        
    def get_all_send_statuses(self, db: Session) -> List[str]:
        """获取所有不同的发送状态值"""
        result = db.query(self.model.send_status).distinct().filter(self.model.send_status.is_not(None))
        return [status[0] for status in result if status[0]]
    
    # 异步方法
    async def async_get(self, db: AsyncSession, id: str) -> Optional[SendData]:
        """异步通过发送数据ID获取对象"""
        result = await db.execute(select(self.model).filter(self.model.id == id))
        return result.scalars().first()
    
    async def async_get_multi(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> List[SendData]:
        """异步获取多个发送数据对象，按发送时间倒序排列"""
        result = await db.execute(
            select(self.model)
            .order_by(self.model.send_date.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_multi_by_kol_id(
        self, db: AsyncSession, *, kol_id: str, skip: int = 0, limit: int = 100
    ) -> List[SendData]:
        """异步通过KOL ID获取多个发送数据对象，按发送时间倒序排列"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.kol_id == kol_id)
            .order_by(self.model.send_date.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_multi_by_status(
        self, db: AsyncSession, *, send_status: str, skip: int = 0, limit: int = 100
    ) -> List[SendData]:
        """异步通过发送状态获取多个发送数据对象，按发送时间倒序排列"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.send_status == send_status)
            .order_by(self.model.send_date.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def async_get_multi_by_platform(
        self, db: AsyncSession, *, platform: str, skip: int = 0, limit: int = 100
    ) -> List[SendData]:
        """异步通过平台获取多个发送数据对象，按发送时间倒序排列"""
        result = await db.execute(
            select(self.model)
            .filter(self.model.platform == platform)
            .order_by(self.model.send_date.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    # 异步Count方法用于分页
    async def async_count(self, db: AsyncSession) -> int:
        """异步获取发送数据总数"""
        result = await db.execute(select(func.count(self.model.id)))
        return result.scalar()
    
    async def async_count_by_kol_id(self, db: AsyncSession, *, kol_id: str) -> int:
        """异步通过KOL ID获取发送数据总数"""
        result = await db.execute(
            select(func.count(self.model.id)).filter(self.model.kol_id == kol_id)
        )
        return result.scalar()
    
    async def async_count_by_status(self, db: AsyncSession, *, send_status: str) -> int:
        """异步通过发送状态获取发送数据总数"""
        result = await db.execute(
            select(func.count(self.model.id)).filter(self.model.send_status == send_status)
        )
        return result.scalar()
    
    async def async_count_by_platform(self, db: AsyncSession, *, platform: str) -> int:
        """异步通过平台获取发送数据总数"""
        result = await db.execute(
            select(func.count(self.model.id)).filter(self.model.platform == platform)
        )
        return result.scalar()


send_data = CRUDSendData(SendData)