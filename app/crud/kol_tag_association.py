from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_

from app.crud.base import CRUDBase
from app.models.kol_tag_association import KolTagAssociation
from app.models.tag import Tag
from app.schemas.kol_tag_association import KolTagAssociationCreate, KolTagAssociationUpdate


class CRUDKolTagAssociation(CRUDBase[KolTagAssociation, KolTagAssociationCreate, KolTagAssociationUpdate]):
    """KOL标签关联的CRUD操作"""
    
    def get_by_kol_and_tag(self, db: Session, *, kol_id: str, tag_id: int) -> Optional[KolTagAssociation]:
        """根据KOL ID和标签ID获取关联记录"""
        return (
            db.query(KolTagAssociation)
            .filter(
                and_(
                    KolTagAssociation.kol_id == kol_id,
                    KolTagAssociation.tag_id == tag_id
                )
            )
            .first()
        )
    
    def get_by_kol_id(self, db: Session, *, kol_id: str, skip: int = 0, limit: int = 100) -> List[KolTagAssociation]:
        """根据KOL ID获取所有关联的标签"""
        return (
            db.query(KolTagAssociation)
            .options(joinedload(KolTagAssociation.tag))
            .filter(KolTagAssociation.kol_id == kol_id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_multi_by_kol_id(self, db: Session, *, kol_id: str) -> List[KolTagAssociation]:
        """根据KOL ID获取所有关联记录（无分页）"""
        return (
            db.query(KolTagAssociation)
            .filter(KolTagAssociation.kol_id == kol_id)
            .all()
        )
    
    def count_by_kol_id(self, db: Session, *, kol_id: str) -> int:
        """根据KOL ID统计关联的标签数量"""
        return db.query(KolTagAssociation).filter(KolTagAssociation.kol_id == kol_id).count()
    
    def get_by_tag_id(self, db: Session, *, tag_id: int, skip: int = 0, limit: int = 100) -> List[KolTagAssociation]:
        """根据标签ID获取所有关联的KOL"""
        return (
            db.query(KolTagAssociation)
            .filter(KolTagAssociation.tag_id == tag_id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def count_by_tag_id(self, db: Session, *, tag_id: int) -> int:
        """根据标签ID统计关联的KOL数量"""
        return db.query(KolTagAssociation).filter(KolTagAssociation.tag_id == tag_id).count()
    
    def get_by_project_code(self, db: Session, *, project_code: str, skip: int = 0, limit: int = 100) -> List[KolTagAssociation]:
        """根据项目编码获取关联记录"""
        return (
            db.query(KolTagAssociation)
            .options(joinedload(KolTagAssociation.tag))
            .filter(KolTagAssociation.project_code == project_code)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def count_by_project_code(self, db: Session, *, project_code: str) -> int:
        """根据项目编码统计关联记录数量"""
        return db.query(KolTagAssociation).filter(KolTagAssociation.project_code == project_code).count()
    
    def create_batch(self, db: Session, *, kol_id: str, tag_ids: List[int], project_code: Optional[str] = None) -> List[KolTagAssociation]:
        """批量创建KOL标签关联"""
        associations = []
        for tag_id in tag_ids:
            # 检查是否已存在
            existing = self.get_by_kol_and_tag(db, kol_id=kol_id, tag_id=tag_id)
            if not existing:
                association_data = KolTagAssociationCreate(
                    kol_id=kol_id,
                    tag_id=tag_id,
                    project_code=project_code
                )
                association = self.create(db, obj_in=association_data)
                associations.append(association)
            else:
                associations.append(existing)
        return associations
    
    def delete_batch(self, db: Session, *, kol_id: str, tag_ids: List[int]) -> int:
        """批量删除KOL标签关联"""
        deleted_count = (
            db.query(KolTagAssociation)
            .filter(
                and_(
                    KolTagAssociation.kol_id == kol_id,
                    KolTagAssociation.tag_id.in_(tag_ids)
                )
            )
            .delete(synchronize_session=False)
        )
        db.commit()
        return deleted_count
    
    def delete_by_kol_id(self, db: Session, *, kol_id: str) -> int:
        """删除KOL的所有标签关联"""
        deleted_count = (
            db.query(KolTagAssociation)
            .filter(KolTagAssociation.kol_id == kol_id)
            .delete(synchronize_session=False)
        )
        db.commit()
        return deleted_count
    
    def delete_by_kol_id_without_commit(self, db: Session, *, kol_id: str) -> int:
        """删除KOL的所有标签关联但不提交事务"""
        deleted_count = (
            db.query(KolTagAssociation)
            .filter(KolTagAssociation.kol_id == kol_id)
            .delete(synchronize_session=False)
        )
        db.flush()  # 刷新以确保删除操作被执行，但不提交
        return deleted_count
    
    def delete_by_tag_id(self, db: Session, *, tag_id: int) -> int:
        """删除标签的所有关联"""
        deleted_count = (
            db.query(KolTagAssociation)
            .filter(KolTagAssociation.tag_id == tag_id)
            .delete(synchronize_session=False)
        )
        db.commit()
        return deleted_count
    
    def get_kol_tags(self, db: Session, *, kol_id: str) -> List[Tag]:
        """获取KOL关联的所有标签"""
        return (
            db.query(Tag)
            .join(KolTagAssociation, Tag.id == KolTagAssociation.tag_id)
            .filter(KolTagAssociation.kol_id == kol_id)
            .all()
        )
    
    def get_tag_kols(self, db: Session, *, tag_id: int) -> List[str]:
        """获取标签关联的所有KOL ID"""
        result = (
            db.query(KolTagAssociation.kol_id)
            .filter(KolTagAssociation.tag_id == tag_id)
            .all()
        )
        return [row[0] for row in result]


kol_tag_association = CRUDKolTagAssociation(KolTagAssociation)