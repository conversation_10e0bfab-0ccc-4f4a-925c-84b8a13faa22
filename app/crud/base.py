from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.db.base import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        """
        CRUD基类，实现默认的CRUD操作
        """
        self.model = model

    # 同步方法
    def get(self, db: Session, id: Any) -> Optional[ModelType]:
        """通过ID获取对象"""
        return db.query(self.model).filter(self.model.id == id).first()

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[ModelType]:
        """获取多个对象"""
        return db.query(self.model).offset(skip).limit(limit).all()

    def create(self, db: Session, *, obj_in: CreateSchemaType) -> ModelType:
        """创建新对象"""
        obj_in_data = jsonable_encoder(obj_in)
        
        # 处理空字符串字段，将其转换为 None
        for key, value in obj_in_data.items():
            if value == "":
                obj_in_data[key] = None
                
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def create_without_commit(self, db: Session, *, obj_in: CreateSchemaType) -> ModelType:
        """创建新对象但不提交事务"""
        obj_in_data = jsonable_encoder(obj_in)
        
        # 处理空字符串字段，将其转换为 None
        for key, value in obj_in_data.items():
            if value == "":
                obj_in_data[key] = None
                
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.flush()  # 刷新以获取ID，但不提交
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """更新对象"""
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
            
        # 处理空字符串字段，将其转换为 None
        for key, value in update_data.items():
            if value == "":
                update_data[key] = None
                
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: Any) -> ModelType:
        """删除对象"""
        obj = db.query(self.model).get(id)
        db.delete(obj)
        db.commit()
        return obj

    def remove_without_commit(self, db: Session, *, id: Any) -> ModelType:
        """删除对象但不提交事务"""
        obj = db.query(self.model).get(id)
        if obj is None:
            raise ValueError(f"No {self.model.__name__} found with id: {id}")
        db.delete(obj)
        db.flush()  # 刷新以确保删除操作被执行，但不提交
        return obj

    # 异步方法
    async def async_get(self, db: AsyncSession, id: Any) -> Optional[ModelType]:
        """异步通过ID获取对象"""
        result = await db.execute(select(self.model).filter(self.model.id == id))
        return result.scalars().first()

    async def async_get_multi(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> List[ModelType]:
        """异步获取多个对象"""
        result = await db.execute(select(self.model).order_by(self.model.updated_at.desc()).offset(skip).limit(limit))
        return result.scalars().all()

    async def async_create(self, db: AsyncSession, *, obj_in: CreateSchemaType) -> ModelType:
        """异步创建新对象"""
        obj_in_data = jsonable_encoder(obj_in)
        
        # 处理空字符串字段，将其转换为 None
        for key, value in obj_in_data.items():
            if value == "":
                obj_in_data[key] = None
                
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def async_update(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """异步更新对象"""
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
            
        # 处理空字符串字段，将其转换为 None
        for key, value in update_data.items():
            if value == "":
                update_data[key] = None
                
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def async_remove(self, db: AsyncSession, *, id: Any) -> ModelType:
        """异步删除对象"""
        obj = await self.async_get(db, id=id)
        await db.delete(obj)
        await db.commit()
        return obj