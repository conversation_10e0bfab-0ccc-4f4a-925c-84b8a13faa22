from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.crud.base import CRUDBase
from app.models.tag import Tag
from app.schemas.tag import TagCreate, TagUpdate


class CRUDTag(CRUDBase[Tag, TagCreate, TagUpdate]):
    """标签的CRUD操作"""
    
    def get_by_name(self, db: Session, *, name: str) -> Optional[Tag]:
        """根据标签名称获取标签"""
        return db.query(Tag).filter(Tag.name == name).first()
    
    def get_multi_by_names(self, db: Session, *, names: List[str]) -> List[Tag]:
        """根据标签名称列表获取多个标签"""
        return db.query(Tag).filter(Tag.name.in_(names)).all()
    
    def search_by_name(self, db: Session, *, name: str, skip: int = 0, limit: int = 100) -> List[Tag]:
        """根据标签名称模糊搜索"""
        return (
            db.query(Tag)
            .filter(Tag.name.ilike(f"%{name}%"))
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def count_search_by_name(self, db: Session, *, name: str) -> int:
        """根据标签名称模糊搜索的总数"""
        return db.query(Tag).filter(Tag.name.ilike(f"%{name}%")).count()
    
    def get_or_create(self, db: Session, *, name: str) -> Tag:
        """获取或创建标签"""
        tag = self.get_by_name(db, name=name)
        if not tag:
            tag_create = TagCreate(name=name)
            tag = self.create(db, obj_in=tag_create)
        return tag
    
    def get_or_create_multi(self, db: Session, *, names: List[str]) -> List[Tag]:
        """批量获取或创建标签"""
        # 先查询已存在的标签
        existing_tags = self.get_multi_by_names(db, names=names)
        existing_names = {tag.name for tag in existing_tags}
        
        # 创建不存在的标签
        new_names = [name for name in names if name not in existing_names]
        new_tags = []
        for name in new_names:
            tag_create = TagCreate(name=name)
            new_tag = self.create(db, obj_in=tag_create)
            new_tags.append(new_tag)
        
        return existing_tags + new_tags
    
    def check_name_exists(self, db: Session, *, name: str, exclude_id: Optional[int] = None) -> bool:
        """检查标签名称是否已存在（用于更新时的唯一性检查）"""
        query = db.query(Tag).filter(Tag.name == name)
        if exclude_id:
            query = query.filter(Tag.id != exclude_id)
        return query.first() is not None


tag = CRUDTag(Tag)