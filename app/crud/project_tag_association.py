from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_

from app.crud.base import CRUDBase
from app.models.project_tag_association import ProjectTagAssociation
from app.models.tag import Tag
from app.schemas.project_tag_association import ProjectTagAssociationCreate, ProjectTagAssociationUpdate


class CRUDProjectTagAssociation(CRUDBase[ProjectTagAssociation, ProjectTagAssociationCreate, ProjectTagAssociationUpdate]):
    """项目标签关联的CRUD操作"""
    
    def get_by_project_and_tag(self, db: Session, *, project_code: str, tag_id: int) -> Optional[ProjectTagAssociation]:
        """根据项目编码和标签ID获取关联记录"""
        return (
            db.query(ProjectTagAssociation)
            .filter(
                and_(
                    ProjectTagAssociation.project_code == project_code,
                    ProjectTagAssociation.tag_id == tag_id
                )
            )
            .first()
        )
    
    def get_by_project_code(self, db: Session, *, project_code: str, skip: int = 0, limit: int = 100) -> List[ProjectTagAssociation]:
        """根据项目编码获取所有关联的标签"""
        return (
            db.query(ProjectTagAssociation)
            .options(joinedload(ProjectTagAssociation.tag))
            .filter(ProjectTagAssociation.project_code == project_code)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def count_by_project_code(self, db: Session, *, project_code: str) -> int:
        """根据项目编码统计关联的标签数量"""
        return db.query(ProjectTagAssociation).filter(ProjectTagAssociation.project_code == project_code).count()
    
    def get_by_tag_id(self, db: Session, *, tag_id: int, skip: int = 0, limit: int = 100) -> List[ProjectTagAssociation]:
        """根据标签ID获取所有关联的项目"""
        return (
            db.query(ProjectTagAssociation)
            .filter(ProjectTagAssociation.tag_id == tag_id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def count_by_tag_id(self, db: Session, *, tag_id: int) -> int:
        """根据标签ID统计关联的项目数量"""
        return db.query(ProjectTagAssociation).filter(ProjectTagAssociation.tag_id == tag_id).count()
    
    def create_batch(self, db: Session, *, project_code: str, tag_ids: List[int]) -> List[ProjectTagAssociation]:
        """批量创建项目标签关联"""
        associations = []
        for tag_id in tag_ids:
            # 检查是否已存在
            existing = self.get_by_project_and_tag(db, project_code=project_code, tag_id=tag_id)
            if not existing:
                association_data = ProjectTagAssociationCreate(
                    project_code=project_code,
                    tag_id=tag_id
                )
                association = self.create(db, obj_in=association_data)
                associations.append(association)
            else:
                associations.append(existing)
        return associations
    
    def delete_batch(self, db: Session, *, project_code: str, tag_ids: List[int]) -> int:
        """批量删除项目标签关联"""
        deleted_count = (
            db.query(ProjectTagAssociation)
            .filter(
                and_(
                    ProjectTagAssociation.project_code == project_code,
                    ProjectTagAssociation.tag_id.in_(tag_ids)
                )
            )
            .delete(synchronize_session=False)
        )
        db.commit()
        return deleted_count
    
    def delete_by_project_code(self, db: Session, *, project_code: str) -> int:
        """删除项目的所有标签关联"""
        deleted_count = (
            db.query(ProjectTagAssociation)
            .filter(ProjectTagAssociation.project_code == project_code)
            .delete(synchronize_session=False)
        )
        db.commit()
        return deleted_count
    
    def delete_by_tag_id(self, db: Session, *, tag_id: int) -> int:
        """删除标签的所有关联"""
        deleted_count = (
            db.query(ProjectTagAssociation)
            .filter(ProjectTagAssociation.tag_id == tag_id)
            .delete(synchronize_session=False)
        )
        db.commit()
        return deleted_count
    
    def get_project_tags(self, db: Session, *, project_code: str) -> List[Tag]:
        """获取项目关联的所有标签"""
        return (
            db.query(Tag)
            .join(ProjectTagAssociation, Tag.id == ProjectTagAssociation.tag_id)
            .filter(ProjectTagAssociation.project_code == project_code)
            .all()
        )
    
    def get_tag_projects(self, db: Session, *, tag_id: int) -> List[str]:
        """获取标签关联的所有项目编码"""
        result = (
            db.query(ProjectTagAssociation.project_code)
            .filter(ProjectTagAssociation.tag_id == tag_id)
            .all()
        )
        return [row[0] for row in result]
    
    def get_all_projects(self, db: Session) -> List[str]:
        """获取所有项目编码"""
        result = (
            db.query(ProjectTagAssociation.project_code)
            .distinct()
            .all()
        )
        return [row[0] for row in result]
    
    def replace_project_tags(self, db: Session, *, project_code: str, tag_ids: List[int]) -> List[ProjectTagAssociation]:
        """替换项目的所有标签关联"""
        # 先删除现有关联
        self.delete_by_project_code(db, project_code=project_code)
        # 创建新关联
        return self.create_batch(db, project_code=project_code, tag_ids=tag_ids)


project_tag_association = CRUDProjectTagAssociation(ProjectTagAssociation)