from typing import List, Optional, Tuple, Dict, Any
import math
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func, select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.candidate_data import CandidateData
from app.schemas.candidate_data import CandidateDataCreate, CandidateDataUpdate


class CRUDCandidateData(CRUDBase[CandidateData, CandidateDataCreate, CandidateDataUpdate]):
    """KOL候选人数据的CRUD操作"""
    
    def get_by_kol_id(self, db: Session, *, kol_id: str) -> Optional[CandidateData]:
        """根据KOL ID获取候选人数据"""
        return db.query(self.model).filter(self.model.kol_id == kol_id).first()
    
    def get_by_reply_email(self, db: Session, *, reply_email: str) -> Optional[CandidateData]:
        """根据回复邮箱获取候选人数据"""
        return db.query(self.model).filter(self.model.reply_email == reply_email).first()
    
    def get_multi_by_reply_email(
        self, db: Session, *, reply_email: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[CandidateData], int]:
        """根据回复邮箱获取候选人数据列表和总数"""
        query = db.query(self.model).filter(self.model.reply_email == reply_email)
        total = query.count()
        items = query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()
        return items, total
    
    def get_multi_by_label(
        self, db: Session, *, label: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[CandidateData], int]:
        """根据标签获取候选人数据列表和总数"""
        query = db.query(self.model).filter(self.model.label == label)
        total = query.count()
        items = query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()
        return items, total
    
    def get_multi_by_project(
        self, db: Session, *, project: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[CandidateData], int]:
        """根据项目获取候选人数据列表和总数"""
        query = db.query(self.model).filter(self.model.project == project)
        total = query.count()
        items = query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()
        return items, total
    
    def get_with_pagination(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> Tuple[List[CandidateData], int]:
        """获取分页的候选人数据列表和总数"""
        query = db.query(self.model)
        total = query.count()
        items = query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()
        return items, total
    
    # 高级查询方法
    def advanced_search(
        self, 
        db: Session, 
        *, 
        conditions: List[Dict[str, Any]], 
        conjunction: str = "and",
        project: Optional[str] = None,
        label: Optional[str] = None,
        skip: int = 0, 
        limit: int = 100
    ) -> Tuple[List[CandidateData], int]:
        """高级搜索候选人数据"""
        query = db.query(self.model)
        
        # 添加基本过滤条件
        if project:
            query = query.filter(self.model.project == project)
        
        if label:
            query = query.filter(self.model.label == label)
        
        # 构建高级搜索条件
        if conditions:
            filters = []
            for condition in conditions:
                field = condition.get("field")
                operator = condition.get("operator")
                value = condition.get("value")
                
                # 如果缺少必要参数，跳过此条件
                if not field or not operator:
                    continue
                
                # 获取模型字段
                model_attr = getattr(self.model, field, None)
                if not model_attr:
                    continue
                
                # 根据操作符添加筛选条件
                if operator == "eq":
                    filters.append(model_attr == value)
                elif operator == "ne":
                    filters.append(model_attr != value)
                elif operator == "contains":
                    filters.append(model_attr.ilike(f"%{value}%"))
                elif operator == "not_contains":
                    filters.append(~model_attr.ilike(f"%{value}%"))
                elif operator == "is_null":
                    filters.append(model_attr.is_(None))
                elif operator == "is_not_null":
                    filters.append(model_attr.isnot(None))
                elif operator == "gt":
                    filters.append(model_attr > value)
                elif operator == "lt":
                    filters.append(model_attr < value)
                elif operator == "ge":
                    filters.append(model_attr >= value)
                elif operator == "le":
                    filters.append(model_attr <= value)
            
            # 合并所有条件
            if filters:
                if conjunction.lower() == "and":
                    query = query.filter(and_(*filters))
                else:
                    query = query.filter(or_(*filters))
        
        # 获取总数
        total = query.count()
        
        # 应用分页和排序
        items = query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()
        
        return items, total
    
    # 异步方法
    async def async_get_by_kol_id(self, db: AsyncSession, *, kol_id: str) -> Optional[CandidateData]:
        """异步根据KOL ID获取候选人数据"""
        result = await db.execute(select(self.model).filter(self.model.kol_id == kol_id))
        return result.scalars().first()
    
    async def async_get_by_reply_email(self, db: AsyncSession, *, reply_email: str) -> Optional[CandidateData]:
        """异步根据回复邮箱获取候选人数据"""
        result = await db.execute(select(self.model).filter(self.model.reply_email == reply_email))
        return result.scalars().first()
    
    async def async_get_multi_by_reply_email(
        self, db: AsyncSession, *, reply_email: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[CandidateData], int]:
        """异步根据回复邮箱获取候选人数据列表和总数"""
        query = select(self.model).filter(self.model.reply_email == reply_email)
        result = await db.execute(query.order_by(self.model.created_at.desc()).offset(skip).limit(limit))
        total_result = await db.execute(select(func.count()).select_from(query.subquery()))
        total = total_result.scalar_one()
        items = result.scalars().all()
        return items, total
    
    async def async_get_multi_by_label(
        self, db: AsyncSession, *, label: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[CandidateData], int]:
        """异步根据标签获取候选人数据列表和总数"""
        query = select(self.model).filter(self.model.label == label)
        result = await db.execute(query.order_by(self.model.created_at.desc()).offset(skip).limit(limit))
        total_result = await db.execute(select(func.count()).select_from(query.subquery()))
        total = total_result.scalar_one()
        items = result.scalars().all()
        return items, total
    
    async def async_get_multi_by_project(
        self, db: AsyncSession, *, project: str, skip: int = 0, limit: int = 100
    ) -> Tuple[List[CandidateData], int]:
        """异步根据项目获取候选人数据列表和总数"""
        query = select(self.model).filter(self.model.project == project)
        result = await db.execute(query.order_by(self.model.created_at.desc()).offset(skip).limit(limit))
        total_result = await db.execute(select(func.count()).select_from(query.subquery()))
        total = total_result.scalar_one()
        items = result.scalars().all()
        return items, total
    
    async def async_get_with_pagination(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> Tuple[List[CandidateData], int]:
        """异步获取分页的候选人数据列表和总数"""
        result = await db.execute(select(self.model).order_by(self.model.created_at.desc()).offset(skip).limit(limit))
        total_result = await db.execute(select(func.count()).select_from(self.model))
        total = total_result.scalar_one()
        items = result.scalars().all()
        return items, total
    
    # 异步高级查询
    async def async_advanced_search(
        self, 
        db: AsyncSession, 
        *, 
        conditions: List[Dict[str, Any]], 
        conjunction: str = "and",
        project: Optional[str] = None,
        label: Optional[str] = None,
        skip: int = 0, 
        limit: int = 100
    ) -> Tuple[List[CandidateData], int]:
        """异步高级搜索候选人数据"""
        query = select(self.model)
        
        # 添加基本过滤条件
        if project:
            query = query.where(self.model.project == project)
        
        if label:
            query = query.where(self.model.label == label)
        
        # 构建高级搜索条件
        if conditions:
            filters = []
            for condition in conditions:
                field = condition.get("field")
                operator = condition.get("operator")
                value = condition.get("value")
                
                # 如果缺少必要参数，跳过此条件
                if not field or not operator:
                    continue
                
                # 获取模型字段
                model_attr = getattr(self.model, field, None)
                if not model_attr:
                    continue
                
                # 根据操作符添加筛选条件
                if operator == "eq":
                    filters.append(model_attr == value)
                elif operator == "ne":
                    filters.append(model_attr != value)
                elif operator == "contains":
                    filters.append(model_attr.ilike(f"%{value}%"))
                elif operator == "not_contains":
                    filters.append(~model_attr.ilike(f"%{value}%"))
                elif operator == "is_null":
                    filters.append(model_attr.is_(None))
                elif operator == "is_not_null":
                    filters.append(model_attr.isnot(None))
                elif operator == "gt":
                    filters.append(model_attr > value)
                elif operator == "lt":
                    filters.append(model_attr < value)
                elif operator == "ge":
                    filters.append(model_attr >= value)
                elif operator == "le":
                    filters.append(model_attr <= value)
            
            # 合并所有条件
            if filters:
                if conjunction.lower() == "and":
                    query = query.where(and_(*filters))
                else:
                    query = query.where(or_(*filters))
        
        # 执行查询
        result = await db.execute(query.order_by(self.model.created_at.desc()).offset(skip).limit(limit))
        items = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar_one() or 0
        
        return items, total
    
    def get_by_thread_id(self, db: Session, *, thread_id: str) -> Optional[CandidateData]:
        """根据对话线程ID获取候选人数据"""
        return db.query(self.model).filter(self.model.thread_id == thread_id).first()
    
    def remove_by_thread_id(self, db: Session, *, thread_id: str) -> Optional[CandidateData]:
        """根据对话线程ID删除候选人数据"""
        obj = self.get_by_thread_id(db, thread_id=thread_id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj
    
    async def async_get_by_thread_id(self, db: AsyncSession, *, thread_id: str) -> Optional[CandidateData]:
        """异步根据对话线程ID获取候选人数据"""
        result = await db.execute(select(self.model).filter(self.model.thread_id == thread_id))
        return result.scalars().first()
    
    async def async_remove_by_thread_id(self, db: AsyncSession, *, thread_id: str) -> Optional[CandidateData]:
        """异步根据对话线程ID删除候选人数据"""
        obj = await self.async_get_by_thread_id(db, thread_id=thread_id)
        if obj:
            await db.delete(obj)
            await db.commit()
        return obj
    
    def update_by_thread_id(self, db: Session, *, thread_id: str, obj_in: CandidateDataUpdate) -> Optional[CandidateData]:
        """根据对话线程ID更新候选人数据"""
        obj = self.get_by_thread_id(db, thread_id=thread_id)
        if obj:
            obj_data = obj.__dict__.copy()
            if isinstance(obj_in, dict):
                update_data = obj_in
            else:
                update_data = obj_in.dict(exclude_unset=True)
            for field in obj_data:
                if field in update_data:
                    setattr(obj, field, update_data[field])
            db.add(obj)
            db.commit()
            db.refresh(obj)
        return obj
    
    async def async_update_by_thread_id(self, db: AsyncSession, *, thread_id: str, obj_in: CandidateDataUpdate) -> Optional[CandidateData]:
        """异步根据对话线程ID更新候选人数据"""
        obj = await self.async_get_by_thread_id(db, thread_id=thread_id)
        if obj:
            obj_data = obj.__dict__.copy()
            if isinstance(obj_in, dict):
                update_data = obj_in
            else:
                update_data = obj_in.dict(exclude_unset=True)
            for field in obj_data:
                if field in update_data:
                    setattr(obj, field, update_data[field])
            db.add(obj)
            await db.commit()
            await db.refresh(obj)
        return obj
    
    def get_aggregated_by_kol_id(
        self, db: Session, *, skip: int = 0, limit: int = 100,
        project: Optional[str] = None, label: Optional[str] = None, reply_email: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """按kol_id聚合候选人数据"""
        from collections import defaultdict
        
        # 构建查询
        query = db.query(self.model)
        
        # 应用筛选条件
        if project:
            query = query.filter(self.model.project == project)
        if label:
            query = query.filter(self.model.label == label)
        if reply_email:
            query = query.filter(self.model.reply_email == reply_email)
        
        # 按创建时间倒序排序
        query = query.order_by(self.model.created_at.desc())
        
        # 获取所有数据进行聚合
        all_data = query.all()
        
        # 按kol_id分组聚合
        aggregated_data = defaultdict(lambda: {
            'kol_id': '',
            'kol_name': None,
            'first_contact_dates': [],
            'last_contact_dates': [],
            'follow_ups': [],
            'labels': [],
            'sublabels': [],
            'thread_ids': [],
            'projects': [],
            'reply_emails': [],
            'ids': [],
            'created_ats': [],
            'updated_ats': [],
            'record_count': 0
        })
        
        for item in all_data:
            kol_id = item.kol_id
            agg_item = aggregated_data[kol_id]
            
            # 设置kol_id和kol_name（相同字段只显示一个）
            if not agg_item['kol_id']:
                agg_item['kol_id'] = kol_id
                agg_item['kol_name'] = item.kol_name
            
            # 聚合不同字段到数组，去除重复值
            if item.first_contact_date not in agg_item['first_contact_dates']:
                agg_item['first_contact_dates'].append(item.first_contact_date)
            if item.last_contact_date not in agg_item['last_contact_dates']:
                agg_item['last_contact_dates'].append(item.last_contact_date)
            if item.follow_up not in agg_item['follow_ups']:
                agg_item['follow_ups'].append(item.follow_up)
            if item.label not in agg_item['labels']:
                agg_item['labels'].append(item.label)
            if item.sublabel not in agg_item['sublabels']:
                agg_item['sublabels'].append(item.sublabel)
            if item.thread_id not in agg_item['thread_ids']:
                agg_item['thread_ids'].append(item.thread_id)
            if item.project not in agg_item['projects']:
                agg_item['projects'].append(item.project)
            if item.reply_email not in agg_item['reply_emails']:
                agg_item['reply_emails'].append(item.reply_email)
            
            # ID和时间戳保留所有值
            agg_item['ids'].append(item.id)
            agg_item['created_ats'].append(item.created_at)
            agg_item['updated_ats'].append(item.updated_at)
            agg_item['record_count'] += 1
        
        # 转换为列表并对每个聚合项的数组进行排序
        aggregated_list = list(aggregated_data.values())
        for item in aggregated_list:
            # 对日期数组先过滤None值再排序
            item['first_contact_dates'] = [x for x in item['first_contact_dates'] if x is not None]
            item['last_contact_dates'] = [x for x in item['last_contact_dates'] if x is not None]
            item['created_ats'] = [x for x in item['created_ats'] if x is not None]
            item['updated_ats'] = [x for x in item['updated_ats'] if x is not None]
            
            # 排序日期数组
            if item['first_contact_dates']:
                item['first_contact_dates'].sort(reverse=True)
            if item['last_contact_dates']:
                item['last_contact_dates'].sort(reverse=True)
            if item['created_ats']:
                item['created_ats'].sort(reverse=True)
            if item['updated_ats']:
                item['updated_ats'].sort(reverse=True)
            
            # 对其他数组去除None值并排序
            item['follow_ups'] = [x for x in item['follow_ups'] if x is not None]
            item['labels'] = [x for x in item['labels'] if x is not None]
            item['sublabels'] = [x for x in item['sublabels'] if x is not None]
            item['thread_ids'] = [x for x in item['thread_ids'] if x is not None]
            item['projects'] = [x for x in item['projects'] if x is not None]
            item['reply_emails'] = [x for x in item['reply_emails'] if x is not None]
        
        total = len(aggregated_list)
        
        # 按最新创建时间排序（取每个kol_id的最新记录时间）
        aggregated_list.sort(key=lambda x: max(x['created_ats']) if x['created_ats'] else datetime.min, reverse=True)
        
        # 应用分页
        paginated_items = aggregated_list[skip:skip + limit]
        
        return paginated_items, total

    async def async_get_aggregated_by_kol_id(
        self, 
        db: Session, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        project: Optional[str] = None,
        label: Optional[str] = None,
        reply_email: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        异步按kol_id聚合获取候选人数据，相同字段显示一个值，不同字段聚合成数组
        
        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 限制返回的记录数
            project: 项目筛选条件
            label: 标签筛选条件
            reply_email: 回复邮箱筛选条件
            
        Returns:
            聚合后的数据列表和总数
        """
        # 复用同步方法的逻辑
        return self.get_aggregated_by_kol_id(
            db, skip=skip, limit=limit, project=project, label=label, reply_email=reply_email
        )


candidate_data = CRUDCandidateData(CandidateData)