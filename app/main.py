import time
from typing import Any
from datetime import datetime

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from starlette.responses import RedirectResponse

from app.api.v1 import api_router
from app.core.config import settings
from app.logging_config import setup_logging, get_request_logger, logger

# 设置日志系统
setup_logging()
request_logger = get_request_logger()

# 创建 FastAPI 应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    version=settings.VERSION,
)


# 添加请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()

    # 生成请求ID
    request_id = f"{int(start_time * 1000)}"

    # 记录请求开始
    request_logger.info(
        f"开始处理请求 | ID:{request_id} | {request.method} {request.url.path} | "
        f"Client:{request.client.host if request.client else 'Unknown'} | "
        f"User-Agent:{request.headers.get('user-agent', 'Unknown')}"
    )

    # 处理请求
    try:
        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time

        # 记录请求完成
        request_logger.info(
            f"请求完成 | ID:{request_id} | {request.method} {request.url.path} | "
            f"状态码:{response.status_code} | 耗时:{process_time:.4f}秒"
        )

        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = f"{process_time:.4f}"
        return response
    except Exception as e:
        # 计算处理时间
        process_time = time.time() - start_time

        # 记录请求错误
        request_logger.error(
            f"请求错误 | ID:{request_id} | {request.method} {request.url.path} | "
            f"错误:{str(e)} | 耗时:{process_time:.4f}秒"
        )
        raise


# 设置 CORS
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 注册 API 路由
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.on_event("startup")
async def startup_event():
    logger.info("=== 应用启动 ===")
    logger.info(f"配置: 日志级别={settings.LOG_LEVEL}, 日志文件={settings.log_file}")
    logger.info(f"数据库: {settings.DATABASE_URI}")
    logger.info(f"Redis: {settings.REDIS_URI}")


@app.on_event("shutdown")
async def shutdown_event():
    logger.info("=== 应用关闭 ===")


@app.get("/", include_in_schema=False)
def redirect_to_docs() -> Any:
    """
    根路径重定向到 API 文档
    """
    return RedirectResponse(url="/docs")


@app.get("/health", tags=["健康检查"])
def health_check() -> Any:
    """
    API 健康检查端点
    """
    logger.debug("详细健康检查端点被访问")
    return {
        "status": "healthy",
        "version": settings.VERSION,
        "timestamp": datetime.now().isoformat()
    }


if __name__ == "__main__":
    # 如果直接运行此文件，则启动Uvicorn服务器
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=settings.LOG_LEVEL.lower(),
    )
