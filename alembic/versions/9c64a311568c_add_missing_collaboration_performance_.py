"""add_missing_collaboration_performance_fields

Revision ID: 9c64a311568c
Revises: acf6eaf9f3c5
Create Date: 2025-06-23 21:09:04.805520

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9c64a311568c'
down_revision: Union[str, None] = 'acf6eaf9f3c5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('collaboration_performance', sa.Column('paypal_accounts', sa.String(), nullable=True))
    op.add_column('collaboration_performance', sa.Column('collab_status', sa.String(), nullable=True))
    op.add_column('collaboration_performance', sa.Column('follow_up', sa.String(), nullable=True))
    op.add_column('collaboration_performance', sa.Column('payout_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('collaboration_performance', sa.Column('fund_source', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('collaboration_performance', 'fund_source')
    op.drop_column('collaboration_performance', 'payout_date')
    op.drop_column('collaboration_performance', 'follow_up')
    op.drop_column('collaboration_performance', 'collab_status')
    op.drop_column('collaboration_performance', 'paypal_accounts')
    # ### end Alembic commands ###
