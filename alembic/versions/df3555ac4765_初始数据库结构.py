"""初始数据库结构

Revision ID: df3555ac4765
Revises: 
Create Date: 2025-06-10 11:45:03.575321

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'df3555ac4765'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('candidate_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('kol_id', sa.String(), nullable=False),
    sa.Column('kol_name', sa.String(), nullable=True),
    sa.Column('first_contact_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_contact_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('follow_up', sa.String(), nullable=True),
    sa.Column('label', sa.String(), nullable=True),
    sa.Column('sublabel', sa.String(), nullable=True),
    sa.Column('thread_id', sa.String(), nullable=True),
    sa.Column('project', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_candidate_data_id'), 'candidate_data', ['id'], unique=False)
    op.create_index(op.f('ix_candidate_data_kol_id'), 'candidate_data', ['kol_id'], unique=False)
    op.create_index(op.f('ix_candidate_data_kol_name'), 'candidate_data', ['kol_name'], unique=False)
    op.create_index(op.f('ix_candidate_data_label'), 'candidate_data', ['label'], unique=False)
    op.create_index(op.f('ix_candidate_data_project'), 'candidate_data', ['project'], unique=False)
    op.create_table('collaboration_performance',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('kol_id', sa.String(), nullable=False),
    sa.Column('project', sa.String(), nullable=True),
    sa.Column('kol_priority', sa.String(), nullable=True),
    sa.Column('post_link', sa.String(), nullable=True),
    sa.Column('payment', sa.String(), nullable=True),
    sa.Column('post_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('views_total', sa.Integer(), nullable=True),
    sa.Column('likes_total', sa.Integer(), nullable=True),
    sa.Column('comments_total', sa.Integer(), nullable=True),
    sa.Column('shares_total', sa.Integer(), nullable=True),
    sa.Column('views_day1', sa.Integer(), nullable=True),
    sa.Column('likes_day1', sa.Integer(), nullable=True),
    sa.Column('comments_day1', sa.Integer(), nullable=True),
    sa.Column('shares_day1', sa.Integer(), nullable=True),
    sa.Column('views_day3', sa.Integer(), nullable=True),
    sa.Column('likes_day3', sa.Integer(), nullable=True),
    sa.Column('comments_day3', sa.Integer(), nullable=True),
    sa.Column('shares_day3', sa.Integer(), nullable=True),
    sa.Column('views_day7', sa.Integer(), nullable=True),
    sa.Column('likes_day7', sa.Integer(), nullable=True),
    sa.Column('comments_day7', sa.Integer(), nullable=True),
    sa.Column('shares_day7', sa.Integer(), nullable=True),
    sa.Column('engagement_rate', sa.Float(), nullable=True),
    sa.Column('cpm', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_collaboration_performance_id'), 'collaboration_performance', ['id'], unique=False)
    op.create_index(op.f('ix_collaboration_performance_kol_id'), 'collaboration_performance', ['kol_id'], unique=False)
    op.create_index(op.f('ix_collaboration_performance_project'), 'collaboration_performance', ['project'], unique=False)
    op.create_table('filter_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('language', sa.String(), nullable=True),
    sa.Column('gender', sa.String(), nullable=True),
    sa.Column('location', sa.String(), nullable=True),
    sa.Column('filter_body', sa.JSON(), nullable=True),
    sa.Column('filter_name', sa.String(), nullable=True),
    sa.Column('project_code', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_filter_data_id'), 'filter_data', ['id'], unique=False)
    op.create_table('kol_info',
    sa.Column('kol_id', sa.String(), nullable=False),
    sa.Column('kol_name', sa.String(), nullable=False),
    sa.Column('username', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('bio', sa.String(), nullable=True),
    sa.Column('account_link', sa.String(), nullable=True),
    sa.Column('followers_k', sa.Float(), nullable=True),
    sa.Column('likes_k', sa.Float(), nullable=True),
    sa.Column('platform', sa.String(), nullable=True),
    sa.Column('source', sa.String(), nullable=True),
    sa.Column('slug', sa.String(), nullable=True),
    sa.Column('creator_id', sa.String(), nullable=True),
    sa.Column('mean_views_k', sa.Float(), nullable=True),
    sa.Column('median_views_k', sa.Float(), nullable=True),
    sa.Column('engagement_rate', sa.Float(), nullable=True),
    sa.Column('average_views_k', sa.Float(), nullable=True),
    sa.Column('average_likes_k', sa.Float(), nullable=True),
    sa.Column('average_comments_k', sa.Float(), nullable=True),
    sa.Column('most_used_hashtags', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('level', sa.String(), nullable=True),
    sa.Column('keywords_ai', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('kol_id')
    )
    op.create_index(op.f('ix_kol_info_average_comments_k'), 'kol_info', ['average_comments_k'], unique=False)
    op.create_index(op.f('ix_kol_info_average_likes_k'), 'kol_info', ['average_likes_k'], unique=False)
    op.create_index(op.f('ix_kol_info_average_views_k'), 'kol_info', ['average_views_k'], unique=False)
    op.create_index(op.f('ix_kol_info_created_at'), 'kol_info', ['created_at'], unique=False)
    op.create_index(op.f('ix_kol_info_creator_id'), 'kol_info', ['creator_id'], unique=False)
    op.create_index(op.f('ix_kol_info_email'), 'kol_info', ['email'], unique=False)
    op.create_index(op.f('ix_kol_info_engagement_rate'), 'kol_info', ['engagement_rate'], unique=False)
    op.create_index(op.f('ix_kol_info_followers_k'), 'kol_info', ['followers_k'], unique=False)
    op.create_index('ix_kol_info_keywords_ai', 'kol_info', ['keywords_ai'], unique=False, postgresql_using='gin')
    op.create_index(op.f('ix_kol_info_kol_id'), 'kol_info', ['kol_id'], unique=False)
    op.create_index(op.f('ix_kol_info_kol_name'), 'kol_info', ['kol_name'], unique=False)
    op.create_index(op.f('ix_kol_info_level'), 'kol_info', ['level'], unique=False)
    op.create_index(op.f('ix_kol_info_likes_k'), 'kol_info', ['likes_k'], unique=False)
    op.create_index(op.f('ix_kol_info_mean_views_k'), 'kol_info', ['mean_views_k'], unique=False)
    op.create_index(op.f('ix_kol_info_median_views_k'), 'kol_info', ['median_views_k'], unique=False)
    op.create_index('ix_kol_info_most_used_hashtags', 'kol_info', ['most_used_hashtags'], unique=False, postgresql_using='gin')
    op.create_index(op.f('ix_kol_info_platform'), 'kol_info', ['platform'], unique=False)
    op.create_index(op.f('ix_kol_info_slug'), 'kol_info', ['slug'], unique=False)
    op.create_index(op.f('ix_kol_info_source'), 'kol_info', ['source'], unique=False)
    op.create_index(op.f('ix_kol_info_updated_at'), 'kol_info', ['updated_at'], unique=False)
    op.create_index(op.f('ix_kol_info_username'), 'kol_info', ['username'], unique=False)
    op.create_table('send_data',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('kol_id', sa.String(), nullable=False),
    sa.Column('send_status', sa.String(), nullable=True),
    sa.Column('platform', sa.String(), nullable=True),
    sa.Column('send_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('export_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('app_code', sa.String(), nullable=True),
    sa.Column('template_id', sa.String(), nullable=True),
    sa.Column('read_status', sa.Boolean(), nullable=False),
    sa.Column('success', sa.Boolean(), nullable=False),
    sa.Column('from_email', sa.String(), nullable=True),
    sa.Column('to_email', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_send_data_id'), 'send_data', ['id'], unique=False)
    op.create_index(op.f('ix_send_data_kol_id'), 'send_data', ['kol_id'], unique=False)
    op.create_table('tag',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tag_id'), 'tag', ['id'], unique=False)
    op.create_index(op.f('ix_tag_name'), 'tag', ['name'], unique=True)
    op.create_table('filter_kol_association',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('filter_id', sa.Integer(), nullable=False),
    sa.Column('kol_id', sa.String(), nullable=False),
    sa.Column('project_code', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['filter_id'], ['filter_data.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['kol_id'], ['kol_info.kol_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_filter_kol_association_filter_id'), 'filter_kol_association', ['filter_id'], unique=False)
    op.create_index(op.f('ix_filter_kol_association_id'), 'filter_kol_association', ['id'], unique=False)
    op.create_index(op.f('ix_filter_kol_association_kol_id'), 'filter_kol_association', ['kol_id'], unique=False)
    op.create_table('kol_tag_association',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('kol_id', sa.String(), nullable=False),
    sa.Column('tag_id', sa.Integer(), nullable=False),
    sa.Column('project_code', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['kol_id'], ['kol_info.kol_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['tag_id'], ['tag.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_kol_tag_association_id'), 'kol_tag_association', ['id'], unique=False)
    op.create_index(op.f('ix_kol_tag_association_kol_id'), 'kol_tag_association', ['kol_id'], unique=False)
    op.create_index(op.f('ix_kol_tag_association_project_code'), 'kol_tag_association', ['project_code'], unique=False)
    op.create_index(op.f('ix_kol_tag_association_tag_id'), 'kol_tag_association', ['tag_id'], unique=False)
    op.create_table('project_tag_association',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('project_code', sa.String(), nullable=False),
    sa.Column('tag_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['tag_id'], ['tag.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_project_tag_association_id'), 'project_tag_association', ['id'], unique=False)
    op.create_index(op.f('ix_project_tag_association_project_code'), 'project_tag_association', ['project_code'], unique=False)
    op.create_index(op.f('ix_project_tag_association_tag_id'), 'project_tag_association', ['tag_id'], unique=False)
    op.create_table('video_info',
    sa.Column('video_id', sa.String(), nullable=False),
    sa.Column('kol_id', sa.String(), nullable=False),
    sa.Column('play_count', sa.Integer(), nullable=True),
    sa.Column('is_pinned', sa.Boolean(), nullable=False),
    sa.Column('share_url', sa.String(), nullable=True),
    sa.Column('desc', sa.String(), nullable=True),
    sa.Column('desc_language', sa.String(), nullable=True),
    sa.Column('video_url', sa.String(), nullable=True),
    sa.Column('music_url', sa.String(), nullable=True),
    sa.Column('likes_count', sa.Integer(), nullable=True),
    sa.Column('comments_count', sa.Integer(), nullable=True),
    sa.Column('shares_count', sa.Integer(), nullable=True),
    sa.Column('collect_count', sa.Integer(), nullable=True),
    sa.Column('platform', sa.String(), nullable=True),
    sa.Column('hashtags', sa.JSON(), nullable=True),
    sa.Column('create_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['kol_id'], ['kol_info.kol_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('video_id')
    )
    op.create_index(op.f('ix_video_info_kol_id'), 'video_info', ['kol_id'], unique=False)
    op.create_index(op.f('ix_video_info_video_id'), 'video_info', ['video_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_video_info_video_id'), table_name='video_info')
    op.drop_index(op.f('ix_video_info_kol_id'), table_name='video_info')
    op.drop_table('video_info')
    op.drop_index(op.f('ix_project_tag_association_tag_id'), table_name='project_tag_association')
    op.drop_index(op.f('ix_project_tag_association_project_code'), table_name='project_tag_association')
    op.drop_index(op.f('ix_project_tag_association_id'), table_name='project_tag_association')
    op.drop_table('project_tag_association')
    op.drop_index(op.f('ix_kol_tag_association_tag_id'), table_name='kol_tag_association')
    op.drop_index(op.f('ix_kol_tag_association_project_code'), table_name='kol_tag_association')
    op.drop_index(op.f('ix_kol_tag_association_kol_id'), table_name='kol_tag_association')
    op.drop_index(op.f('ix_kol_tag_association_id'), table_name='kol_tag_association')
    op.drop_table('kol_tag_association')
    op.drop_index(op.f('ix_filter_kol_association_kol_id'), table_name='filter_kol_association')
    op.drop_index(op.f('ix_filter_kol_association_id'), table_name='filter_kol_association')
    op.drop_index(op.f('ix_filter_kol_association_filter_id'), table_name='filter_kol_association')
    op.drop_table('filter_kol_association')
    op.drop_index(op.f('ix_tag_name'), table_name='tag')
    op.drop_index(op.f('ix_tag_id'), table_name='tag')
    op.drop_table('tag')
    op.drop_index(op.f('ix_send_data_kol_id'), table_name='send_data')
    op.drop_index(op.f('ix_send_data_id'), table_name='send_data')
    op.drop_table('send_data')
    op.drop_index(op.f('ix_kol_info_username'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_updated_at'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_source'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_slug'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_platform'), table_name='kol_info')
    op.drop_index('ix_kol_info_most_used_hashtags', table_name='kol_info', postgresql_using='gin')
    op.drop_index(op.f('ix_kol_info_median_views_k'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_mean_views_k'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_likes_k'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_level'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_kol_name'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_kol_id'), table_name='kol_info')
    op.drop_index('ix_kol_info_keywords_ai', table_name='kol_info', postgresql_using='gin')
    op.drop_index(op.f('ix_kol_info_followers_k'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_engagement_rate'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_email'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_creator_id'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_created_at'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_average_views_k'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_average_likes_k'), table_name='kol_info')
    op.drop_index(op.f('ix_kol_info_average_comments_k'), table_name='kol_info')
    op.drop_table('kol_info')
    op.drop_index(op.f('ix_filter_data_id'), table_name='filter_data')
    op.drop_table('filter_data')
    op.drop_index(op.f('ix_collaboration_performance_project'), table_name='collaboration_performance')
    op.drop_index(op.f('ix_collaboration_performance_kol_id'), table_name='collaboration_performance')
    op.drop_index(op.f('ix_collaboration_performance_id'), table_name='collaboration_performance')
    op.drop_table('collaboration_performance')
    op.drop_index(op.f('ix_candidate_data_project'), table_name='candidate_data')
    op.drop_index(op.f('ix_candidate_data_label'), table_name='candidate_data')
    op.drop_index(op.f('ix_candidate_data_kol_name'), table_name='candidate_data')
    op.drop_index(op.f('ix_candidate_data_kol_id'), table_name='candidate_data')
    op.drop_index(op.f('ix_candidate_data_id'), table_name='candidate_data')
    op.drop_table('candidate_data')
    # ### end Alembic commands ###
