"""add_reply_email_to_candidate_data

Revision ID: a88b17950ae4
Revises: 9c64a311568c
Create Date: 2025-06-24 14:38:49.501708

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a88b17950ae4'
down_revision: Union[str, None] = '9c64a311568c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('candidate_data', sa.Column('reply_email', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('candidate_data', 'reply_email')
    # ### end Alembic commands ###
