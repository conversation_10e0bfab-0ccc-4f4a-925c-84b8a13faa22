"""add tag description

Revision ID: acf6eaf9f3c5
Revises: df3555ac4765
Create Date: 2025-06-11 15:35:50.793004

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text


# revision identifiers, used by Alembic.
revision: str = 'acf6eaf9f3c5'
down_revision: Union[str, None] = 'df3555ac4765'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema - add description column to tag table."""
    op.add_column(
        'tag',
        sa.Column('description', sa.Text, nullable=True, server_default='')
    )


def downgrade() -> None:
    """Downgrade schema - remove description column from tag table."""
    op.drop_column('tag', 'description')
