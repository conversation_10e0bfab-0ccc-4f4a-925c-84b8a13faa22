[tool.pytest]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]

[tool.pytest.ini_options]
pythonpath = [".", "app"]
testpaths = ["tests"]
log_level = "INFO"
log_cli = true
log_cli_level = "INFO"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = ["tests/*", "alembic/*"]

[tool.black]
line-length = 100
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
exclude = '''
(
  /(
      \.eggs         # exclude a few common directories in the
    | \.git          # root of the project
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
    | alembic
  )/
)
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
line_length = 100
skip = ["alembic"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
strict_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true

[[tool.mypy.overrides]]
module = [
    "fastapi.*",
    "sqlalchemy.*",
    "pydantic.*",
    "alembic.*",
    "pytest.*"
]
ignore_missing_imports = true

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.0"
pytest-asyncio = "^0.23.0"
httpx = "^0.28.0" 