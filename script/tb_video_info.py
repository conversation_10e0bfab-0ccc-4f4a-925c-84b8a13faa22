import csv
import os
import sys
from datetime import datetime
import json
from typing import Dict, List, Optional, Set

import pandas as pd
from sqlalchemy import create_engine, select, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, JSON, Boolean
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

# 数据库连接配置
DB_USER = "admin"
DB_PASSWORD = "autodb123456!A"
DB_HOST = "*************"
DB_PORT = "35432"
DB_NAME = "kol_db_v4"

# CSV文件路径
CSV_FILE_PATH_LIST = [
    "script/video_data_0.csv", 
    "script/video_data_1.csv",
    "script/video_data_2.csv",
    "script/video_data_3.csv",
    "script/video_data_4.csv",
    "script/video_data_5.csv",
    "script/video_data_6.csv",
    "script/video_data_7.csv",
    "script/video_data_8.csv",
    "script/video_data_9.csv",
    "script/video_data_10.csv",
    "script/video_data_11.csv",
    "script/video_data_12.csv"
    ]

# 创建数据库连接
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型
Base = declarative_base()

# 模型定义
class KOLInfo(Base):
    __tablename__ = "kol_info"

    kol_id = Column(String, primary_key=True, index=True)
    kol_name = Column(String, index=True, nullable=False)
    username = Column(String, index=True)
    email = Column(String, index=True)
    bio = Column(String)
    account_link = Column(String)
    followers_k = Column(Float, index=True)
    likes_k = Column(Float, index=True)
    platform = Column(String, index=True)
    source = Column(String, index=True)
    slug = Column(String, index=True)
    creator_id = Column(String, index=True)
    
    mean_views_k = Column(Float, index=True)
    median_views_k = Column(Float, index=True)
    engagement_rate = Column(Float, index=True)
    average_views_k = Column(Float, index=True)
    average_likes_k = Column(Float, index=True)
    average_comments_k = Column(Float, index=True)
    most_used_hashtags = Column(JSONB)
    level = Column(String, index=True)
    keywords_ai = Column(JSONB)
    
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now, onupdate=datetime.now)

class VideoInfo(Base):
    __tablename__ = "video_info"

    video_id = Column(String, primary_key=True, index=True)
    kol_id = Column(String, ForeignKey("kol_info.kol_id", ondelete="CASCADE"), index=True)
    
    # 视频基本信息
    play_count = Column(Integer)
    is_pinned = Column(Boolean, default=False)
    share_url = Column(String)
    desc = Column(String)
    desc_language = Column(String)
    video_url = Column(String)
    music_url = Column(String)
    
    # 互动数据
    likes_count = Column(Integer)
    comments_count = Column(Integer)
    shares_count = Column(Integer)
    collect_count = Column(Integer)
    
    # 平台信息
    platform = Column(String)
    
    # 标签和内容分析
    hashtags = Column(JSON)
    
    # 时间信息
    create_time = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now, onupdate=datetime.now)

def process_csv_data(session: Session, csv_file_path: str):
    """处理CSV数据并导入到数据库"""
    print(f"开始处理CSV文件: {csv_file_path}")
    
    # 获取已存在的video_id集合
    existing_video_ids = set()
    try:
        # 获取数据库中已存在的Video ID
        existing_ids = session.execute(select(VideoInfo.video_id)).scalars().all()
        existing_video_ids = set(existing_ids)
        print(f"数据库中已有 {len(existing_video_ids)} 条视频记录")
    except Exception as e:
        print(f"获取已存在Video ID时出错: {str(e)}")
    
    # 获取所有KOL ID，用于验证外键关系
    existing_kol_ids = set()
    try:
        kol_ids = session.execute(select(KOLInfo.kol_id)).scalars().all()
        existing_kol_ids = set(kol_ids)
        print(f"数据库中已有 {len(existing_kol_ids)} 条KOL记录")
    except Exception as e:
        print(f"获取KOL ID时出错: {str(e)}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(csv_file_path)
        print(f"CSV文件读取成功，共 {len(df)} 行")
        
        # 检查必要的列是否存在 - 根据实际CSV文件结构调整
        required_columns = ["video_id", "kol_id"]  # 最基本的必需列
        
        # 可选列映射 - 根据实际CSV文件的列名进行映射
        column_mapping = {
            "video_id": "video_id",
            "kol_id": "kol_id", 
            "play_count": "play_count",
            "is_pinned": "is_pinned",
            "share_url": "share_url",
            "desc": "desc",
            "desc_language": "desc_language",
            "video_url": "video_url",
            "music_url": "music_url",
            "likes_count": "likes_count",
            "comments_count": "comments_count",
            "shares_count": "shares_count",
            "collect_count": "collect_count",
            "platform": "platform",
            "hashtags": "hashtags",
            "create_time": "create_time"
        }
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"错误: CSV文件缺少必需列: {missing_columns}")
            print(f"CSV文件现有列: {list(df.columns)}")
            return
        
        # 批量处理参数
        batch_size = 1000
        total_records = len(df)
        total_batches = (total_records + batch_size - 1) // batch_size
        
        video_infos = []
        processed_video_ids = set()  # 用于跟踪已成功插入的Video ID
        skipped_count = 0  # 跳过的记录数
        
        # 处理每一行数据
        for index, row in df.iterrows():
            batch_num = index // batch_size + 1
            
            if index % 100 == 0:
                print(f"处理进度: {index}/{total_records} ({(index/total_records*100):.2f}%), 批次 {batch_num}/{total_batches}")
            
            # 获取video_id和kol_id
            video_id = str(row['video_id']) if not pd.isna(row['video_id']) else None
            kol_id = str(row['kol_id']) if not pd.isna(row['kol_id']) else None
            
            if not video_id or not kol_id:
                print(f"警告: 第 {index+1} 行缺少video_id或kol_id，跳过此行")
                skipped_count += 1
                continue
            
            # 检查避免重复
            if video_id in existing_video_ids:
                if index % 1000 == 0:
                    print(f"跳过已存在的Video ID: {video_id}")
                skipped_count += 1
                continue
            
            # 检查KOL是否存在
            if kol_id not in existing_kol_ids:
                print(f"警告: 第 {index+1} 行的KOL ID {kol_id} 不存在，跳过此行")
                skipped_count += 1
                continue
            
            # 处理时间字段
            create_time = None
            if 'create_time' in row and not pd.isna(row['create_time']):
                try:
                    create_time = pd.to_datetime(row['create_time'])
                except:
                    create_time = None
            
            # 处理JSON字段
            hashtags = process_json_field(row.get('hashtags', None))
            
            # 创建VideoInfo对象
            video_info = VideoInfo(
                video_id=video_id,
                kol_id=kol_id,
                play_count=safe_int(row.get('play_count', None)),
                is_pinned=safe_bool(row.get('is_pinned', False)),
                share_url=safe_str(row.get('share_url', None)),
                desc=safe_str(row.get('desc', None)),
                desc_language=safe_str(row.get('desc_language', None)),
                video_url=safe_str(row.get('video_url', None)),
                music_url=safe_str(row.get('music_url', None)),
                likes_count=safe_int(row.get('likes_count', None)),
                comments_count=safe_int(row.get('comments_count', None)),
                shares_count=safe_int(row.get('shares_count', None)),
                collect_count=safe_int(row.get('collect_count', None)),
                platform=safe_str(row.get('platform', 'tiktok')),  # 默认为tiktok
                hashtags=hashtags,
                create_time=create_time
            )
            
            video_infos.append(video_info)
            
            # 批量提交VideoInfo
            if (index + 1) % batch_size == 0 or index == total_records - 1:
                try:
                    # 插入视频信息并提交
                    session.add_all(video_infos)
                    session.commit()
                    
                    # 记录成功插入的Video ID
                    for video in video_infos:
                        processed_video_ids.add(video.video_id)
                        
                    print(f"批次 {batch_num}/{total_batches} 视频信息提交成功，已处理 {min(index+1, total_records)}/{total_records} 条记录")
                    
                    # 清空批次列表
                    video_infos = []
                except Exception as e:
                    session.rollback()
                    print(f"批次 {batch_num}/{total_batches} 视频信息提交失败: {str(e)}")
                    # 继续处理下一批
                    video_infos = []

        print(f"视频信息处理完成，共成功插入 {len(processed_video_ids)} 条记录，跳过 {skipped_count} 条记录")

    except Exception as e:
        print(f"处理CSV文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()

def safe_int(value):
    """安全转换为整数"""
    if pd.isna(value) or value is None:
        return None
    try:
        return int(float(value))  # 先转float再转int，处理小数点情况
    except (ValueError, TypeError):
        return None

def safe_str(value):
    """安全转换为字符串"""
    if pd.isna(value) or value is None:
        return None
    return str(value)

def safe_bool(value):
    """安全转换为布尔值"""
    if pd.isna(value) or value is None:
        return False
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ['true', '1', 'yes', 'on']
    try:
        return bool(int(value))
    except:
        return False

def process_json_field(value):
    """处理JSON字段，确保它是有效的JSON格式"""
    if pd.isna(value) or value is None:
        return None
    
    if isinstance(value, list):
        return value
    
    if isinstance(value, str):
        # 尝试解析JSON字符串
        try:
            # 处理可能的单引号JSON
            value = value.replace("'", '"')
            return json.loads(value)
        except json.JSONDecodeError:
            # 如果不是有效的JSON，尝试按逗号分隔
            try:
                items = [item.strip() for item in value.split(',')]
                return items
            except:
                return None
    
    return None

def main():
    """主函数"""
    # 获取CSV文件路径（可以从命令行参数传入）
    csv_file_path = CSV_FILE_PATH_LIST
    for csv_file_path in CSV_FILE_PATH_LIST:        
        if not os.path.exists(csv_file_path):
            print(f"错误: CSV文件不存在: {csv_file_path}")
            print(f"请确保CSV文件存在，或者通过命令行参数指定正确的文件路径")
            print(f"使用方法: python tb_video_info.py [csv_file_path]")
            return
        
        # 创建数据库会话
        session = SessionLocal()
        try:
            # 处理CSV数据
            process_csv_data(session, csv_file_path)
            print(f"######################## 处理完成: {csv_file_path}")
        finally:
            session.close()

if __name__ == "__main__":
    main()