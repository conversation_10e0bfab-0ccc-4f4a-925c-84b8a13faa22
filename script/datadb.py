from sqlalchemy import create_engine, text
import pandas as pd

# 连接配置
remote_db = create_engine('*****************************************************/kol_db_v2')
local_db = create_engine('postgresql://admin:autodb123456!A@localhost:5432/kol_db_v2')

# 分批处理video_info_v1
batch_size = 100000
total_records = 1278075  # 已知总记录数
for offset in range(0, total_records, batch_size):
    # 读取数据
    query = f"SELECT * FROM video_info_v1 ORDER BY video_id LIMIT {batch_size} OFFSET {offset}"
    df = pd.read_sql(query, remote_db)
    
    # 写入本地
    df.to_sql('video_info_v1', local_db, if_exists='append', index=False)
    print(f"Processed {offset+len(df)} of {total_records} records")

# 处理filter_data_v1（一次性）
filter_df = pd.read_sql("SELECT * FROM filter_data_v1", remote_db)
filter_df.to_sql('filter_data_v1', local_db, if_exists='append', index=False)


