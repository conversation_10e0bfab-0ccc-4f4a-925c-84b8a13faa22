import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 数据库连接配置
DB_USER = "linyq"
DB_PASSWORD = "12345678linyq"
DB_HOST = "127.0.0.1"
DB_PORT = "5432"
DB_NAME = "kol_db_v3"

# 创建数据库连接
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def clear_data(include_filter_data=False):
    """清除导入的数据"""
    session = SessionLocal()
    try:
        # 获取删除前的数据数量
        kol_count = session.execute(text("SELECT COUNT(*) FROM kol_info")).scalar()
        filter_assoc_count = session.execute(text("SELECT COUNT(*) FROM filter_kol_association")).scalar()
        tag_assoc_count = session.execute(text("SELECT COUNT(*) FROM kol_tag_association")).scalar()
        
        print(f"删除前数据统计:")
        print(f"- KOL信息: {kol_count} 条")
        print(f"- Filter关联: {filter_assoc_count} 条")
        print(f"- Tag关联: {tag_assoc_count} 条")
        
        # 按照依赖关系顺序删除
        session.execute(text("TRUNCATE kol_tag_association CASCADE"))
        session.execute(text("TRUNCATE filter_kol_association CASCADE"))
        session.execute(text("TRUNCATE kol_info CASCADE"))
        
        if include_filter_data:
            filter_count = session.execute(text("SELECT COUNT(*) FROM filter_data")).scalar()
            print(f"- Filter数据: {filter_count} 条")
            session.execute(text("TRUNCATE filter_data CASCADE"))
        
        session.commit()
        print("数据已成功删除!")
        
    except Exception as e:
        session.rollback()
        print(f"删除数据时出错: {str(e)}")
    finally:
        session.close()

if __name__ == "__main__":
    # 如果命令行参数包含 --all，则同时删除filter_data
    include_filter_data = "--all" in sys.argv
    clear_data(include_filter_data)
    
    if include_filter_data:
        print("已删除所有数据，包括filter_data")
    else:
        print("已删除KOL及关联数据，保留filter_data") 