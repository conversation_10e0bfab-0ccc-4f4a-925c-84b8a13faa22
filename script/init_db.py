#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库初始化脚本
用于初始化数据库结构和基础数据
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 添加项目根目录到 Python 路径
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(BASE_DIR))

from sqlalchemy.exc import SQLAlchemyError
from app.core.config import settings
from app.db.session import engine, SessionLocal
from app.models import Tag, ProjectTagAssociation

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_migrations():
    """执行数据库迁移"""
    logger.info("开始执行数据库迁移...")
    try:
        # 使用 alembic 执行迁移
        alembic_cmd = "alembic upgrade head"
        result = subprocess.run(alembic_cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("数据库迁移成功完成")
            logger.debug(result.stdout)
        else:
            logger.error(f"数据库迁移失败: {result.stderr}")
            return False
        return True
    except Exception as e:
        logger.error(f"执行迁移时发生错误: {str(e)}")
        return False


def init_base_data():
    """初始化基础数据"""
    logger.info("开始初始化基础数据...")
    db = SessionLocal()
    try:
        # 检查是否已有标签数据
        existing_tags = db.query(Tag).count()
        if existing_tags > 0:
            logger.info(f"数据库中已存在 {existing_tags} 个标签")
            user_input = input("是否清除现有标签并重新初始化? (y/n): ")
            if user_input.lower() != 'y':
                logger.info("跳过标签初始化")
                return True
            
            # 删除现有标签关联和标签
            db.query(ProjectTagAssociation).delete()
            db.query(Tag).delete()
            db.commit()
            logger.info("已删除现有标签数据")
        
        # 创建指定的三个标签及其项目关联
        tags_with_projects = [
            {"name": "ACG", "project_code": "OOG105"},
            {"name": "Yoga&Pilates", "project_code": "OOG120"},
            {"name": "Lifestyle", "project_code": "NOTM201"},
        ]
        
        # 创建标签并记录其ID
        tag_id_map = {}
        for tag_info in tags_with_projects:
            new_tag = Tag(name=tag_info["name"])
            db.add(new_tag)
            db.flush()  # 刷新会话以获取新生成的ID
            tag_id_map[new_tag.name] = new_tag.id
            logger.info(f"创建标签: {new_tag.name} (ID: {new_tag.id})")
        
        # 创建项目标签关联
        for tag_info in tags_with_projects:
            tag_name = tag_info["name"]
            project_code = tag_info["project_code"]
            project_tag_assoc = ProjectTagAssociation(
                project_code=project_code,
                tag_id=tag_id_map[tag_name]
            )
            db.add(project_tag_assoc)
            logger.info(f"创建项目标签关联: {project_code} <-> {tag_name}")
        
        db.commit()
        logger.info(f"成功创建 {len(tags_with_projects)} 个标签及其项目关联")
        return True
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"初始化基础数据时发生SQL错误: {str(e)}")
        return False
    except Exception as e:
        db.rollback()
        logger.error(f"初始化基础数据时发生错误: {str(e)}")
        return False
    finally:
        db.close()


def main():
    """主函数"""
    logger.info("=== 数据库初始化开始 ===")
    logger.info(f"数据库连接: {settings.DATABASE_URI}")
    
    # 执行迁移
    if not run_migrations():
        logger.error("数据库迁移失败，初始化终止")
        return
    
    # 初始化基础数据
    if not init_base_data():
        logger.error("基础数据初始化失败")
        return
    
    logger.info("=== 数据库初始化完成 ===")


if __name__ == "__main__":
    main() 