#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库迁移创建脚本
用于简化创建新的数据库迁移文件的过程
"""

import argparse
import subprocess
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(BASE_DIR))


def create_migration(message: str, empty: bool = False):
    """
    创建新的数据库迁移文件
    
    Args:
        message: 迁移的说明信息
        empty: 是否创建空的迁移文件，默认为False（自动生成）
    """
    # 确保message不为空
    if not message:
        print("错误: 必须提供迁移说明信息")
        sys.exit(1)
    
    # 构建alembic命令
    cmd = ["alembic", "revision"]
    if not empty:
        cmd.append("--autogenerate")
    cmd.extend(["-m", message])
    
    # 执行命令
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(result.stdout)
            print("迁移文件创建成功!")
        else:
            print(f"错误: {result.stderr}")
            sys.exit(1)
    except Exception as e:
        print(f"执行命令时发生错误: {str(e)}")
        sys.exit(1)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="创建新的数据库迁移文件")
    parser.add_argument(
        "message",
        help="迁移的说明信息，例如: '添加用户表'"
    )
    parser.add_argument(
        "--empty",
        "-e",
        action="store_true",
        help="创建空的迁移文件（不自动检测模型变化）"
    )
    
    args = parser.parse_args()
    create_migration(args.message, args.empty)


if __name__ == "__main__":
    main() 