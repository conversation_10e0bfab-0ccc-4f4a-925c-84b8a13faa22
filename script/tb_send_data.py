import csv
import os
import sys
from datetime import datetime
import json
from typing import Dict, List, Optional, Set

import pandas as pd
from sqlalchemy import create_engine, select, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, JSON, Boolean
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

# 数据库连接配置
DB_USER = "linyq"
DB_PASSWORD = "12345678linyq"
DB_HOST = "127.0.0.1"
DB_PORT = "5432"
DB_NAME = "kol_db_v4"

# CSV文件路径
CSV_FILE_PATH = "script/send_data.csv"

# 创建数据库连接
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型
Base = declarative_base()

# 模型定义
class SendData(Base):
    """发送数据表，记录消息发送状态和相关信息"""
    __tablename__ = "send_data"

    id = Column(String, primary_key=True, index=True)  # 消息唯一标识
    kol_id = Column(String, index=True, nullable=False)  # KOL ID（非外键）
    send_status = Column(String)  # 发送状态
    platform = Column(String)  # 发送平台
    send_date = Column(DateTime(timezone=True))  # 发送日期
    export_date = Column(DateTime(timezone=True))  # 导出日期
    
    # 新增字段
    app_code = Column(String)  # 应用代码 / project_code
    template_id = Column(String)  # 模板ID
    read_status = Column(Boolean, default=False)  # 阅读状态
    success = Column(Boolean, default=False)  # 发送成功状态
    from_email = Column(String)      # 发送者邮箱
    to_email = Column(String)        # 接收者邮箱

    # 时间信息
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now)  # 创建时间
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now, onupdate=datetime.now)  # 更新时间

def process_csv_data(session: Session, csv_file_path: str):
    """处理CSV数据并导入到数据库"""
    print(f"开始处理CSV文件: {csv_file_path}")
    
    # 获取已存在的send_data id集合
    existing_send_ids = set()
    try:
        # 获取数据库中已存在的Send Data ID
        existing_ids = session.execute(select(SendData.id)).scalars().all()
        existing_send_ids = set(existing_ids)
        print(f"数据库中已有 {len(existing_send_ids)} 条发送记录")
    except Exception as e:
        print(f"获取已存在Send Data ID时出错: {str(e)}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(csv_file_path)
        print(f"CSV文件读取成功，共 {len(df)} 行")
        
        # 检查必要的列是否存在 - 根据实际CSV文件结构调整
        required_columns = ["id", "kol_id"]  # 最基本的必需列
        
        # CSV列名映射
        csv_columns = list(df.columns)
        print(f"CSV文件现有列: {csv_columns}")
        
        # 根据CSV文件的实际列名进行映射
        expected_columns = [
            "id", "kol_id", "send_status", "platform", "send_date", "export_date",
            "app_code", "template_id", "read_status", "success", "from_email", "to_email",
            "created_at", "updated_at"
        ]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"错误: CSV文件缺少必需列: {missing_columns}")
            print(f"CSV文件现有列: {list(df.columns)}")
            return
        
        # 批量处理参数
        batch_size = 1000
        total_records = len(df)
        total_batches = (total_records + batch_size - 1) // batch_size
        
        send_data_list = []
        processed_send_ids = set()  # 用于跟踪已成功插入的Send Data ID
        skipped_count = 0  # 跳过的记录数
        
        # 处理每一行数据
        for index, row in df.iterrows():
            batch_num = index // batch_size + 1
            
            if index % 100 == 0:
                print(f"处理进度: {index}/{total_records} ({(index/total_records*100):.2f}%), 批次 {batch_num}/{total_batches}")
            
            # 获取id和kol_id
            send_id = str(row['id']) if not pd.isna(row['id']) else None
            kol_id = str(row['kol_id']) if not pd.isna(row['kol_id']) else None
            
            if not send_id or not kol_id:
                print(f"警告: 第 {index+1} 行缺少id或kol_id，跳过此行")
                skipped_count += 1
                continue
            
            # 检查避免重复
            if send_id in existing_send_ids:
                if index % 1000 == 0:
                    print(f"跳过已存在的Send Data ID: {send_id}")
                skipped_count += 1
                continue
            
            # 处理时间字段
            send_date = None
            if 'send_date' in row and not pd.isna(row['send_date']):
                try:
                    send_date = pd.to_datetime(row['send_date'])
                except:
                    send_date = None
            
            export_date = None
            if 'export_date' in row and not pd.isna(row['export_date']):
                try:
                    export_date = pd.to_datetime(row['export_date'])
                except:
                    export_date = None
            
            created_at = None
            if 'created_at' in row and not pd.isna(row['created_at']):
                try:
                    created_at = pd.to_datetime(row['created_at'])
                except:
                    created_at = datetime.now()
            else:
                created_at = datetime.now()
            
            updated_at = None
            if 'updated_at' in row and not pd.isna(row['updated_at']):
                try:
                    updated_at = pd.to_datetime(row['updated_at'])
                except:
                    updated_at = datetime.now()
            else:
                updated_at = datetime.now()
            
            # 创建SendData对象
            send_data = SendData(
                id=send_id,
                kol_id=kol_id,
                send_status=safe_str(row.get('send_status', None)),
                platform=safe_str(row.get('platform', None)),
                send_date=send_date,
                export_date=export_date,
                app_code=safe_str(row.get('app_code', None)),
                template_id=safe_str(row.get('template_id', None)),
                read_status=safe_bool(row.get('read_status', False)),
                success=safe_bool(row.get('success', False)),
                from_email=safe_str(row.get('from_email', None)),
                to_email=safe_str(row.get('to_email', None)),
                created_at=created_at,
                updated_at=updated_at
            )
            
            send_data_list.append(send_data)
            
            # 批量提交SendData
            if (index + 1) % batch_size == 0 or index == total_records - 1:
                try:
                    # 插入发送数据并提交
                    session.add_all(send_data_list)
                    session.commit()
                    
                    # 记录成功插入的Send Data ID
                    for send_data in send_data_list:
                        processed_send_ids.add(send_data.id)
                        
                    print(f"批次 {batch_num}/{total_batches} 发送数据提交成功，已处理 {min(index+1, total_records)}/{total_records} 条记录")
                    
                    # 清空批次列表
                    send_data_list = []
                except Exception as e:
                    session.rollback()
                    print(f"批次 {batch_num}/{total_batches} 发送数据提交失败: {str(e)}")
                    # 继续处理下一批
                    send_data_list = []

        print(f"发送数据处理完成，共成功插入 {len(processed_send_ids)} 条记录，跳过 {skipped_count} 条记录")

    except Exception as e:
        print(f"处理CSV文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()

def safe_int(value):
    """安全转换为整数"""
    if pd.isna(value) or value is None:
        return None
    try:
        return int(float(value))  # 先转float再转int，处理小数点情况
    except (ValueError, TypeError):
        return None

def safe_str(value):
    """安全转换为字符串"""
    if pd.isna(value) or value is None:
        return None
    return str(value)

def safe_bool(value):
    """安全转换为布尔值"""
    if pd.isna(value) or value is None:
        return False
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ['true', '1', 'yes', 'on', 't']
    try:
        return bool(int(value))
    except:
        return False

def main():
    """主函数"""
    # 获取CSV文件路径（可以从命令行参数传入）
    csv_file_path = CSV_FILE_PATH
    if len(sys.argv) > 1:
        csv_file_path = sys.argv[1]
    
    if not os.path.exists(csv_file_path):
        print(f"错误: CSV文件不存在: {csv_file_path}")
        print(f"请确保CSV文件存在，或者通过命令行参数指定正确的文件路径")
        print(f"使用方法: python tb_send_data.py [csv_file_path]")
        return
    
    # 创建数据库会话
    session = SessionLocal()
    try:
        # 处理CSV数据
        process_csv_data(session, csv_file_path)
    finally:
        session.close()

if __name__ == "__main__":
    main()