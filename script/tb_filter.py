import pandas as pd
from sqlalchemy import create_engine
from datetime import datetime

# 读取CSV文件
df = pd.read_csv('/Users/<USER>/Desktop/home/<USER>/api/script/filter_data.csv')

# 定义函数处理时间戳列
def fix_timestamp(ts):
    try:
        # 尝试解析日期
        if pd.isna(ts) or ts == "":
            return None
        if isinstance(ts, str) and not ts[0].isdigit():
            return None
        return pd.to_datetime(ts)
    except:
        return None

# 处理时间戳列
df['created_at'] = df['created_at'].apply(fix_timestamp)
df['updated_at'] = df['updated_at'].apply(fix_timestamp)

# 连接数据库
engine = create_engine('postgresql://linyq:12345678linyq@127.0.0.1:5432/kol_db_v4')

# 写入数据库
df.to_sql('filter_data', engine, if_exists='append', index=False)
