from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
import logging
from tqdm import tqdm
import json
import time
from typing import Optional
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库连接配置
SOURCE_DB_URL = "*****************************************************/kol_db"
TARGET_DB_URL = "*****************************************************/kol_db_v2"

# 创建引擎（添加连接池和超时设置）
source_engine = create_engine(
    SOURCE_DB_URL,
    pool_size=5,
    max_overflow=10,
    pool_timeout=30,
    pool_recycle=1800,
    connect_args={'connect_timeout': 60}
)

target_engine = create_engine(
    TARGET_DB_URL,
    pool_size=5,
    max_overflow=10,
    pool_timeout=30,
    pool_recycle=1800,
    connect_args={'connect_timeout': 60}
)

# 创建会话工厂
SourceSession = sessionmaker(bind=source_engine)
TargetSession = sessionmaker(bind=target_engine)

# 进度文件
PROGRESS_FILE = "migration_progress.json"

def load_progress() -> tuple[int, set[str]]:
    """加载迁移进度"""
    if os.path.exists(PROGRESS_FILE):
        with open(PROGRESS_FILE, 'r') as f:
            data = json.load(f)
            return data.get('offset', 0), set(data.get('processed_kols', []))
    return 0, set()

def save_progress(offset: int, processed_kols: set[str]):
    """保存迁移进度"""
    with open(PROGRESS_FILE, 'w') as f:
        json.dump({
            'offset': offset,
            'processed_kols': list(processed_kols)
        }, f)

@contextmanager
def session_scope(Session):
    """提供事务范围的会话"""
    session = Session()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"发生错误: {e}")
        raise
    finally:
        session.close()

def retry_on_error(func, max_retries=3, delay=5):
    """带重试机制的函数执行器"""
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            logger.warning(f"操作失败，{delay}秒后重试: {str(e)}")
            time.sleep(delay)

def migrate_data():
    # 加载之前的进度
    offset, processed_kols = load_progress()
    
    # 步骤1: 在目标数据库创建新的filter_data记录
    def get_or_create_filter():
        with session_scope(TargetSession) as target_session:
            existing_filter = target_session.execute(
                text("SELECT id FROM filter_data_v1 WHERE project_code = :project_code"),
                {"project_code": "NOTM201"}
            ).fetchone()
            
            if existing_filter:
                return existing_filter[0]
            
            result = target_session.execute(
                text("INSERT INTO filter_data_v1 (project_code, created_at, updated_at) VALUES (:project_code, NOW(), NOW()) RETURNING id"),
                {"project_code": "NOTM201"}
            )
            return result.fetchone()[0]

    filter_id = retry_on_error(get_or_create_filter)
    logger.info(f"Filter ID: {filter_id}")
    
    # 步骤2: 获取源数据库中的KOL总数
    def get_total_kols():
        with session_scope(SourceSession) as source_session:
            return source_session.execute(text("SELECT COUNT(*) FROM kol_info_v1")).scalar()

    total_kols = retry_on_error(get_total_kols)
    logger.info(f"源数据库中共有 {total_kols} 条KOL记录")
    
    # 步骤3: 批量迁移数据
    batch_size = 500  # 减小批处理大小
    
    with tqdm(total=total_kols, initial=offset, desc="迁移进度") as pbar:
        while offset < total_kols:
            try:
                # 3.1 读取一批源数据
                def fetch_batch():
                    with session_scope(SourceSession) as source_session:
                        return source_session.execute(
                            text("SELECT * FROM kol_info_v1 ORDER BY kol_id LIMIT :limit OFFSET :offset"),
                            {"limit": batch_size, "offset": offset}
                        ).fetchall()

                kols = retry_on_error(fetch_batch)
                
                if not kols:
                    break
                
                # 3.2 将数据写入目标数据库
                with session_scope(TargetSession) as target_session:
                    for kol in kols:
                        if kol.kol_id in processed_kols:
                            continue
                            
                        # 检查目标数据库是否已存在该KOL
                        existing = target_session.execute(
                            text("SELECT 1 FROM kol_info_v1 WHERE kol_id = :kol_id"),
                            {"kol_id": kol.kol_id}
                        ).fetchone()
                        
                        if not existing:
                            # 构建插入KOL的SQL语句（排除filter_id字段）
                            columns = [col for col in kol._mapping.keys() if col != 'filter_id']
                            placeholders = [f":{col}" for col in columns]
                            
                            # 插入KOL数据
                            kol_data = {col: kol._mapping[col] for col in columns}
                            
                            # 将列表类型转换为JSON字符串
                            if 'keywords_ai' in kol_data and kol_data['keywords_ai'] is not None:
                                kol_data['keywords_ai'] = json.dumps(kol_data['keywords_ai'])
                            if 'most_used_hashtags' in kol_data and kol_data['most_used_hashtags'] is not None:
                                kol_data['most_used_hashtags'] = json.dumps(kol_data['most_used_hashtags'])
                                
                            target_session.execute(
                                text(f"INSERT INTO kol_info_v1 ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"),
                                kol_data
                            )
                        
                        # 检查并创建多对多关联
                        existing_association = target_session.execute(
                            text("""
                            SELECT 1 FROM filter_kol_association_v1 
                            WHERE filter_id = :filter_id AND kol_id = :kol_id
                            """),
                            {"filter_id": filter_id, "kol_id": kol.kol_id}
                        ).fetchone()
                        
                        if not existing_association:
                            target_session.execute(
                                text("""
                                INSERT INTO filter_kol_association_v1 
                                (filter_id, kol_id, project_code, created_at, updated_at)
                                VALUES (:filter_id, :kol_id, :project_code, NOW(), NOW())
                                """),
                                {
                                    "filter_id": filter_id,
                                    "kol_id": kol.kol_id,
                                    "project_code": "NOTM201"
                                }
                            )
                        
                        processed_kols.add(kol.kol_id)
                
                # 更新进度
                offset += len(kols)
                pbar.update(len(kols))
                
                # 每批次完成后保存进度
                save_progress(offset, processed_kols)
                
            except Exception as e:
                logger.error(f"处理批次时发生错误: {e}")
                # 保存当前进度
                save_progress(offset, processed_kols)
                raise
    
    logger.info("数据迁移完成！")

if __name__ == "__main__":
    try:
        migrate_data()
    except KeyboardInterrupt:
        logger.info("迁移被用户中断")
    except Exception as e:
        logger.error(f"迁移过程中发生错误: {e}")