import csv
import os
import sys
from datetime import datetime
import json
from typing import Dict, List, Optional, Set
import re
import math

import pandas as pd
import numpy as np
from sqlalchemy import create_engine, select, text, BigInteger
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, JSON, Boolean
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

# 数据库连接配置
# DB_USER = "admin"
# DB_PASSWORD = "autodb123456!A"
# DB_HOST = "*************"
# DB_PORT = "35432"
# DB_NAME = "kol_db_v4"

DB_USER = "linyq"
DB_PASSWORD = "12345678linyq"
DB_HOST = "127.0.0.1"
DB_PORT = "5432"
DB_NAME = "kol_db_v4"

# CSV文件路径
CSV_FILE_PATH = "script/KOL-Performance.csv"

# 创建数据库连接
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型
Base = declarative_base()

# 模型定义
class CollaborationPerformance(Base):
    """KOL合作绩效表"""
    __tablename__ = "collaboration_performance"

    id = Column(Integer, primary_key=True, index=True)  # 自增主键
    kol_id = Column(String, nullable=False, index=True)  # KOL ID
    project = Column(String, nullable=True)  # 项目名称
    kol_priority = Column(String, nullable=True)  # KOL优先级
    post_link = Column(String, nullable=True)  # 帖子链接
    payment = Column(String, nullable=True)  # 支付金额
    post_date = Column(DateTime(timezone=True), nullable=True)  # 发布日期
    
    # 使用BigInteger替代Integer处理大数值
    # 总计数据
    views_total = Column(BigInteger, nullable=True)  # 总浏览量
    likes_total = Column(BigInteger, nullable=True)  # 总点赞数
    comments_total = Column(BigInteger, nullable=True)  # 总评论数
    shares_total = Column(BigInteger, nullable=True)  # 总分享数
    
    # 第1天数据
    views_day1 = Column(BigInteger, nullable=True)  # 第1天浏览量
    likes_day1 = Column(BigInteger, nullable=True)  # 第1天点赞数
    comments_day1 = Column(BigInteger, nullable=True)  # 第1天评论数
    shares_day1 = Column(BigInteger, nullable=True)  # 第1天分享数
    
    # 第3天数据
    views_day3 = Column(BigInteger, nullable=True)  # 第3天浏览量
    likes_day3 = Column(BigInteger, nullable=True)  # 第3天点赞数
    comments_day3 = Column(BigInteger, nullable=True)  # 第3天评论数
    shares_day3 = Column(BigInteger, nullable=True)  # 第3天分享数
    
    # 第7天数据
    views_day7 = Column(BigInteger, nullable=True)  # 第7天浏览量
    likes_day7 = Column(BigInteger, nullable=True)  # 第7天点赞数
    comments_day7 = Column(BigInteger, nullable=True)  # 第7天评论数
    shares_day7 = Column(BigInteger, nullable=True)  # 第7天分享数
    
    # 其他指标
    engagement_rate = Column(Float, nullable=True)  # 互动率
    cpm = Column(Float, nullable=True)  # CPM
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now)  # 创建时间
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now, onupdate=datetime.now)  # 更新时间

def is_nan_or_none(value):
    """检查值是否为NaN或None"""
    if value is None:
        return True
    if isinstance(value, float) and math.isnan(value):
        return True
    if pd.isna(value):
        return True
    return False

def safe_int(value):
    """安全转换为整数"""
    if is_nan_or_none(value):
        return None
    try:
        return int(float(value))  # 先转float再转int，处理小数点情况
    except (ValueError, TypeError):
        return None

def safe_float(value):
    """安全转换为浮点数"""
    if is_nan_or_none(value):
        return None
    try:
        if isinstance(value, str) and '%' in value:
            # 处理百分比字符串
            return float(value.replace('%', '')) / 100
        return float(value)
    except (ValueError, TypeError):
        return None

def safe_str(value):
    """安全转换为字符串"""
    if is_nan_or_none(value):
        return None
    return str(value)

def add_tk_prefix(kol_id):
    """为KOL ID添加TK_前缀（如果没有的话）"""
    if is_nan_or_none(kol_id):
        return None
    
    kol_id_str = str(kol_id).strip()
    # 如果已经有TK_前缀，则不添加
    if kol_id_str.startswith('TK_'):
        return kol_id_str
    # 否则添加TK_前缀
    return f"TK_{kol_id_str}"

def clean_record(record):
    """清理记录中的NaN值"""
    cleaned = {}
    for key, value in record.items():
        if is_nan_or_none(value):
            cleaned[key] = None
        else:
            cleaned[key] = value
    return cleaned

def process_csv_data(session: Session, csv_file_path: str):
    """处理CSV数据并导入到数据库"""
    print(f"开始处理CSV文件: {csv_file_path}")
    
    # 读取CSV文件
    try:
        # 设置所有空值为NaN
        df = pd.read_csv(csv_file_path, na_values=['', 'nan', 'NaN', 'null', 'NULL'])
        print(f"CSV文件读取成功，共 {len(df)} 行")
        
        # 列名映射 (CSV列名 -> 数据库列名)
        column_mapping = {
            'KOL ID': 'kol_id',
            'Project': 'project',
            'KOL Priority': 'kol_priority',
            'Post Link': 'post_link',
            'Payment': 'payment',
            'Post Date': 'post_date',
            'Views-Total': 'views_total',
            'Likes-Total': 'likes_total',
            'Comments-Total': 'comments_total',
            'Shares-Total': 'shares_total',
            'Views-Day 1': 'views_day1',
            'Likes-Day 1': 'likes_day1',
            'Comments-Day 1': 'comments_day1',
            'Shares-Day 1': 'shares_day1',
            'Views-Day 3': 'views_day3',
            'Likes-Day 3': 'likes_day3',
            'Comments-Day 3': 'comments_day3',
            'Shares-Day 3': 'shares_day3',
            'Views-Day 7': 'views_day7',
            'Likes-Day 7': 'likes_day7',
            'Comments-Day 7': 'comments_day7',
            'Shares-Day 7': 'shares_day7',
            'CPM': 'cpm',
            'Engagement Rate of Post': 'engagement_rate'
        }
        
        # 重命名列名以匹配数据库
        renamed_columns = {}
        for csv_col, db_col in column_mapping.items():
            if csv_col in df.columns:
                renamed_columns[csv_col] = db_col
        
        df = df.rename(columns=renamed_columns)
        
        # 确保必要的列存在
        required_columns = ['kol_id']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"错误: CSV文件缺少必需列: {missing_columns}")
            print(f"CSV文件现有列: {list(df.columns)}")
            return
        
        # 数据预处理
        
        # 处理日期字段
        if 'post_date' in df.columns:
            df['post_date'] = pd.to_datetime(df['post_date'], format='%Y/%m/%d', errors='coerce')
        
        # 处理百分比字段 (Engagement Rate)
        if 'engagement_rate' in df.columns:
            df['engagement_rate'] = df['engagement_rate'].apply(lambda x: 
                                                              float(str(x).replace('%', '')) / 100 
                                                              if pd.notna(x) and str(x).strip() and '%' in str(x) 
                                                              else x)
        
        # 添加时间戳字段
        now = datetime.now()
        df['created_at'] = now
        df['updated_at'] = now
        
        # 将所有NaN值替换为None（对整个DataFrame操作）
        df = df.replace({np.nan: None})
        
        # 批量处理参数
        batch_size = 100
        total_records = len(df)
        total_batches = (total_records + batch_size - 1) // batch_size
        
        skipped_count = 0  # 跳过的记录数
        inserted_count = 0  # 插入的记录数
        
        # 整数字段列表
        int_columns = [
            'views_total', 'likes_total', 'comments_total', 'shares_total',
            'views_day1', 'likes_day1', 'comments_day1', 'shares_day1',
            'views_day3', 'likes_day3', 'comments_day3', 'shares_day3',
            'views_day7', 'likes_day7', 'comments_day7', 'shares_day7'
        ]
        
        # 浮点数字段列表
        float_columns = ['engagement_rate', 'cpm']
        
        # 处理每一批数据
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min((batch_num + 1) * batch_size, total_records)
            
            print(f"处理批次 {batch_num+1}/{total_batches}，记录 {start_idx+1}-{end_idx}/{total_records}")
            
            batch_df = df.iloc[start_idx:end_idx].copy()
            
            # 预处理数据类型
            for col in int_columns:
                if col in batch_df.columns:
                    batch_df[col] = batch_df[col].apply(safe_int)
                    
            for col in float_columns:
                if col in batch_df.columns:
                    batch_df[col] = batch_df[col].apply(safe_float)
            
            # 为kol_id添加TK_前缀
            if 'kol_id' in batch_df.columns:
                # 记录添加前缀前的kol_id样本（最多显示5个）
                sample_before = batch_df['kol_id'].dropna().head(5).tolist()
                
                # 添加TK_前缀
                batch_df['kol_id'] = batch_df['kol_id'].apply(add_tk_prefix)
                
                # 记录添加前缀后的kol_id样本
                sample_after = batch_df['kol_id'].dropna().head(5).tolist()
                print(f"KOL ID添加TK_前缀示例: 前: {sample_before} -> 后: {sample_after}")
            
            # 将DataFrame转换为字典列表
            batch_records = batch_df.to_dict('records')
            
            # 额外清理每个记录中的NaN值
            cleaned_records = [clean_record(record) for record in batch_records]
            
            perf_objects = []
            for record in cleaned_records:
                # 确保kol_id存在
                if not record.get('kol_id'):
                    skipped_count += 1
                    continue
                
                # 过滤掉没有在表模型中定义的列
                filtered_record = {k: v for k, v in record.items() 
                                 if hasattr(CollaborationPerformance, k)}
                
                try:
                    # 创建性能对象 (不包含id，让数据库自动生成)
                    perf_obj = CollaborationPerformance(**filtered_record)
                    perf_objects.append(perf_obj)
                except Exception as e:
                    print(f"创建对象失败: {str(e)}")
                    print(f"问题记录: {filtered_record}")
                    skipped_count += 1
            
            try:
                # 批量插入记录
                if perf_objects:
                    session.add_all(perf_objects)
                    session.commit()
                    inserted_count += len(perf_objects)
                    print(f"批次 {batch_num+1}/{total_batches} 成功插入 {len(perf_objects)} 条记录")
            except Exception as e:
                session.rollback()
                print(f"批次 {batch_num+1}/{total_batches} 插入失败: {str(e)}")
                # 尝试逐条插入，跳过出错的记录
                for perf_obj in perf_objects:
                    try:
                        # 确保对象中没有NaN值
                        obj_dict = {k: v for k, v in perf_obj.__dict__.items() if not k.startswith('_')}
                        for k, v in obj_dict.items():
                            if isinstance(v, float) and math.isnan(v):
                                setattr(perf_obj, k, None)
                        
                        session.add(perf_obj)
                        session.commit()
                        inserted_count += 1
                    except Exception as inner_e:
                        session.rollback()
                        print(f"单条记录插入失败: {str(inner_e)}")
                        # 输出更详细的错误信息
                        error_record = {k: v for k, v in perf_obj.__dict__.items() if not k.startswith('_')}
                        print(f"记录详情: {error_record}")
                        skipped_count += 1
        
        print(f"数据导入完成: 成功插入 {inserted_count} 条记录，跳过 {skipped_count} 条记录")
        
    except Exception as e:
        print(f"处理CSV文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    # 获取CSV文件路径（可以从命令行参数传入）
    csv_file_path = CSV_FILE_PATH
    if len(sys.argv) > 1:
        csv_file_path = sys.argv[1]
    
    if not os.path.exists(csv_file_path):
        print(f"错误: CSV文件不存在: {csv_file_path}")
        print(f"请确保CSV文件存在，或者通过命令行参数指定正确的文件路径")
        print(f"使用方法: python tb_collaboration_performance.py [csv_file_path]")
        return
    
    print("注意: 本脚本会自动为所有KOL ID添加TK_前缀（如果尚未添加）")
    
    # 创建数据库会话
    session = SessionLocal()
    try:
        # 处理CSV数据
        process_csv_data(session, csv_file_path)
    finally:
        session.close()

if __name__ == "__main__":
    main()