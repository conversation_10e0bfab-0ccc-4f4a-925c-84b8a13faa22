import csv
import os
import sys
from datetime import datetime
import json
from typing import Dict, List, Optional, Set

import pandas as pd
from sqlalchemy import create_engine, select, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Float, DateTime, Foreign<PERSON>ey, JSON, Boolean
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

# 数据库连接配置
DB_USER = "admin"
DB_PASSWORD = "autodb123456!A"
DB_HOST = "*************"
DB_PORT = "35432"
DB_NAME = "kol_db_v4"

QZ = "INS_"
PLATFORM = "instagram"

# CSV文件路径
# CSV_FILE_PATH = "script/merged_oog_ded.csv"
# PROJECT_CODE = "OOG120"
CSV_FILE_PATH = "script/Instagram_1.csv"
PROJECT_CODE = "NOTM201"
# CSV_FILE_PATH = "script/105.csv"
# PROJECT_CODE = "NOTM105"
# CSV_FILE_PATH = "script/105_youtube.csv"
# PROJECT_CODE = "NOTM105"
# 创建数据库连接
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型
Base = declarative_base()

# 模型定义
class KOLInfo(Base):
    __tablename__ = "kol_info"

    kol_id = Column(String, primary_key=True, index=True)
    kol_name = Column(String, index=True, nullable=False)
    username = Column(String, index=True)
    email = Column(String, index=True)
    bio = Column(String)
    account_link = Column(String)
    followers_k = Column(Float, index=True)
    likes_k = Column(Float, index=True)
    platform = Column(String, index=True)
    source = Column(String, index=True)
    slug = Column(String, index=True)
    creator_id = Column(String, index=True)
    
    mean_views_k = Column(Float, index=True)
    median_views_k = Column(Float, index=True)
    engagement_rate = Column(Float, index=True)
    average_views_k = Column(Float, index=True)
    average_likes_k = Column(Float, index=True)
    average_comments_k = Column(Float, index=True)
    most_used_hashtags = Column(JSONB)
    level = Column(String, index=True)
    keywords_ai = Column(JSONB)
    
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now, onupdate=datetime.now)

class FilterData(Base):
    __tablename__ = "filter_data"

    id = Column(Integer, primary_key=True, index=True)
    language = Column(String)
    gender = Column(String)
    location = Column(String)
    filter_body = Column(JSON)
    filter_name = Column(String)
    project_code = Column(String)
    
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now, onupdate=datetime.now)

class FilterKolAssociation(Base):
    __tablename__ = "filter_kol_association"

    id = Column(Integer, primary_key=True, index=True)
    filter_id = Column(Integer, ForeignKey("filter_data.id", ondelete="CASCADE"), index=True)
    kol_id = Column(String, ForeignKey("kol_info.kol_id", ondelete="CASCADE"), index=True)
    project_code = Column(String)
    
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now, onupdate=datetime.now)

class Tag(Base):
    __tablename__ = "tag"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    description = Column(String)
    
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now, onupdate=datetime.now)

class KolTagAssociation(Base):
    __tablename__ = "kol_tag_association"

    id = Column(Integer, primary_key=True, index=True)
    kol_id = Column(String, ForeignKey("kol_info.kol_id", ondelete="CASCADE"), index=True)
    tag_id = Column(Integer, ForeignKey("tag.id", ondelete="CASCADE"), index=True)
    project_code = Column(String, index=True)
    
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now, onupdate=datetime.now)

def process_csv_data(session: Session, csv_file_path: str):
    """处理CSV数据并导入到数据库"""
    print(f"开始处理CSV文件: {csv_file_path}")
    
    # 获取已存在的kol_id集合
    existing_kol_ids = set()
    try:
        # 获取数据库中已存在的KOL ID
        existing_ids = session.execute(select(KOLInfo.kol_id)).scalars().all()
        existing_kol_ids = set(existing_ids)
        print(f"数据库中已有 {len(existing_kol_ids)} 条KOL记录")
    except Exception as e:
        print(f"获取已存在KOL ID时出错: {str(e)}")
    
    # 预先获取所有filter_data，用于后续匹配
    filter_data_dict = {}
    filter_names_set = set()  # 用于快速检查filter_name是否存在
    filters = session.execute(select(FilterData)).scalars().all()
    for filter_data in filters:
        filter_data_dict[filter_data.filter_name] = filter_data.id
        filter_names_set.add(filter_data.filter_name)
    
    print(f"已加载 {len(filter_data_dict)} 条filter_data记录")
    
    # 跟踪新创建的filter记录数
    new_filter_count = 0
    
    # 获取 PROJECT_CODE 项目的tag标签
    project_code = PROJECT_CODE
    tags = session.execute(
        text("SELECT t.id, t.name FROM tag t JOIN project_tag_association pta ON t.id = pta.tag_id WHERE pta.project_code = :project_code"),
        {"project_code": project_code}
    ).fetchall()
    
    tag_dict = {tag.name: tag.id for tag in tags}
    print(f"已加载 {len(tag_dict)} 个标签: {list(tag_dict.keys())}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(csv_file_path)
        print(f"CSV文件读取成功，共 {len(df)} 行")
        
        # 检查必要的列是否存在
        required_columns = ["KOL ID", "Creator ID", "Source", "Slug", "Bio", "Followers(K)", "Mean Views(K)", 
                           "Median Views(K)", "Engagement Rate(%)", "KOL Name", "Account link", "Filter", 
                           "Email", "Level", "Keywords-AI", "Most used hashtags", "Likes(K)", 
                           "Average Comments(K)", "Average Likes(K)", "Average Views(K)"]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"警告: CSV文件缺少以下列: {missing_columns}")
            return
        
        # 批量处理参数
        batch_size = 1000
        total_records = len(df)
        total_batches = (total_records + batch_size - 1) // batch_size
        
        kol_infos = []
        processed_kol_ids = set()  # 用于跟踪已成功插入的KOL ID
        
        # 处理每一行数据
        for index, row in df.iterrows():
            batch_num = index // batch_size + 1
            
            if index % 100 == 0:
                print(f"处理进度: {index}/{total_records} ({(index/total_records*100):.2f}%), 批次 {batch_num}/{total_batches}")
            
            # 创建KOL ID (TK前缀)
            kol_id = f"{QZ}{row['KOL ID']}" if not pd.isna(row['KOL ID']) else None
            if not kol_id:
                print(f"警告: 第 {index+1} 行缺少KOL ID，跳过此行")
                continue
            
            # 检查避免重复
            if kol_id in existing_kol_ids:
                if index % 1000 == 0:
                    print(f"跳过已存在的KOL ID: {kol_id}")
                continue
            
            # 处理JSON字段
            keywords_ai = process_json_field(row.get('Keywords-AI', None))
            most_used_hashtags = process_json_field(row.get('Most used hashtags', None))
            
            # 创建KOL信息对象
            kol_info = KOLInfo(
                kol_id=kol_id,
                kol_name=str(row['KOL ID']) if not pd.isna(row['KOL ID']) else None,
                username=row.get('KOL Name', None),
                email=row.get('Email', None),
                bio=row.get('Bio', None),
                account_link=row.get('Account link', None),
                followers_k=safe_float(row.get('Followers(K)', None)),
                likes_k=safe_float(row.get('Likes(K)', None)),
                platform=PLATFORM,  # 固定值
                source=row.get('Source', None),
                slug=row.get('Slug', None),
                creator_id=row.get('Creator ID', None),
                mean_views_k=safe_float(row.get('Mean Views(K)', None)),
                median_views_k=safe_float(row.get('Median Views(K)', None)),
                engagement_rate=safe_float(row.get('Engagement Rate(%)', None)),
                average_views_k=safe_float(row.get('Average Views(K)', None)),
                average_likes_k=safe_float(row.get('Average Likes(K)', None)),
                average_comments_k=safe_float(row.get('Average Comments(K)', None)),
                most_used_hashtags=most_used_hashtags,
                level=row.get('Level', None),
                keywords_ai=keywords_ai
            )
            
            kol_infos.append(kol_info)
            
            # 批量提交KOL信息
            if (index + 1) % batch_size == 0 or index == total_records - 1:
                try:
                    # 先插入KOL信息并提交
                    session.add_all(kol_infos)
                    session.commit()
                    
                    # 记录成功插入的KOL ID
                    for kol in kol_infos:
                        processed_kol_ids.add(kol.kol_id)
                        
                    print(f"批次 {batch_num}/{total_batches} KOL信息提交成功，已处理 {min(index+1, total_records)}/{total_records} 条记录")
                    
                    # 清空批次列表
                    kol_infos = []
                except Exception as e:
                    session.rollback()
                    print(f"批次 {batch_num}/{total_batches} KOL信息提交失败: {str(e)}")
                    # 继续处理下一批
                    kol_infos = []

        print(f"KOL信息处理完成，共成功插入 {len(processed_kol_ids)} 条记录")

        # 处理关联关系
        print("开始处理关联关系...")

        # 重新读取CSV处理关联关系
        filter_associations = []
        tag_associations = []
        batch_size = 5000  # 增大关联表的批处理大小

        for index, row in df.iterrows():
            batch_num = index // batch_size + 1
            
            if index % 500 == 0:
                print(f"关联处理进度: {index}/{total_records} ({(index/total_records*100):.2f}%)")
            
            kol_id = f"{QZ}{row['KOL ID']}" if not pd.isna(row['KOL ID']) else None
            if not kol_id or kol_id not in processed_kol_ids:
                continue
            
            # 处理Filter关联
            filter_value = row.get('Filter', None)
            if filter_value and not pd.isna(filter_value):
                if filter_value not in filter_data_dict:
                    # 创建新的filter_data记录
                    try:
                        # 确保不重复创建
                        if filter_value not in filter_names_set:
                            new_filter = FilterData(
                                language="English",
                                gender=None,
                                location="US",
                                filter_body={},  # 空JSON对象
                                filter_name=filter_value,
                                project_code=PROJECT_CODE
                            )
                            session.add(new_filter)
                            session.commit()
                            
                            # 更新filter_data_dict和filter_names_set
                            filter_data_dict[filter_value] = new_filter.id
                            filter_names_set.add(filter_value)
                            new_filter_count += 1
                            
                            if new_filter_count % 100 == 0:
                                print(f"已创建 {new_filter_count} 个新的filter记录")
                    except Exception as e:
                        session.rollback()
                        print(f"创建新filter记录失败: {filter_value}, 错误: {str(e)}")
                
                # 现在filter_value应该存在于filter_data_dict中
                if filter_value in filter_data_dict:
                    filter_id = filter_data_dict[filter_value]
                    filter_association = FilterKolAssociation(
                        filter_id=filter_id,
                        kol_id=kol_id,
                        project_code=project_code
                    )
                    filter_associations.append(filter_association)
            
            # 处理Tag关联（关联到所有 PROJECT_CODE 项目的标签）
            for tag_name, tag_id in tag_dict.items():
                tag_association = KolTagAssociation(
                    kol_id=kol_id,
                    tag_id=tag_id,
                    project_code=project_code
                )
                tag_associations.append(tag_association)
            
            # 批量提交关联
            if (index + 1) % batch_size == 0 or index == total_records - 1:
                try:
                    # 先提交filter关联
                    if filter_associations:
                        session.add_all(filter_associations)
                        session.commit()
                        print(f"提交了 {len(filter_associations)} 条filter关联")
                        filter_associations = []
                    
                    # 再提交tag关联
                    if tag_associations:
                        session.add_all(tag_associations)
                        session.commit()
                        print(f"提交了 {len(tag_associations)} 条tag关联")
                        tag_associations = []
                        
                except Exception as e:
                    session.rollback()
                    print(f"关联关系提交失败: {str(e)}")
                    
                    # 如果批次太大导致失败，减小批次大小重试
                    if len(filter_associations) > 100 or len(tag_associations) > 100:
                        retry_batch_size = 100
                        
                        # 重试filter关联
                        for i in range(0, len(filter_associations), retry_batch_size):
                            try:
                                batch = filter_associations[i:i+retry_batch_size]
                                if batch:
                                    session.add_all(batch)
                                    session.commit()
                                    print(f"重试提交了 {len(batch)} 条filter关联")
                            except Exception as e2:
                                session.rollback()
                                print(f"重试filter关联提交失败: {str(e2)}")
                        
                        # 重试tag关联
                        for i in range(0, len(tag_associations), retry_batch_size):
                            try:
                                batch = tag_associations[i:i+retry_batch_size]
                                if batch:
                                    session.add_all(batch)
                                    session.commit()
                                    print(f"重试提交了 {len(batch)} 条tag关联")
                            except Exception as e2:
                                session.rollback()
                                print(f"重试tag关联提交失败: {str(e2)}")
                        
                    # 清空列表继续处理
                    filter_associations = []
                    tag_associations = []
            
        # 在处理完所有数据后，输出新创建的filter记录数
        print(f"CSV数据处理完成，共处理 {total_records} 条记录")
        print(f"总共创建了 {new_filter_count} 个新的filter记录")

    except Exception as e:
        print(f"处理CSV文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()

def safe_float(value):
    """安全转换为浮点数"""
    if pd.isna(value) or value is None:
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def process_json_field(value):
    """处理JSON字段，确保它是有效的JSON格式"""
    if pd.isna(value) or value is None:
        return None
    
    if isinstance(value, list):
        return value
    
    if isinstance(value, str):
        # 尝试解析JSON字符串
        try:
            # 处理可能的单引号JSON
            value = value.replace("'", '"')
            return json.loads(value)
        except json.JSONDecodeError:
            # 如果不是有效的JSON，尝试按逗号分隔
            try:
                items = [item.strip() for item in value.split(',')]
                return items
            except:
                return None
    
    return None

def main():
    """主函数"""
    # 获取CSV文件路径（可以从命令行参数传入）
    csv_file_path = CSV_FILE_PATH
    if len(sys.argv) > 1:
        csv_file_path = sys.argv[1]
    
    if not os.path.exists(csv_file_path):
        print(f"错误: CSV文件不存在: {csv_file_path}")
        return
    
    # 创建数据库会话
    session = SessionLocal()
    try:
        # 处理CSV数据
        process_csv_data(session, csv_file_path)
    finally:
        session.close()

if __name__ == "__main__":
    main()
