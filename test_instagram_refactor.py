#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
测试重构后的 Instagram 爬虫服务
"""

from app.services.instagram_crawler_service import InstagramAPIClient

def test_instagram_api_client():
    """测试 InstagramAPIClient 的基本功能"""
    username = "test_user"
    
    # 测试初始化
    client = InstagramAPIClient(username)
    assert client._username == username
    assert client._session is None
    assert client._initialized is False
    
    # 测试上下文管理器
    try:
        with InstagramAPIClient(username) as client:
            assert client._initialized is True
            assert client._session is not None
            # 这里不实际调用API，只测试结构
            print("✓ 上下文管理器工作正常")
    except Exception as e:
        print(f"✗ 上下文管理器测试失败: {e}")
    
    # 测试手动资源管理
    try:
        client = InstagramAPIClient(username)
        client.init_sync()
        assert client._initialized is True
        assert client._session is not None
        client.cleanup_sync()
        assert client._initialized is False
        assert client._session is None
        print("✓ 手动资源管理工作正常")
    except Exception as e:
        print(f"✗ 手动资源管理测试失败: {e}")
    
    # 测试API URL构建
    assert InstagramAPIClient.BASE_URL == "https://api.tikhub.io/api/v1/instagram/web_app"
    print("✓ BASE_URL 配置正确")
    
    print("所有基本功能测试通过！")

def test_api_methods():
    """测试API方法的结构（不实际调用API）"""
    username = "test_user"
    client = InstagramAPIClient(username)
    
    # 检查方法是否存在
    assert hasattr(client, 'fetch_user_info')
    assert hasattr(client, 'fetch_user_reels')
    assert hasattr(client, 'scrape_sync')
    
    # 检查方法签名
    import inspect
    
    # fetch_user_info 应该接受 username 参数
    sig = inspect.signature(client.fetch_user_info)
    assert 'username' in sig.parameters
    
    # fetch_user_reels 应该接受 username 和 max_reels 参数
    sig = inspect.signature(client.fetch_user_reels)
    assert 'username' in sig.parameters
    assert 'max_reels' in sig.parameters
    
    # scrape_sync 应该接受 username 和 max_reels 参数
    sig = inspect.signature(client.scrape_sync)
    assert 'username' in sig.parameters
    assert 'max_reels' in sig.parameters
    
    print("✓ API方法结构正确")

if __name__ == "__main__":
    print("开始测试重构后的 Instagram 爬虫服务...")
    test_instagram_api_client()
    test_api_methods()
    print("所有测试完成！")
