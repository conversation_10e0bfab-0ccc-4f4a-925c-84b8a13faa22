{"filter_name": "Modash-coser-bio-10k-16", "source_name": "modash", "platform_name": "tiktok", "timestamp": "2025-06-18T14:24:04.231901", "data_count": 16, "raw_data": [{"_id": "6582583963615d606f9da78a", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "6673460226408875009", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.08183481152993348, "engagements": 2358, "followers": 270600, "fullname": "Luna_លូណា", "picture": "https://imgigp.modash.io/v2?4e7GkPsCtkrXRAYTZgKXbRcwcUHSO9sxDOeo0a1RB%2Fxs3WYJbRVN0FblCXok06jVTtXkT9JlZ2hOtMDZc%2Bhwhxn%2FZpSR7RAR1L6D2YFZuDTCyUF0x8b9xwSRHm6hWi7%2BPYiv2QBaqiaSNkk%2FcX3lXhvayC0NhB8E09O6n4dvEbr3FN0rnLvs4jBMZRHTIcU7", "url": "https://www.tiktok.com/@_lunamori_", "username": "_lunamori_", "isVerified": false, "averageViews": 26100}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.5258611381060361}]}}}}, "profileId": "6582583963615d606f9da75c"}, {"_id": "68500c5fc28d888820b85c65", "profileType": "TIKTOK", "profileData": {"userId": "7195524639035966469", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.24711171310629515, "engagements": 10580, "followers": 150100, "fullname": "SooYoung Coser", "picture": "https://imgigp.modash.io/v2?4e7GkPsCtkrXRAYTZgKXbVo7gyb6lfPjUhIO1dCG%2Fyxs3WYJbRVN0FblCXok06jVHsZgl6YZ%2BzfA9mEAY2MFwF78xzzUMZwb9hsrqt4F1VZGbIjaRhPPtaV%2FFi68PKO4TqXqAgl%2F6hKGpq0NR%2BEADt8qYMz2XWWBVAYsVJSYSU3Q6amLljmQ%2BuBP4okR8%2Bl5", "url": "https://www.tiktok.com/@sooyoungg_", "username": "sooyoungg_", "isVerified": false, "averageViews": 38400}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.413803273853414}]}}}}, "profileId": "65813d8263615d606feeff65"}, {"_id": "622e0a43e048a164c313a5bd", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "6626100926439522309", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.18786714285714284, "engagements": 2086, "followers": 74800, "fullname": "𝓂ℴ𝓂ℴ", "picture": "https://imgigp.modash.io/v2?Ks%2FFXRbZf%2Blvn%2BwWMyRIs2t8VZOFcFEb4OJcMrjtna85Tjn5vdMMzB0%2FEXicr%2BY5J7TeKh0ZEBR026e3SbZ2zgBp9ruF1ax8WlcHvK13qxKEO1phDXlCsibT57vcjdtE1frKa6gBON1YGYH%2FvkpDuAg5wjaQPsN7lXz%2FNh%2BeaLQP7%2F319P2k%2F1dcITajqRah", "url": "https://www.tiktok.com/@bitterrabbitcos", "username": "bitterrabbitcos", "isVerified": false, "averageViews": 12250}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.5964605891256258}]}}}}, "profileId": "622e0a43e048a1573742113b"}, {"_id": "68521970c28d888820d817b8", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "7244765062539215878", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.2101516072841212, "engagements": 9411, "followers": 43300, "fullname": "rinsilly ♡ !", "picture": "https://imgigp.modash.io/v2?4e7GkPsCtkrXRAYTZgKXbRcwcUHSO9sxDOeo0a1RB%2Fxs3WYJbRVN0FblCXok06jVzlXMqAgSi4YY5nmqB93n3o1d2KVU5rKoNI82%2FwlODIqbKu%2F8OPnzhS8qT3Lr%2BRUsPYiv2QBaqiaSNkk%2FcX3lXhvayC0NhB8E09O6n4dvEbr3FN0rnLvs4jBMZRHTIcU7", "url": "https://www.tiktok.com/@itoshigma", "username": "itoshigma", "isVerified": false, "averageViews": 44150}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.6967627373309657}]}}}}, "profileId": "67e4226e4e4cdb95d58c1ba4"}, {"_id": "68521970c28d888820d817b9", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "6790230845594174470", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.07207692307692308, "engagements": 937, "followers": 41900, "fullname": "<PERSON><PERSON>", "picture": "https://imgigp.modash.io/v2?XkjVaNuuCZ%2BzpQtQYyuTHBcwcUHSO9sxDOeo0a1RB%2Fxs3WYJbRVN0FblCXok06jVCNK2mwDF1aaSi8JT3JSXIek3%2Bv77FCiuY5M6uaqVr7XGBKb95fwwJCNUqyqGOdcGPYiv2QBaqiaSNkk%2FcX3lXhvayC0NhB8E09O6n4dvEbr3FN0rnLvs4jBMZRHTIcU7", "url": "https://www.tiktok.com/@ikalovee", "username": "ikalovee", "isVerified": false, "averageViews": 13300}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.7821083890126206}]}}}}, "profileId": "68521970c28d888820d817b0"}, {"_id": "68521970c28d888820d817ba", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "6784370282859791362", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.12978723404255318, "engagements": 6806, "followers": 18500, "fullname": "reen coser", "picture": "https://imgigp.modash.io/v2?9gRRkBbg4nctjMDXek72QZVfwaM%2B9JyelB0J2IwjGfU5qrdIuBTqbpa5S%2B1WOkIwEQfaREddB561K%2BN9iIeAa5NYkYnmYvSWA2dSCEyX2AcH%2BICJ%2FyoMqC2VL1F0i%2F%2FhVR48PTat%2BaLMFxWY2EiqqUDgujHdSfEckhILPY7rVQuOJ9JDhJdA9K5lqSCfB8FC", "url": "https://www.tiktok.com/@reen_on_me", "username": "reen_on_me", "isVerified": false, "averageViews": 53400}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.8}]}}}}, "profileId": "68521970c28d888820d817b1"}, {"_id": "681c62088356e75262faa851", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "7345198250356622378", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.1790101809954751, "engagements": 3348, "followers": 17700, "fullname": "🥪", "picture": "https://imgigp.modash.io/v2?Ks%2FFXRbZf%2Blvn%2BwWMyRIs2t8VZOFcFEb4OJcMrjtna85Tjn5vdMMzB0%2FEXicr%2BY5J7TeKh0ZEBR026e3SbZ2zr0b04pTKGsNBHdJ9cww1s2lCkaBmQkDpbfsapF8%2Bt0brfwO1kx0eCy1Ew12w1bc%2Fgg5wjaQPsN7lXz%2FNh%2BeaLQP7%2F319P2k%2F1dcITajqRah", "url": "https://www.tiktok.com/@smz_m2", "username": "smz_m2", "isVerified": false, "averageViews": 16250}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.3504772004241782}]}}}}, "profileId": "681c62078356e75262faa849"}, {"_id": "68521970c28d888820d817bb", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "7444538946573583376", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.15936288691188305, "engagements": 15037, "followers": 12800, "fullname": "熠灵-CN", "picture": "https://imgigp.modash.io/v2?4e7GkPsCtkrXRAYTZgKXbRcwcUHSO9sxDOeo0a1RB%2Fxs3WYJbRVN0FblCXok06jVTtXkT9JlZ2hOtMDZc%2Bhwh%2Bze2V%2BFUYqWC0uq%2BoN3OZ5EQga4mmXQplYOE99odoD9PYiv2QBaqiaSNkk%2FcX3lXhvayC0NhB8E09O6n4dvEbr3FN0rnLvs4jBMZRHTIcU7", "url": "https://www.tiktok.com/@yiling_china", "username": "yiling_china", "isVerified": false, "averageViews": 80800}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.46075085324232085}]}}}}, "profileId": "68521970c28d888820d817b2"}, {"_id": "68521970c28d888820d817bc", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "7132646908138980398", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.20694564213119804, "engagements": 3924, "followers": 11900, "fullname": "<PERSON><PERSON>", "picture": "https://imgigp.modash.io/v2?qeKgYKgV1gO1vAP42sjOAmt8VZOFcFEb4OJcMrjtna85Tjn5vdMMzB0%2FEXicr%2BY5J7TeKh0ZEBR026e3SbZ2zpB1NSNsIszwt7xQM%2BzhYqV3rf%2FHHlIPW19gjEuScZCCq3HEdJp7ncYY%2FmraC5P64Qg5wjaQPsN7lXz%2FNh%2BeaLQP7%2F319P2k%2F1dcITajqRah", "url": "https://www.tiktok.com/@yy881__", "username": "yy881__", "isVerified": false, "averageViews": 18400}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.726713532513181}]}}}}, "profileId": "68521970c28d888820d817b3"}, {"_id": "68521970c28d888820d817bd", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "6922040748037178370", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.062278559520570945, "engagements": 2604, "followers": 11400, "fullname": "罐頭不好吃🥫", "picture": "https://imgigp.modash.io/v2?4e7GkPsCtkrXRAYTZgKXbVo7gyb6lfPjUhIO1dCG%2Fyxs3WYJbRVN0FblCXok06jVHsZgl6YZ%2BzfA9mEAY2MFwLNTY7hh35SdAQxpADH5iFDXqvWk26Qe%2FOaXuUUHr09jTqXqAgl%2F6hKGpq0NR%2BEADt8qYMz2XWWBVAYsVJSYSU3Q6amLljmQ%2BuBP4okR8%2Bl5", "url": "https://www.tiktok.com/@canneat", "username": "canneat", "isVerified": false, "averageViews": 32900}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.5650741350906096}]}}}}, "profileId": "68521970c28d888820d817b4"}, {"_id": "68521970c28d888820d817b5", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "6548726134959521794", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.08869080084501632, "engagements": 1051, "followers": 11100, "fullname": "Ruru茹茹", "picture": "https://imgigp.modash.io/v2?4e7GkPsCtkrXRAYTZgKXbRcwcUHSO9sxDOeo0a1RB%2Fxs3WYJbRVN0FblCXok06jVCNK2mwDF1aaSi8JT3JSXISPJBeROEKi%2BgCoxHG3aHDNbt3QXcsoMOFPYnn2oUR4hPYiv2QBaqiaSNkk%2FcX3lXhvayC0NhB8E09O6n4dvEbr3FN0rnLvs4jBMZRHTIcU7", "url": "https://www.tiktok.com/@howdanoruru", "username": "howdanoruru", "isVerified": false, "averageViews": 14500}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.4131326949384405}]}}}}, "profileId": "68521970c28d888820d817ad"}, {"_id": "6849a5dd7218c0be3b2615c6", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "6546441769431646213", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.2474302751867836, "engagements": 41458, "followers": 8211, "fullname": "Vie Sera ♡", "picture": "https://imgigp.modash.io/v2?4e7GkPsCtkrXRAYTZgKXbRcwcUHSO9sxDOeo0a1RB%2Fxs3WYJbRVN0FblCXok06jVHN4OcaUVEc8CDLLDAlLOXoruXH8LWD3Q2cGWn0hmHQTV%2Fs%2F2vPWysvowYGgSPS0KPYiv2QBaqiaSNkk%2FcX3lXhvayC0NhB8E09O6n4dvEbr3FN0rnLvs4jBMZRHTIcU7", "url": "https://www.tiktok.com/@viesera.cos", "username": "viesera.cos", "isVerified": false, "averageViews": 152050}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.6584269662921348}]}}}}, "profileId": "6849a5dd7218c0be3b2615c4"}, {"_id": "68521970c28d888820d817b6", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "6996873969710793755", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.10020174848688634, "engagements": 701, "followers": 4132, "fullname": "kaizo.cos", "picture": "https://imgigp.modash.io/v2?4e7GkPsCtkrXRAYTZgKXbRcwcUHSO9sxDOeo0a1RB%2Fxs3WYJbRVN0FblCXok06jVtpQeeYK2MnFtnmamBkZMdki9BL8weSNsTfb0ur7EiRJaYrY5nsfek7WzeyD%2Bi%2F8nPYiv2QBaqiaSNkk%2FcX3lXhvayC0NhB8E09O6n4dvEbr3FN0rnLvs4jBMZRHTIcU7", "url": "https://www.tiktok.com/@kaizo.cos", "username": "kaizo.cos", "isVerified": false, "averageViews": 14300}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.9579487179487179}]}}}}, "profileId": "68521970c28d888820d817ae"}, {"_id": "681c81658356e75262fb4d22", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "7312650376663352366", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.2579807887266829, "engagements": 4584, "followers": 3038, "fullname": "hikari", "picture": "https://imgigp.modash.io/v2?Ks%2FFXRbZf%2Blvn%2BwWMyRIs2t8VZOFcFEb4OJcMrjtna85Tjn5vdMMzB0%2FEXicr%2BY5dm8Ky3Qr2AYn6eDs46PyKz2ol6nEuvoIxqzmAXYMrYu93GNqySBcwOozq4Txw3HEbtQzDeKeOKYmT5%2BGX1fgiNDpqYuWOZD64E%2FiiRHz6Xk%3D", "url": "https://www.tiktok.com/@hikari.coser", "username": "hikari.coser", "isVerified": false, "averageViews": 15050}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.704225352112676}]}}}}, "profileId": "681c81658356e75262fb4d1c"}, {"_id": "68521970c28d888820d817b7", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "7104239275151262746", "updatedAt": "2025-06-18T06:24:01.953Z", "profile": {"engagementRate": 0.16636454193878314, "engagements": 3082, "followers": 2131, "fullname": "<PERSON>", "picture": "https://imgigp.modash.io/v2?4e7GkPsCtkrXRAYTZgKXbRcwcUHSO9sxDOeo0a1RB%2Fxs3WYJbRVN0FblCXok06jVv%2FGOwYfwNArL%2BnlEwgt1SnPK7RI0z%2B5ZY0Q%2BMcFWrPDzKS2YNUlPqYWKSDLrzqCxPYiv2QBaqiaSNkk%2FcX3lXhvayC0NhB8E09O6n4dvEbr3FN0rnLvs4jBMZRHTIcU7", "url": "https://www.tiktok.com/@renwei0788", "username": "renwei0788", "isVerified": false, "averageViews": 13875}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.464}]}}}}, "profileId": "68521970c28d888820d817af"}, {"_id": "67c029332e51ee5a8d9879ef", "profileType": "TIKTOK", "emails": [], "profileData": {"userId": "7104190279725614107", "updatedAt": "2025-06-18T06:24:02.827Z", "profile": {"engagementRate": 0.21721518987341773, "engagements": 5180, "followers": 1457, "fullname": "𝐿awliet.", "picture": "https://imgigp.modash.io/v2?9gRRkBbg4nctjMDXek72QaD1Z6ujEs9fS8Aa10L%2B0D8gQQMpLWaQYL0e7nkOKdaBj3JKIfwGQB92stVVCQ5%2Bl8qQB9THY5j8qhc84ZP6VBziyucoTWelv9GRc%2BAJyGvi9TfYdscwbC4ZofsENeTRgVfiLgvD5Zfc%2BBWKBfLWDl0%3D", "url": "https://www.tiktok.com/@lawlieve", "username": "lawlieve", "isVerified": false, "averageViews": 17100}, "match": {"audience_likers": {"data": {"audience_languages": [{"code": "en", "name": "English", "weight": 0.3423180592991914}]}}}}, "profileId": "67c029332e51ee5a8d9879e9"}]}