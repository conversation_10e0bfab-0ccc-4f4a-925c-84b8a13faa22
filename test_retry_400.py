#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
测试400错误重试功能
"""

import requests
from unittest.mock import Mock, patch
from app.services.instagram_crawler_service import retry_on_rate_limit


def test_400_retry():
    """测试400错误重试功能"""
    print("=== 测试400错误重试 ===")
    
    call_count = 0
    
    @retry_on_rate_limit(max_retries=3, base_delay=0.1)  # 使用较短的延迟进行测试
    def mock_api_call():
        nonlocal call_count
        call_count += 1
        print(f"API调用次数: {call_count}")
        
        if call_count < 3:  # 前两次返回400错误
            response = Mock()
            response.status_code = 400
            error = requests.exceptions.HTTPError("400 Bad Request")
            error.response = response
            raise error
        else:  # 第三次成功
            return {"success": True, "data": "test_data"}
    
    try:
        result = mock_api_call()
        print(f"✓ 重试成功，结果: {result}")
        print(f"总共调用了 {call_count} 次")
        assert call_count == 3, f"期望调用3次，实际调用了{call_count}次"
        assert result["success"] is True
        print("✓ 400错误重试测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")


def test_429_retry():
    """测试429错误重试功能"""
    print("\n=== 测试429错误重试 ===")
    
    call_count = 0
    
    @retry_on_rate_limit(max_retries=2, base_delay=0.1)
    def mock_api_call():
        nonlocal call_count
        call_count += 1
        print(f"API调用次数: {call_count}")
        
        if call_count < 2:  # 第一次返回429错误
            response = Mock()
            response.status_code = 429
            error = requests.exceptions.HTTPError("429 Too Many Requests")
            error.response = response
            raise error
        else:  # 第二次成功
            return {"success": True, "data": "test_data"}
    
    try:
        result = mock_api_call()
        print(f"✓ 重试成功，结果: {result}")
        print(f"总共调用了 {call_count} 次")
        assert call_count == 2, f"期望调用2次，实际调用了{call_count}次"
        print("✓ 429错误重试测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")


def test_other_error_no_retry():
    """测试其他错误不重试"""
    print("\n=== 测试其他错误不重试 ===")
    
    call_count = 0
    
    @retry_on_rate_limit(max_retries=3, base_delay=0.1)
    def mock_api_call():
        nonlocal call_count
        call_count += 1
        print(f"API调用次数: {call_count}")
        
        # 返回404错误，不应该重试
        response = Mock()
        response.status_code = 404
        error = requests.exceptions.HTTPError("404 Not Found")
        error.response = response
        raise error
    
    try:
        result = mock_api_call()
        print("✗ 不应该成功")
    except requests.exceptions.HTTPError as e:
        print(f"✓ 正确抛出异常: {e}")
        print(f"只调用了 {call_count} 次（不重试）")
        assert call_count == 1, f"期望只调用1次，实际调用了{call_count}次"
        print("✓ 其他错误不重试测试通过")


def test_max_retries_exceeded():
    """测试超过最大重试次数"""
    print("\n=== 测试超过最大重试次数 ===")
    
    call_count = 0
    
    @retry_on_rate_limit(max_retries=2, base_delay=0.1)
    def mock_api_call():
        nonlocal call_count
        call_count += 1
        print(f"API调用次数: {call_count}")
        
        # 总是返回400错误
        response = Mock()
        response.status_code = 400
        error = requests.exceptions.HTTPError("400 Bad Request")
        error.response = response
        raise error
    
    try:
        result = mock_api_call()
        print("✗ 不应该成功")
    except requests.exceptions.HTTPError as e:
        print(f"✓ 正确抛出异常: {e}")
        print(f"总共调用了 {call_count} 次")
        assert call_count == 2, f"期望调用2次，实际调用了{call_count}次"
        print("✓ 超过最大重试次数测试通过")


if __name__ == "__main__":
    print("开始测试重试功能...\n")
    
    test_400_retry()
    test_429_retry()
    test_other_error_no_retry()
    test_max_retries_exceeded()
    
    print("\n所有重试测试完成！")
