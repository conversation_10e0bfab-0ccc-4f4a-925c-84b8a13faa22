# KOL 数据管理与分析平台

## 项目概述

KOL 数据管理与分析平台是一个专为社交媒体关键意见领袖（KOL）数据采集、存储和分析设计的后端服务系统。系统通过 RESTful API 接口实现 KOL 数据的核心管理功能，支持基础数据采集、存储和智能分析。

### 主要功能

- **KOL 数据采集**：支持从多个平台（如 TikTok/抖音、Instagram、YouTube）采集 KOL 数据
- **数据存储与管理**：提供完整的 CRUD 操作，支持 KOL 信息、视频数据、发送数据的管理
- **异步任务处理**：使用 Celery 实现异步任务队列，处理数据采集和分析任务
- **数据分析**：支持 KOL 数据的基础分析，包括指标计算和 AI 驱动的内容分析

## 技术栈

- **Python**: 3.12
- **Web框架**: FastAPI 0.115.11
- **ORM**: SQLAlchemy 2.0.39
- **数据验证**: Pydantic 2.10.6
- **数据库**: PostgreSQL 17
- **缓存/消息队列**: Redis 7
- **任务队列**: Celery 5.4.0
- **依赖管理**: Poetry 1.8

## 项目结构

```
kol-platform/
├── app/                    # 主应用目录
│   ├── api/                # API 接口
│   │   └── v1/             # API v1 版本
│   │       └── endpoints/  # API 端点
│   ├── core/               # 核心配置
│   ├── crud/               # 数据库操作
│   ├── db/                 # 数据库连接
│   ├── models/             # SQLAlchemy 模型
│   ├── schemas/            # Pydantic 模型
│   ├── services/           # 服务层
│   ├── worker/             # Celery 任务
│   │   └── tasks/          # 任务定义
│   └── main.py             # 应用入口
├── migrations/             # Alembic 数据库迁移
├── .env                    # 环境变量
├── .env.example            # 环境变量示例
├── alembic.ini             # Alembic 配置
├── poetry.lock             # Poetry 锁定的依赖
├── pyproject.toml          # Poetry 项目配置
└── README.md               # 项目文档
```

## 安装与配置

### 前置条件

- Python 3.12+
- PostgreSQL 17
- Redis 7
- Poetry 1.8

### 安装步骤

1. 克隆项目仓库

```bash
git clone <repository-url>
cd kol-platform
```

2. 安装依赖

```bash
poetry install
```

3. 环境变量配置

复制 `.env.example` 文件并重命名为 `.env`，然后根据实际情况修改环境变量：

```bash
cp .env.example .env
```

4. 数据库初始化

使用项目提供的初始化脚本进行数据库初始化：

```bash
# 方法1：使用初始化脚本（推荐）
python script/init_db.py

# 方法2：手动执行迁移
alembic revision --autogenerate -m "Initial migration"
alembic upgrade head
```

迁移数据步骤：
- python script/init_db.py  
- python script/tb_filter.py
- python script/tb_kol_info.py
- python script/tb_video_info.py
- python script/tb_send_data.py


初始化脚本会自动执行以下操作：
- 应用所有数据库迁移
- 初始化基础数据（如标签等）

## 运行项目

### 启动 Web 服务

```bash
# 开发环境
uvicorn app.main:app --reload

# 生产环境
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### 启动 Celery Worker

```bash
# 启动 Celery Worker
poetry run celery -A app.worker.celery_app worker --loglevel=info

# 可选：启动 Celery Beat（如需定时任务）
poetry run celery -A app.worker.celery_app beat --loglevel=info
```

## API 文档

启动服务后，可通过以下 URL 访问 API 文档：

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### 功能文档

以下是系统主要功能的详细文档：

- [KOL高级检索功能](docs/Advanced_Search_Guide.md) - 详细了解如何使用高级检索API进行复杂条件查询
- [Celery任务系统](docs/CELERY.md) - 异步任务系统的使用说明
- [数据收集流程](docs/Data_collection_process.md) - 数据收集流程和策略
- [邮件处理流程](docs/Email_Process.md) - 邮件处理的自动化流程

## 数据库模型

系统包含以下主要数据模型：

- **FilterData**: 筛选条件，存储 KOL 的标签、语言、性别、地理位置等筛选信息
- **KOLInfo**: KOL 信息，包含基本信息和指标数据
- **VideoInfo**: 视频信息，与 KOL 是多对一关系
- **SendData**: 发送数据，记录消息发送状态和相关信息

## 开发指南

### 添加新的 API 端点

1. 在 `app/api/v1/endpoints/` 目录下创建新的路由文件
2. 在 `app/api/v1/__init__.py` 中注册新的路由
3. 实现路由处理函数

### 添加新的 Celery 任务

1. 在 `app/worker/tasks/` 目录下创建新的任务文件
2. 使用 `@celery_app.task` 装饰器定义任务
3. 在需要的地方调用任务

### 数据库迁移

当修改数据库模型后，需要创建并应用新的迁移：

```bash
# 方法1：使用辅助脚本创建迁移（推荐）
python script/create_migration.py "描述迁移目的"

# 方法2：直接使用 alembic 命令
alembic revision --autogenerate -m "描述迁移目的"

# 应用迁移
alembic upgrade head
```

## 部署

### 1. 在服务器中部署

1. 在自动化服务器中执行

```bash
cd /home/<USER>/kol

docker-compose pull && docker-compose up -d --force-recreate
```
docker-compose pull：拉取服务镜像的最新版本。

--force-recreate：强制重新创建容器（即使配置未更改）。

2. 清理旧镜像（可选）
```bash
docker image prune -a -f
```

### 2.重启消息队列(可选)

当消息队列异常时，只重启服务可能无法解决问题，下面命令用于重启redis
```bash
sudo su
cd /root/Auto_Team_DB
docker-compose restart redis
```

## 测试

KOL-Dashboard API包含完整的自动化测试套件，用于验证API功能的正确性和稳定性。

### 运行测试

你可以使用我们提供的测试运行脚本来执行测试：

```bash
# 运行所有测试
python tests/run_tests.py

# 运行特定模块的测试
python tests/run_tests.py -m tests/api/test_kol_info.py

# 运行特定测试用例
python tests/run_tests.py -m tests/api/test_kol_info.py -f TestKOLInfoAPI::test_read_kol_info

# 显示详细输出
python tests/run_tests.py -v

# 显示测试中的输出
python tests/run_tests.py -s

# 生成测试覆盖率报告
python tests/run_tests.py -c
```

或者直接使用pytest：

```bash
# 运行所有测试
pytest

# 运行特定模块的测试
pytest tests/api/test_kol_info.py

# 运行特定测试用例
pytest tests/api/test_kol_info.py::TestKOLInfoAPI::test_read_kol_info

# 生成测试覆盖率报告
pytest --cov=app --cov-report=html
```

### 测试覆盖范围

当前的测试套件涵盖了以下功能：

1. KOL信息基本CRUD操作：创建、读取、更新、删除
2. KOL相关查询操作：按ID、邮箱、平台、项目代码查询
3. KOL与筛选条件的关联操作
4. 高级搜索功能
5. 发送数据（SendData）相关操作：
   - 基本CRUD操作
   - 按平台、KOL ID、发送状态查询
   - 获取指定KOL的所有发送数据

测试环境使用了本地数据库，并配置了测试工作流程，以便在GitHub Actions等CI/CD平台上自动运行。

### 待改进事项

- 异步API测试：目前异步API测试被跳过，需要进一步配置和优化
- 测试数据清理：确保测试之间完全隔离
- 性能测试：针对大数据量场景的性能测试
- 并发测试：测试API在高并发情况下的稳定性
- 数据库事务测试：确保所有数据库操作的原子性和一致性

### CI/CD集成

项目已配置GitHub Actions工作流，提交代码后会自动运行测试。
工作流配置文件位于`.github/workflows/test.yml`。
