services:
  # FastAPI 应用服务
  api:
    image: linyq1/kol_platform:latest
    container_name: kol_api
    environment:
      - APP_MODE=api
      - LOG_LEVEL=INFO
      - CONFIG_PATH=/app/config.toml
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
      - ./config.toml:/app/config.toml
    restart: unless-stopped
    networks:
      - kol_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s

  # Celery Worker 服务
  worker:
    image: linyq1/kol_platform:latest
    container_name: kol_worker
    environment:
      - APP_MODE=worker_kol
      - CELERY_WORKER_CONCURRENCY=6
      - CONFIG_PATH=/app/config.toml
    volumes:
      - ./logs:/app/logs
      - ./config.toml:/app/config.toml
    restart: unless-stopped
    networks:
      - kol_network

  # Celery Beat 服务 - 启用定时任务调度
  beat:
    image: linyq1/kol_platform:latest
    container_name: kol_beat
    environment:
      - APP_MODE=beat
      - CONFIG_PATH=/app/config.toml
    volumes:
      - ./logs:/app/logs
      - ./config.toml:/app/config.toml
    restart: unless-stopped
    networks:
      - kol_network


networks:
  kol_network:
    name: kol_network
    driver: bridge
