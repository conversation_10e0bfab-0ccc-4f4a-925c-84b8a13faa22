#!/usr/bin/env python
"""
Celery Worker 启动脚本
使用方式:
python celery_worker.py
"""
import os
import sys
import argparse
from app.worker.celery_app import celery_app
from app.logging_config import logger
from app.core.config import settings

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="启动Celery Worker")
    parser.add_argument(
        "--queue", "-Q", 
        default="default,kol_data", 
        help="指定要监听的队列，多个队列使用逗号分隔"
    )
    parser.add_argument(
        "--concurrency", "-c", 
        type=int, 
        default=settings.CELERY_WORKER_CONCURRENCY,
        help=f"Worker并发数 (默认: {settings.CELERY_WORKER_CONCURRENCY})"
    )
    parser.add_argument(
        "--loglevel", "-l", 
        default="INFO", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="日志级别 (默认: INFO)"
    )
    args = parser.parse_args()
    
    logger.info(f"启动Celery Worker进程...")
    logger.info(f"监听队列: {args.queue}")
    logger.info(f"并发数: {args.concurrency}")
    
    # 设置工作目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(base_dir)
    
    # 构建Celery Worker命令
    worker_command = [
        "celery",
        "-A", "app.worker.celery_app:celery_app",
        "worker",
        f"--loglevel={args.loglevel}",
        f"--concurrency={args.concurrency}",
        f"--queues={args.queue}",
        "--hostname=worker@%h",  # 使用主机名作为worker名
    ]
    
    logger.info(f"执行命令: {' '.join(worker_command)}")
    sys.argv = worker_command
    
    # 启动Worker
    from celery.bin.celery import main
    
    main() 