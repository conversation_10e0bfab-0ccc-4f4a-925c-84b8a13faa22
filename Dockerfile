FROM python:3.12-slim AS builder

WORKDIR /app

# 系统依赖 - 只安装构建必需的包
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libpq-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 最终镜像
FROM python:3.12-slim

WORKDIR /app

# 系统依赖 - 只安装运行必需的包
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    procps \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN useradd -m appuser

# 复制依赖
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 先复制启动脚本并设置权限
COPY celery_worker.py celery_beat.py ./
RUN chmod +x celery_*.py && \
    chown -R appuser:appuser /app

# 创建必要的目录
RUN mkdir -p /app/logs && \
    chmod -R 755 /app/logs && \
    chown -R appuser:appuser /app/logs

# 复制应用代码
COPY --chown=appuser:appuser app ./app
COPY --chown=appuser:appuser alembic.ini ./

USER appuser

# 设置环境变量
ENV PYTHONPATH=/app \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PORT=8000 \
    LOG_LEVEL=INFO \
    KOL_WORKER_LOG_LEVEL=INFO \
    BEAT_LOG_LEVEL=INFO \
    KOL_WORKER_CONCURRENCY=4 \
    MAX_TASKS_PER_CHILD=100 \
    PREFETCH_MULTIPLIER=4 \
    USE_REDBEAT=true \
    BROKER_CONNECTION_RETRY=true \
    BROKER_CONNECTION_RETRY_ON_STARTUP=true \
    BROKER_CONNECTION_MAX_RETRIES=5

# 配置文件路径环境变量
ENV CONFIG_PATH=/app/config.toml

# 暴露端口
EXPOSE 8000

# 运行模式: 
# api - API服务器
# worker_kol - KOL专用Worker
# beat - Beat调度器
ENV APP_MODE=api

# 健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
    CMD if [ "$APP_MODE" = "api" ]; then \
            curl -f http://localhost:8000/health || exit 1; \
        else \
            ps aux | grep celery | grep -v grep || exit 1; \
        fi

# 启动命令
CMD if [ "$APP_MODE" = "worker_kol" ]; then \
        ./start_worker_kol.sh; \
    elif [ "$APP_MODE" = "beat" ]; then \
        ./start_beat.sh; \
    else \
        uvicorn app.main:app --host=0.0.0.0 --port=${PORT:-8000} --log-level=$(echo ${LOG_LEVEL:-INFO} | tr '[:upper:]' '[:lower:]'); \
    fi 