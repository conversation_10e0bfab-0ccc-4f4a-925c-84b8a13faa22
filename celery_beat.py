#!/usr/bin/env python
"""
Celery Beat 启动脚本
用于启动定时任务调度器
使用方式:
python celery_beat.py [--loglevel LEVEL] [--scheduler SCHEDULER]
"""
import os
import sys
import argparse
from app.worker.celery_app import celery_app
from app.logging_config import logger

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="启动Celery Beat调度器")
    parser.add_argument(
        "--loglevel", "-l", 
        default="INFO", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="日志级别 (默认: INFO)"
    )
    parser.add_argument(
        "--scheduler", "-S", 
        help="使用的调度器 (例如: redbeat.RedBeatScheduler)"
    )
    args = parser.parse_args()
    
    logger.info("启动Celery Beat进程...")
    
    # 设置工作目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(base_dir)
    
    # 构建Celery Beat命令
    beat_command = [
        "celery",
        "-A", "app.worker.celery_app:celery_app",
        "beat",
        f"--loglevel={args.loglevel}",
    ]
    
    # 添加自定义调度器（如果指定）
    if args.scheduler:
        beat_command.append(f"--scheduler={args.scheduler}")
    
    logger.info(f"执行命令: {' '.join(beat_command)}")
    sys.argv = beat_command
    
    # 启动Beat
    from celery.bin.celery import main
    
    main() 