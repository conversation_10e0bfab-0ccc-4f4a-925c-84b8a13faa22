# 默认KOL解决方案

## 问题背景

由于取消了KOL是否存在的判断，当需要往video info存储数据的时候，若KOL不存在就会报错。为了解决这个问题，我们实现了一个默认KOL的解决方案。

## 解决方案

### 核心思路

1. 创建一个系统默认KOL，ID固定为 `DEFAULT_KOL_UNKNOWN`
2. 当创建video info时，如果指定的KOL不存在，自动关联到默认KOL
3. 在应用启动时自动初始化默认KOL，确保系统正常运行

### 实现细节

#### 1. 默认KOL信息

- **kol_id**: `DEFAULT_KOL_UNKNOWN`
- **kol_name**: `未知KOL`
- **username**: `unknown_kol`
- **platform**: `unknown`
- **source**: `system`
- **bio**: `系统默认KOL，用于处理不存在的KOL关联`
- 其他字段均为 `None`

#### 2. 修改的文件

##### `/app/crud/kol_info.py`
- 添加了 `get_or_create_default_kol()` 方法
- 用于获取或创建默认KOL

##### `/app/api/v1/endpoints/video_info.py`
- 修改了 `create_video()` 函数
- 在创建video时检查KOL是否存在
- 如果不存在，自动关联到默认KOL

##### `/app/main.py`
- 在应用启动事件中添加默认KOL初始化逻辑
- 确保应用启动时默认KOL已存在

##### `/script/init_default_kol.py`
- 独立的初始化脚本
- 可以手动运行来创建默认KOL

#### 3. 工作流程

```
创建Video Info
     ↓
检查KOL是否存在
     ↓
存在 → 正常关联
     ↓
不存在 → 获取/创建默认KOL → 关联到默认KOL
     ↓
创建Video Info成功
```

### 使用方法

#### 自动初始化（推荐）

应用启动时会自动检查并创建默认KOL，无需手动操作。

#### 手动初始化

如果需要手动初始化默认KOL，可以运行：

```bash
cd /Users/<USER>/Desktop/home/<USER>/api
python script/init_default_kol.py
```

### 优势

1. **数据完整性**: 确保所有video info都能成功入库
2. **向后兼容**: 不需要修改现有数据结构
3. **简单高效**: 通过默认KOL解决外键关联问题
4. **自动化**: 应用启动时自动初始化，无需人工干预
5. **可追溯**: 所有关联到默认KOL的video都可以被识别和管理

### 注意事项

1. 默认KOL的ID `DEFAULT_KOL_UNKNOWN` 是系统保留的，不应被删除
2. 如果需要后续将video关联到正确的KOL，可以通过更新video的kol_id来实现
3. 可以通过查询kol_id为 `DEFAULT_KOL_UNKNOWN` 的video来找到所有需要修正的数据

### 监控和维护

建议定期检查关联到默认KOL的video数量：

```sql
SELECT COUNT(*) FROM video_info WHERE kol_id = 'DEFAULT_KOL_UNKNOWN';
```

如果数量过多，可能需要：
1. 检查数据源的KOL信息质量
2. 完善KOL数据的同步机制
3. 手动修正部分video的KOL关联