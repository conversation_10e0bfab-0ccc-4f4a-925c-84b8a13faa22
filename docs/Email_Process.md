# 飞书邮件表格数据写入流程

该函数实现将创作者数据写入飞书表格的功能。主要步骤包括查询现有数据、数据去重、格式化和批量写入。

## 功能流程

```mermaid
flowchart TD
    A[开始] --> B[查询飞书表格现有数据]
    B --> C[提取现有 KOL ID 列表]
    C --> D[遍历 stored_data]
    D --> E{KOL ID 是否\n已存在?}
    E -->|是| F[从 stored_data\n中移除]
    E -->|否| G[保留数据]
    F --> H{是否遍历完成?}
    G --> H
    H -->|否| D
    H -->|是| I[格式化数据]
    I --> J[批量写入飞书表格]
    J --> K{写入是否成功?}
    K -->|是| L[返回成功状态]
    K -->|否| M[返回错误状态]
    L --> N[结束]
    M --> N
```

## 数据处理说明

### 1. 查询现有数据
- 调用飞书 API 获取表格中已存在的记录
- 提取所有现有记录的 KOL ID 用于后续去重

### 2. 数据去重
- 遍历 `stored_data` 中的每条记录
- 检查记录的 KOL ID 是否在现有数据中存在
- 移除已存在的记录，保留新数据

### 3. 数据格式化
将去重后的数据格式化为飞书表格所需的格式：
```python
{
    "KOL ID": item.get("kol_id", ""),
    "Email": item.get("email", ""),
    "Account link": {
        "text": item["account_link"], 
        "link": item["account_link"]
    },
    "KOL Name": item.get("username", ""),
    "Template": item.get("template", ""),
    "App Code": item.get("project_code", "")
}
```

### 4. 批量写入
- 调用飞书 API 批量写入格式化后的数据
- 处理写入响应，确认操作结果

### 5. 返回结果
返回包含以下信息的操作结果：
- 写入状态（成功/失败）
- 成功写入的记录数
- 错误信息（如果有）

## 注意事项
1. 确保输入数据中的必要字段（KOL ID、Email）不为空
2. 处理特殊字符和数据格式转换
3. 考虑批量写入的数量限制
4. 做好错误处理和日志记录

传入参数示例
stored_data:
```json
[
  {
    "kol_id": "TK_cia_maria",
    "kol_name": "cia_maria",
    "username": "Cia\u2002⭐️",
    "email": "<EMAIL>",
    "bio": "Aussie gal in London\nEHPlabs code LUCIA\n💌<EMAIL>",
    "account_link": "https://www.tiktok.com/@cia_maria",
    "platform": "tiktok",
    "engagement_rate": 0.*****************,
    "average_views_k": 50.028,
    "followers_k": 56.387,
    "likes_k": 6006.16,
    "keywords_ai": [
      "lifestyle"
    ],
    "source": "ttone",
    "level": "Mid-tier 50k～500k",
    "mean_views_k": 45.58,
    "median_views_k": 48.08,
    "db_id": "TK_cia_maria"
  },
  ...
]
```
platform: tiktok
query_params: 请求参数，包括：project_code，template 等参数

飞书查询返回结果：
```json
{
  "code": 0,
  "data": {
    "has_more": false,
    "items": [
      {
        "fields": {
          "Account link": {
            "link": "https://www.tiktok.com/@erikawheaton",
            "text": "https://www.tiktok.com/@erikawheaton"
          },
          "Email": [
            {
              "text": "<EMAIL>",
              "type": "text"
            }
          ],
          "KOL ID": [
            {
              "text": "erikawheaton",
              "type": "text"
            }
          ],
          "KOL Name": [
            {
              "text": "erikawheaton",
              "type": "text"
            }
          ],
          "Template": "TiKTok-V2"
        },
        "record_id": "recuGxdvolONcs"
      },
      {
        "fields": {
          "Account link": {
            "link": "https://www.tiktok.com/@nicolehoskens",
            "text": "https://www.tiktok.com/@nicolehoskens"
          },
          "Email": [
            {
              "text": "<EMAIL>",
              "type": "text"
            }
          ],
          "KOL ID": [
            {
              "text": "nicolehoskens",
              "type": "text"
            }
          ],
          "KOL Name": [
            {
              "text": "Nicole Hoskens,MS,RD",
              "type": "text"
            }
          ],
          "Template": "TiKTok-V2"
        },
        "record_id": "recuGxdvol5Edm"
      }
    ],
    "total": 2
  },
  "msg": "success"
}
```