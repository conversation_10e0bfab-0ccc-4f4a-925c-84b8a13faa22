# 高精度数据写入流程

该函数实现将创作者数据写入飞书表格的功能。主要步骤包括URL参数处理、多表查询去重、数据格式化和批量写入。

## 输入参数说明

### 1. 基础参数
- `stored_data`: 待写入的KOL数据列表
- `platform`: 平台名称（如：tiktok）
- `query_params`: 请求参数，包括project_code，template等

### 2. 表格参数
- `table_duplicate`: 用于查重的表格URL列表，以逗号分隔
  示例：`"https://laientech.feishu.cn/wiki/xxx?table=tbl1&view=view1,https://laientech.feishu.cn/wiki/xxx?table=tbl2&view=view2"`
- `table_save`: 数据写入目标表格的URL
  示例：`"https://laientech.feishu.cn/wiki/xxx?table=tbl1&view=view1"`

## 功能流程

```mermaid
flowchart TD
    A[开始] --> B1[解析表格URL参数]
    B1 --> B2[提取node_token和table_id]
    B2 --> B3[处理多个查重表格]
    B3 --> C[查询多个表格现有数据]
    C --> D[合并所有表格的KOL ID]
    D --> E[遍历stored_data]
    E --> F{KOL ID是否\n在任意表中存在?}
    F -->|是| G[从stored_data\n中移除]
    F -->|否| H[保留数据]
    G --> I{是否遍历完成?}
    H --> I
    I -->|否| E
    I -->|是| J[格式化数据]
    J --> K[写入目标表格]
    K --> L{写入是否成功?}
    L -->|是| M[返回成功状态]
    L -->|否| N[返回错误状态]
    M --> O[结束]
    N --> O
```

## 数据处理说明

### 1. URL参数处理
- 解析`table_duplicate`字符串，分割出多个表格URL
- 从每个URL中提取`node_token`和`table_id`
  ```python
  # URL示例
  "https://laientech.feishu.cn/wiki/QTRqwY8vFiplVUkP5CMc9e9Fn1f?table=tbla9MrmdY17iAXM&view=vewKNh3NJ2"
  # 需提取：
  # node_token: QTRqwY8vFiplVUkP5CMc9e9Fn1f
  # table_id: tbla9MrmdY17iAXM
  ```
- 从`table_save`中提取目标表格的`node_token`和`table_id`

### 2. 多表查询和去重
- 遍历所有查重表格URL
- 调用飞书API获取每个表格中的现有记录
- 合并所有表格中的KOL ID列表
- 使用合并后的KOL ID列表进行去重

### 3. 数据格式化
将去重后的数据格式化为飞书表格所需的格式：
```python
{
    "KOL ID": item.get("kol_id", ""),
    "Email": item.get("email", ""),
    "Account link": {
        "text": item["account_link"], 
        "link": item["account_link"]
    },
    "KOL Name": item.get("username", ""),
    "Template": item.get("template", ""),
    "App Code": item.get("project_code", "")
}
```

### 4. 批量写入
- 调用飞书API将数据写入目标表格（table_save）
- 处理写入响应，确认操作结果

### 5. 返回结果
返回包含以下信息的操作结果：
- 写入状态（成功/失败）
- 成功写入的记录数
- 错误信息（如果有）

## 注意事项
1. 确保所有表格URL格式正确且可访问
2. 处理URL参数提取过程中的异常情况
3. 确保输入数据中的必要字段（KOL ID、Email）不为空
4. 处理特殊字符和数据格式转换
5. 考虑批量写入的数量限制
6. 做好错误处理和日志记录

传入参数示例
stored_data:
```json
[
  {
    "kol_id": "TK_cia_maria",
    "kol_name": "cia_maria",
    "username": "Cia\u2002⭐️",
    "email": "<EMAIL>",
    "bio": "Aussie gal in London\nEHPlabs code LUCIA\n💌<EMAIL>",
    "account_link": "https://www.tiktok.com/@cia_maria",
    "platform": "tiktok",
    "engagement_rate": 0.*****************,
    "average_views_k": 50.028,
    "followers_k": 56.387,
    "likes_k": 6006.16,
    "keywords_ai": [
      "lifestyle"
    ],
    "source": "ttone",
    "level": "Mid-tier 50k～500k",
    "mean_views_k": 45.58,
    "median_views_k": 48.08,
    "db_id": "TK_cia_maria"
  },
  ...
]
```
platform: tiktok
query_params: 请求参数，包括：project_code，template 等参数

飞书查询返回结果：
```json
{
  "code": 0,
  "data": {
    "has_more": false,
    "items": [
      {
        "fields": {
          "Account link": {
            "link": "https://www.tiktok.com/@erikawheaton",
            "text": "https://www.tiktok.com/@erikawheaton"
          },
          "Email": [
            {
              "text": "<EMAIL>",
              "type": "text"
            }
          ],
          "KOL ID": [
            {
              "text": "erikawheaton",
              "type": "text"
            }
          ],
          "KOL Name": [
            {
              "text": "erikawheaton",
              "type": "text"
            }
          ],
          "Template": "TiKTok-V2"
        },
        "record_id": "recuGxdvolONcs"
      },
      {
        "fields": {
          "Account link": {
            "link": "https://www.tiktok.com/@nicolehoskens",
            "text": "https://www.tiktok.com/@nicolehoskens"
          },
          "Email": [
            {
              "text": "<EMAIL>",
              "type": "text"
            }
          ],
          "KOL ID": [
            {
              "text": "nicolehoskens",
              "type": "text"
            }
          ],
          "KOL Name": [
            {
              "text": "Nicole Hoskens,MS,RD",
              "type": "text"
            }
          ],
          "Template": "TiKTok-V2"
        },
        "record_id": "recuGxdvol5Edm"
      }
    ],
    "total": 2
  },
  "msg": "success"
}
```