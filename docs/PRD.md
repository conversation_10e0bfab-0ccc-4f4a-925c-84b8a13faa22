**KOL 数据管理与分析平台 PRD 文档（MVP 版本）**

---

# **1. 应用整体概览**
## 1.1 目标
构建一个最小可行化后端服务系统，通过 API 接口实现 KOL（关键意见领袖）数据的核心管理功能，支持基础数据采集、存储和智能分析。

## 1.2 系统架构
```
[数据源层] → [API 采集层] → [数据处理层] → [存储层] → [分析层] → [API 服务层]
```
- **数据源层**: 社交媒体平台（如微博、抖音、Instagram）
- **API 采集层**: 基于 Celery 的异步任务调度
- **数据处理层**: 数据清洗、归一化
- **存储层**: PostgreSQL（主库） + Redis（缓存）
- **分析层**: 大语言模型分析（基础指标计算+文本洞察）
- **API 服务层**: RESTful API（MVP阶段）

---

# **2. 用户使用流程**
## 2.1 典型用户角色
| 角色 | 用例场景 |
|------|----------|
| 管理员 | 配置数据采集任务，管理系统用户 |
| 数据分析师 | 执行基础分析任务 |
| 开发者 | 调用核心API接口 |

## 2.2 核心流程
```mermaid
graph TD
    A[配置采集任务] --> B[异步数据采集]
    B --> C[数据清洗存储]
    C --> D[发起LLM分析]
    D --> E[返回结构化结果]
```

---

# **3. 技术栈与 API 设计**
## 3.1 技术架构
| 组件 | 技术选型 | 说明 |
|------|----------|------|
| Web框架 | FastAPI 0.115.8 | 支持异步请求处理 |
| ORM | SQLAlchemy 2.0 | 异步数据库操作 |
| 消息队列 | Celery + Redis | 任务队列管理 |
| 数据库 | PostgreSQL 15 + asyncpg | 主数据存储 |
| 认证 | 暂时不考虑 | 简化认证流程 |
| AI分析 | 大语言模型API | 文本分析与洞察生成 |

## 3.2 API 模块设计
| 模块 | 接口示例 | 功能说明 |
|------|----------|----------|
| 数据采集 | POST /v1/kol/tasks | 创建采集任务 |
| 数据管理 | PUT /v1/kol/{kol_id} | 更新 KOL 元数据 |
| 数据分析 | POST /v1/ai-analysis/insights | 提交LLM分析请求 |

---

# **4. 核心功能**
## 4.1 数据采集模块（MVP）
- 核心平台爬虫支持（微博/抖音）
- 定时任务基础调度
- 数据去重校验（MD5指纹）
- 异常重试机制（最多 2 次重试）

## 4.2 数据管理模块
| 功能 | 技术实现 |
|------|----------|
| KOL 元数据存储 | PostgreSQL 标准字段 |
| 基础标签体系 | 平面标签结构 |
| 关键数据检索 | 基础SQL查询 |

## 4.3 数据分析模块（AI驱动）
- 基础指标计算（粉丝数/互动量）
- 文本内容分析（LLM生成标签/情感分析）
- 简易报告生成（JSON格式返回）

---

# **5. 项目范围**
## 5.1 In-Scope
- ✔️ 核心 API 接口开发（20+ 端点）
- ✔️ 异步任务处理基础框架
- ✔️ LLM分析接口对接
- ✔️ Swagger文档生成

## 5.2 Out-of-Scope
- ❌ JWT/OAuth认证系统
- ❌ 复杂机器学习模型
- ❌ 前端可视化界面
- ❌ 多租户支持 / 用户管理
- ❌ 数据版本控制

---

**附录：MVP阶段需求**
1. 性能要求：核心API响应时间 < 1s
2. 安全性：基础HTTPS传输加密
3. 扩展性：预留平台插件接口
4. 可靠性：每日定时数据备份

---

**版本记录**
- v1.1 MVP版本 (2024-XX-XX)
- 修改说明：
  - 移除JWT认证改用Basic Auth
  - 分析层改为LLM驱动
  - 简化数据存储结构
  - 缩减接口数量
- 评审人：技术负责人、产品经理
