# Celery + RabbitMQ + Redis 任务队列

本项目使用 Celery 作为任务队列，RabbitMQ 作为消息代理（Broker），Redis 作为结果后端（Result Backend）。

## 环境要求

1. Python 3.8+
2. RabbitMQ 服务
3. Redis 服务

## 配置说明

所有配置项都在 `.env` 文件中设置，主要包括：

```
# Redis 设置 (用于结果存储和任务监控)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# RabbitMQ 设置 (消息队列)
RABBITMQ_HOST=127.0.0.1
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=12345678
RABBITMQ_VHOST=/

# Celery 设置
CELERY_BROKER_URL=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@${RABBITMQ_HOST}:${RABBITMQ_PORT}/${RABBITMQ_VHOST}
CELERY_RESULT_BACKEND=redis://${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}
CELERY_WORKER_CONCURRENCY=1
CELERY_MONITORING_ENABLED=true
CELERY_TASK_TRACK_STARTED=true
CELERY_RESULT_EXPIRES=86400
```

## 项目结构

```
app/
├── worker/               # Celery相关代码
│   ├── celery_app.py     # Celery应用实例
│   ├── beat_schedule.py  # 定时任务配置
│   └── tasks/            # 任务定义
│       ├── example.py    # 示例任务
│       └── email.py      # 邮件任务
├── api/                  # API路由
│   └── v1/endpoints/
│       └── tasks.py      # 任务相关API
└── schemas/
    └── task.py           # 任务相关Schema

celery_worker.py          # Worker启动脚本
celery_beat.py            # Beat启动脚本
```

## 启动说明

### 1. 启动RabbitMQ和Redis服务

可以使用Docker或本地安装：

```bash
# 使用Docker启动
docker run -d --hostname rabbitmq --name rabbitmq -p 5672:5672 -p 15672:15672 -e RABBITMQ_DEFAULT_USER=admin -e RABBITMQ_DEFAULT_PASS=12345678 rabbitmq:3-management
docker run -d --name redis -p 6379:6379 redis:latest
```

### 2. 启动Celery Worker

可以使用提供的脚本启动Worker：

```bash
python celery_worker.py
```

或直接使用Celery命令：

```bash
celery -A app.worker.celery_app:celery_app worker --loglevel=INFO
```

### 3. 启动Celery Beat (可选，用于定时任务)

使用提供的脚本：

```bash
python celery_beat.py
```

或直接使用Celery命令：

```bash
celery -A app.worker.celery_app:celery_app beat --loglevel=INFO
```

### 4. 启动FastAPI应用

```bash
python -m app.main
```

## 任务API使用示例

### 创建简单任务

```python
# 在FastAPI路由中
from app.worker.tasks.example import add

@router.post("/calculate")
async def calculate(a: int, b: int):
    # 发送任务到队列
    task = add.delay(a, b)
    return {"task_id": task.id}
```

### 查询任务状态

```
GET /api/v1/tasks/{task_id}
```

响应示例：
```json
{
  "task_id": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6",
  "status": "SUCCESS",
  "result": 42,
  "error": null
}
```

### 取消任务

```
DELETE /api/v1/tasks/{task_id}
```

## 监控

推荐使用Flower监控Celery任务和Workers：

```bash
pip install flower
celery -A app.worker.celery_app:celery_app flower --port=5555
```

访问 http://localhost:5555 查看监控界面。

## 开发新任务

1. 在 `app/worker/tasks/` 目录下创建新的任务模块
2. 使用 `@shared_task` 或 `@celery_app.task` 装饰器定义任务
3. 在 `app/worker/celery_app.py` 的 `include` 列表中添加新模块

## 常见问题

1. **任务执行失败**：检查Worker日志，确保RabbitMQ和Redis服务正常运行
2. **定时任务没有执行**：确保Beat服务正在运行，检查时区设置是否正确
3. **Worker无法连接到Broker**：检查RabbitMQ的连接信息，用户名、密码和vhost是否正确 