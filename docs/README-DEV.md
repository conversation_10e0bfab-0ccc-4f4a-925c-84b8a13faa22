# KOL数据平台 - 开发环境启动指南

本文档提供了关于如何在开发环境中快速启动KOL数据平台的说明。

## 前提条件

在开始之前，请确保已经满足以下条件：

1. 已安装Python 3.12或更高版本
2. 已安装Poetry（用于依赖管理）
3. PostgreSQL数据库已经运行（或者在设置中选择使用SQLite）
4. Redis服务已经运行

## 依赖安装

如果尚未安装依赖，请运行以下命令：

```bash
# 创建虚拟环境并安装依赖
poetry install
```

## 环境配置

1. 确保`.env`文件存在于项目根目录（如果不存在，可以从`.env.example`复制）
2. 根据你的开发环境调整`.env`文件中的配置参数

## 使用启动脚本

我们提供了便捷的启动脚本，可以根据需要启动不同的服务组件。

### 在Unix/Linux/MacOS上

使用`start_dev.sh`脚本：

```bash
# 赋予执行权限（仅首次需要）
chmod +x start_dev.sh

# 启动API服务（默认）
./start_dev.sh

# 启动所有服务（API、Celery Worker和Beat）
./start_dev.sh --all

# 自定义启动选项
./start_dev.sh --api --worker --port 8080
```

### 在Windows上

使用`start_dev.bat`脚本：

```cmd
# 启动API服务（默认）
start_dev.bat

# 启动所有服务（API、Celery Worker和Beat）
start_dev.bat --all

# 自定义启动选项
start_dev.bat --api --worker --port 8080
```

## 可用启动选项

以下是可用的启动选项：

- `--all`：启动所有服务（API、Celery Worker和Beat）
- `--api`：启动API服务（默认开启）
- `--worker`：启动Celery Worker
- `--beat`：启动Celery Beat（定时任务调度器）
- `--port <端口>`：指定API服务端口号（默认：8000）
- `--no-reload`：禁用自动重载功能
- `--help`：显示帮助信息

## 访问API文档

启动API服务后，可以通过以下URL访问API文档：

- Swagger UI：http://localhost:8000/docs
- ReDoc：http://localhost:8000/redoc

## 调试提示

1. API服务默认启用了自动重载功能，修改代码后会自动重启
2. 查看日志输出可以帮助诊断问题
3. 可以通过`http://localhost:8000/test-celery/collect_kol_data`测试Celery任务功能

## 常见问题

1. **无法连接到数据库**：
   - 检查PostgreSQL服务是否已启动
   - 验证`.env`文件中的数据库连接参数是否正确

2. **Celery任务未执行**：
   - 确保Redis服务已启动
   - 检查Celery Worker是否已经正常启动

3. **端口冲突**：
   - 如果8000端口已被占用，可以使用`--port`参数指定其他端口

## 项目结构

核心项目结构如下：

```
project/
├── app/                # 核心应用代码
│   ├── api/            # API路由定义
│   ├── core/           # 核心配置和工具
│   ├── crud/           # 数据库操作函数
│   ├── db/             # 数据库设置
│   ├── models/         # 数据库模型
│   ├── schemas/        # Pydantic模型/方案定义
│   ├── services/       # 业务逻辑服务
│   └── worker/         # Celery任务定义
├── dev.py              # 开发环境启动脚本
├── start_dev.sh        # Unix启动脚本
├── start_dev.bat       # Windows启动脚本
├── .env                # 环境配置
├── .env.example        # 环境配置示例
└── pyproject.toml      # 项目依赖配置
``` 