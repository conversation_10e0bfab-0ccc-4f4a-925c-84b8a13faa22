# 日志系统使用说明

本项目使用 Loguru 实现了高效、直观的日志记录系统，为开发和运维提供完整的日志跟踪能力。

## 1. 日志分类

系统根据不同需求将日志分为以下几类：

### 1.1 通用日志

所有应用程序通用的日志记录，包含各个模块的常规日志。

- 文件位置：`logs/kol_platform.log`
- 日志级别：通过环境变量 `LOG_LEVEL` 设置，默认为 `INFO`
- 日志轮转：配置为达到10MB时自动轮转，保留5个备份文件

### 1.2 API请求日志

专门记录所有接口请求的日志，便于分析API使用情况和性能。

- 文件位置：`logs/api_requests.log`
- 记录内容：请求ID、方法、路径、客户端信息、处理时间、状态码等
- 日志格式：结构化格式，便于分析

### 1.3 Celery任务日志

专门记录Celery任务执行的日志，包含任务开始、结束、重试、失败等详细信息。

- 文件位置：`logs/celery_tasks.log`
- 记录内容：任务ID、任务名称、执行状态、参数、结果、错误信息等
- 适用场景：追踪和调试后台任务执行情况

### 1.4 错误日志

专门记录所有ERROR及以上级别的日志，便于快速定位错误。

- 文件位置：`logs/errors.log`
- 记录内容：包含错误详情、异常堆栈和上下文信息
- 错误跟踪：包含完整的异常栈和诊断信息

## 2. 使用方法

### 2.1 在FastAPI API中记录日志

请求日志已通过中间件自动记录，无需额外代码。如果需要在接口中添加自定义日志：

```python
from app.logging_config import get_request_logger

# 获取请求日志记录器
request_logger = get_request_logger()

@app.get("/example")
def example_endpoint():
    # 记录API请求日志
    request_logger.info("处理特殊请求逻辑")
    
    # 业务逻辑...
    return {"result": "success"}
```

### 2.2 在Celery任务中记录日志

任务的开始和结束已经通过信号自动记录。如果需要在任务中添加自定义日志：

```python
from app.logging_config import get_task_logger

@celery_app.task(name="example_task")
def example_task(param1, param2):
    # 获取任务特定的日志记录器
    task_logger = get_task_logger(
        task_id=example_task.request.id, 
        task_name=example_task.name
    )
    
    # 记录任务处理步骤
    task_logger.info(f"开始处理数据，参数: {param1}, {param2}")
    
    # 执行任务...
    result = process_data(param1, param2)
    
    # 记录任务处理结果
    task_logger.info(f"数据处理完成，结果: {result}")
    
    return result
```

### 2.3 在其他模块中记录日志

对于普通模块，可以使用模块特定的日志记录器：

```python
from app.logging_config import get_logger

# 获取模块特定的日志记录器
logger = get_logger("app.services.my_service")

def some_function():
    logger.debug("调试信息")
    logger.info("一般信息")
    logger.warning("警告信息")
    logger.error("错误信息")
    logger.critical("严重错误信息")
```

## 3. 查看和分析日志

### 3.1 实时查看日志

可以使用以下命令实时查看各类日志：

```bash
# 查看所有日志
tail -f logs/kol_platform.log

# 查看API请求日志
tail -f logs/api_requests.log

# 查看Celery任务日志
tail -f logs/celery_tasks.log

# 查看错误日志
tail -f logs/errors.log
```

### 3.2 日志搜索

可以使用grep等工具搜索日志中的特定内容：

```bash
# 搜索特定任务ID的日志
grep "task_id=12345" logs/celery_tasks.log

# 搜索特定API路径的请求日志
grep "/api/v1/tasks" logs/api_requests.log

# 搜索所有ERROR级别的日志
grep "ERROR" logs/kol_platform.log
```

### 3.3 日志分析

对于更复杂的日志分析，可以使用ELK(Elasticsearch, Logstash, Kibana)等工具。本项目的日志格式设计已考虑这一点，便于集成。

## 4. 日志配置参数

可以通过环境变量或修改 `app/core/config.py` 文件来调整以下日志配置：

- `LOG_LEVEL`: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `LOG_FILE_PATH`: 日志文件目录
- `LOG_FILE_NAME`: 主日志文件名
- `LOG_MAX_BYTES`: 单个日志文件最大大小（轮转阈值）
- `LOG_BACKUP_COUNT`: 保留的备份文件数量

## 5. 日志系统维护

系统已配置日志文件轮转，以防止日志文件过大。但对于长期运行的系统，建议定期清理旧的日志文件或配置更系统化的日志管理方案。

## 6. 故障排查

如果遇到日志相关问题，请检查：

1. 日志目录权限是否正确
2. 磁盘空间是否充足
3. 应用是否有权限写入日志文件
4. 环境变量配置是否正确 