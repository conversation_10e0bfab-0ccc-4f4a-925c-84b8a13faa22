# KOL高级检索功能使用指南

## 功能概述

KOL高级检索功能允许用户对KOL数据进行精确、灵活的多条件查询，支持所有字段的各种查询操作，包括等于、不等于、包含、不包含、为空和不为空。该功能同时兼容现有的按平台、筛选条件名称和项目代码查询，提供了强大的数据过滤能力。

## API端点

高级检索提供了两个API端点，分别支持同步和异步调用方式：

- **同步API**: `POST /api/v1/kol-info/advanced-search`
- **异步API**: `POST /api/v1/kol-info/async/advanced-search`

## 请求参数

请求体需要使用JSON格式，包含以下字段：

```json
{
  "conditions": [
    {
      "field": "字段名",
      "operator": "操作符",
      "value": "搜索值"
    }
  ],
  "platform": "平台名称（可选）",
  "filter_name": "筛选条件名称（可选）",
  "project_code": "项目代码（可选）",
  "skip": 0,
  "limit": 100
}
```

### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| conditions | 数组 | 是 | 搜索条件列表，每个条件包含field、operator和value |
| platform | 字符串 | 否 | 平台名称，如"TIKTOK"、"INSTAGRAM"等 |
| filter_name | 字符串 | 否 | 筛选条件名称 |
| project_code | 字符串 | 否 | 项目代码 |
| skip | 整数 | 否 | 分页偏移量，默认为0 |
| limit | 整数 | 否 | 每页数据量，默认为100 |

### 搜索条件

每个搜索条件包含三个部分：

1. **field**: 字段名，对应KOL模型的属性
2. **operator**: 操作符，支持以下6种操作：
   - `eq`: 等于
   - `ne`: 不等于
   - `contains`: 包含
   - `not_contains`: 不包含
   - `is_null`: 为空
   - `is_not_null`: 不为空
3. **value**: 搜索值，对于`is_null`和`is_not_null`操作可以省略

## 支持的字段

高级检索支持KOL数据模型的所有字段，主要包括：

### 基本信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| kol_id | 字符串 | KOL唯一标识 |
| kol_name | 字符串 | KOL名称 |
| username | 字符串 | 用户名 |
| email | 字符串 | 电子邮箱 |
| bio | 字符串 | 个人简介 |
| account_link | 字符串 | 社交账号链接 |
| platform | 枚举 | 社交平台 |
| source | 字符串 | 数据来源 |
| slug | 字符串 | 用于获取unlock邮箱的ID |
| creator_id | 字符串 | 创建者ID |

### 指标数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| followers_k | 浮点数 | 粉丝数(K) |
| likes_k | 浮点数 | 点赞数(K) |
| mean_views_k | 浮点数 | 近15天播放平均数(K) |
| median_views_k | 浮点数 | 近15天视频播放中位观看数(K) |
| engagement_rate | 浮点数 | 互动率 |
| average_views_k | 浮点数 | 平均观看数(K) |
| average_likes_k | 浮点数 | 平均点赞数(K) |
| average_comments_k | 浮点数 | 平均评论数(K) |
| most_used_hashtags | 数组 | 最常用的标签 |
| level | 字符串 | KOL等级 |
| keywords_ai | 数组 | AI生成的关键词 |

### 时间信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| created_at | 日期时间 | 创建时间 |
| updated_at | 日期时间 | 更新时间 |

## 使用示例

### 示例1：查找名字中包含"张"的KOL

```json
{
  "conditions": [
    {
      "field": "kol_name",
      "operator": "contains",
      "value": "张"
    }
  ],
  "skip": 0,
  "limit": 20
}
```

### 示例2：查找TikTok平台上粉丝数超过100K且有邮箱的KOL

```json
{
  "conditions": [
    {
      "field": "followers_k",
      "operator": "eq",
      "value": 100
    },
    {
      "field": "email",
      "operator": "is_not_null"
    }
  ],
  "platform": "TIKTOK",
  "skip": 0,
  "limit": 20
}
```

### 示例3：查找使用特定标签且互动率高于3%的KOL

```json
{
  "conditions": [
    {
      "field": "most_used_hashtags",
      "operator": "contains",
      "value": "美食"
    },
    {
      "field": "engagement_rate",
      "operator": "eq",
      "value": 0.03
    }
  ],
  "skip": 0,
  "limit": 20
}
```

### 示例4：查找某个筛选条件下近期更新的KOL

```json
{
  "conditions": [
    {
      "field": "updated_at",
      "operator": "contains",
      "value": "2023-09"
    }
  ],
  "filter_name": "高价值KOL",
  "skip": 0,
  "limit": 20
}
```

## 响应格式

API响应采用标准的分页格式，包含以下字段：

```json
{
  "items": [
    // KOL对象列表
  ],
  "total": 100,
  "page": 1,
  "size": 20,
  "pages": 5
}
```

### 字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| items | 数组 | KOL信息对象列表 |
| total | 整数 | 满足条件的总记录数 |
| page | 整数 | 当前页码 |
| size | 整数 | 每页大小 |
| pages | 整数 | 总页数 |

## 性能优化

高级检索功能采用了多种性能优化措施：

1. **索引优化**：所有可搜索字段都添加了适当的索引
2. **JSON查询优化**：对JSON字段使用GIN索引和专门的JSON操作符
3. **子查询优化**：使用子查询减少数据库访问
4. **异步查询**：提供异步API减少IO等待时间

## 查询处理细节

1. **字符串字段**：使用`ILIKE`进行模糊匹配
2. **数值字段**：提供精确比较
3. **JSON数组**：支持数组元素包含查询
4. **枚举字段**：支持精确匹配
5. **日期时间**：支持日期包含查询

## 注意事项

1. 当提供`platform`、`filter_name`和`project_code`参数时，优先级顺序为：platform > filter_name > project_code
2. 为保证查询性能，建议合理设置`limit`值，避免一次查询过多数据
3. 对于复杂查询，推荐使用异步API端点
4. JSON字段的查询支持字符串包含和数组包含两种模式
5. 无效的字段名会被自动忽略

## 错误处理

API可能返回以下错误：

- **400 Bad Request**: 请求格式错误
- **404 Not Found**: 指定的资源不存在
- **500 Internal Server Error**: 服务器内部错误

## 与其他API的集成

高级检索API可以与以下API结合使用：

- KOL信息创建和更新API
- 筛选条件管理API
- KOL与筛选条件关联API

## 开发者工具

推荐使用以下工具测试高级检索API：

- Postman或Insomnia进行API测试
- pgAdmin或其他PostgreSQL客户端查看查询计划
- Django Debug Toolbar分析API性能 