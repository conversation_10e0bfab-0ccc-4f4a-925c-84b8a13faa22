# KOL数据平台生产环境部署指南

本文档提供了如何在生产环境中部署KOL数据平台的详细步骤。本部署使用已有的外部数据库和Redis服务。

## 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 外部PostgreSQL数据库
- 外部Redis服务
- 最低配置：2核CPU，4GB内存

## 部署步骤

### 1. 准备环境

1.1 克隆代码库：

```bash
git clone <repository_url>
cd kol-platform
```

1.2 创建日志目录：

```bash
mkdir -p logs
```

### 2. 配置环境变量

2.1 创建生产环境配置文件：

```bash
cp .env.production .env
```

2.2 编辑 `.env` 文件，设置适合生产环境的参数：

- 配置外部数据库连接信息(POSTGRES_*)
- 配置外部Redis连接信息(REDIS_*)
- 设置CORS允许的域名
- 配置第三方服务凭据

### 3. 构建和启动服务

3.1 构建Docker镜像：

```bash
docker-compose build
```

3.2 启动服务：

```bash
docker-compose up -d
```

3.3 检查服务状态：

```bash
docker-compose ps
```

### 4. 数据库初始化

4.1 运行数据库迁移：

```bash
docker-compose exec api alembic upgrade head
```

4.2 （可选）导入初始数据：

```bash
docker-compose exec api python -m scripts.import_initial_data
```

### 5. 监控和日志

5.1 查看服务日志：

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs api
docker-compose logs worker
```

5.2 服务运行状况检查：

访问 `http://your-server-ip:8000/health` 查看API服务运行状况。

### 6. 维护操作

6.1 更新应用：

```bash
git pull
docker-compose build
docker-compose up -d
```

6.2 重启服务：

```bash
docker-compose restart api        # 重启API服务
docker-compose restart worker     # 重启Celery Worker
docker-compose restart            # 重启所有服务
```

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查日志：`docker-compose logs <service_name>`
   - 确认端口未被占用：`netstat -tulpn | grep <port_number>`

2. **数据库连接问题**
   - 检查数据库配置：`.env` 文件中的数据库参数
   - 确保外部数据库可访问
   - 检查网络连通性：`docker-compose exec api ping <your-db-host>`

3. **Celery任务不执行**
   - 检查Redis连接：`docker-compose exec worker redis-cli -h <your-redis-host> -a <password> ping`
   - 检查Celery日志：`docker-compose logs worker`

## 安全建议

1. 使用强密码保护数据库和Redis
2. 限制数据库和Redis的外部访问
3. 定期更新依赖和Docker镜像
4. 如需暴露API到公网，建议使用反向代理并启用HTTPS

## 参考资料

- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Celery官方文档](https://docs.celeryproject.org/) 