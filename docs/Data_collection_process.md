## 下面是一个数据采集流程：
- 输入参数：
```json
{
  "source": "creable/modash/ttone",
  "platform": "TikTok/Instagram/YouTube",
  "auto_send": "no/yes",
  "template": "tempv1",
  "cookie": "string",
  "filter_body": "string",
  "filter_name": "string",
  "crawl_method": "A/B/C",
  "record_id": "string",
  "high_potential": "no/yes",
  "project_code": "oog116",
  "table_duplicate": "xxxxxxx",
  "table_save": "xxxxxxx"
}
```
- 下面是执行步骤：
1. 判断 source 执行不同的爬取代码
2. 获取到数据整理为统一格式
3. 将数据传递给 TikTok/Instagram/YouTube 爬虫服务
4. 每查询到一条数据，就存储一条到数据库
5. 存储完成后，判断 auto_send ，为yes，将数据存储到指定飞书表（飞书表要去重），为no不管
6. 接着判断 high_potential，为yes，将数据库存储到 table_save 对应的飞书表，需要根据 table_duplicate 对应表进行去重

### 详细流程如下：
```mermaid
graph TD
    A[Start] --> B{参数校验}
    B -->|校验通过| C{Source判断}
    B -->|校验失败| Z[异常终止]
    C -->|creable| D[执行 Creable 采集模块]
    C -->|modash | E[执行 Modash  采集模块]
    C -->|ttone  | F[执行 TTONE   采集模块]
    D --> G[数据标准化处理]
    E --> G
    F --> G
    G --> H{platform平台判断}
    H -->|TikTok   | I[TikTok    爬虫服务]
    H -->|Instagram| J[Instagram 爬虫服务]
    H -->|YouTube  | K[YouTube   爬虫服务（暂无）]
    I --> L[实时存储数据库]
    J --> L
    K --> L
    L --> M{auto_send?}
    M -->|yes| N[飞书表去重存储（存储到邮件表）]
    M -->|no | W[pass]
    N --> O{high_potential?}
    O -->|yes| P[高潜力数据去重存储（存储到table_save表，依据table_duplicate去重）]
    O -->|no | Q[流程结束]
    P --> Q
    Z --> Q
```

### L 实时存储数据库入库流程：

数据库入库流程分为以下几个步骤：

1. 首先将过滤条件数据存储到 `app/models/filter_data.py` 表中：
   - 存储字段：filter_body, filter_name, project_code
   - language, gender, location 字段暂时为空值
   - 数据插入逻辑：
     - 检查是否存在相同的 filter_body、filter_name 和 project_code
     - 如果存在，则更新该记录
     - 如果不存在，则新增记录
   
2. 将爬取后的标准化数据存储到 `app/models/kol_info.py` 表中：
   - 包含 kol_id, kol_name, username, email 等字段
   - 数据插入逻辑：
     - 检查是否存在相同的 kol_id
     - 如果存在，则更新该记录
     - 如果不存在，则新增记录
   - 采集程序中的 'videos' 字段数据单独存储到 `app/models/video_info.py` 表中

3. 将视频数据存储到 `app/models/video_info.py` 表中：
   - 直接插入数据，不做去重处理
   - 通过 kol_id 外键关联到 kol_info 表

4. 表之间的关联关系：
   - filter_data 和 kol_info 是多对多关系：
     - 通过中间表 `app/models/filter_kol_association.py` 建立关联
     - 每当一个 KOL 符合某个过滤条件时，在中间表中创建关联记录
   - kol_info 和 video_info 是一对多关系：
     - 一个 KOL 可以有多个视频
     - video_info 表中通过外键关联到 kol_info 表

```mermaid
graph TD
    A[开始数据入库] --> B{检查 filter_data}
    B -->|存在相同记录| C[更新 filter_data]
    B -->|不存在| D[新增 filter_data]
    C --> E{检查 kol_info}
    D --> E
    E -->|存在相同 kol_id| F[更新 kol_info]
    E -->|不存在| G[新增 kol_info]
    F --> H[直接插入 video_info]
    G --> H
    H --> I[创建关联关系]
    I --> J[入库完成]
```

### 数据库表结构关系图：
```mermaid
erDiagram
    filter_data ||--o{ filter_kol_association : contains
    filter_kol_association }o--|| kol_info : belongs_to
    kol_info ||--o{ video_info : has

    filter_data {
        int id PK
        string language
        string gender
        string location
        json filter_body
        string filter_name
        string project_code
        datetime created_at
        datetime updated_at
    }

    kol_info {
        string kol_id PK
        string kol_name
        string username
        string email
        string bio
        string account_link
        float followers_k
        float likes_k
        enum platform
        string source
        string slug
        string creator_id
        float mean_views_k
        float median_views_k
        float engagement_rate
        float average_views_k
        float average_likes_k
        float average_comments_k
        json most_used_hashtags
        string level
        json keywords_ai
        datetime created_at
        datetime updated_at
    }

    video_info {
        string video_id PK
        string kol_id FK
        int play_count
        boolean is_pinned
        string share_url
        string desc
        string desc_language
        string video_url
        string music_url
        int likes_count
        int comments_count
        int shares_count
        int collect_count
        enum platform
        json hashtags
        datetime create_time
        datetime created_at
        datetime updated_at
    }

    filter_kol_association {
        int id PK
        int filter_id FK
        string kol_id FK
        string project_code
        datetime created_at
        datetime updated_at
    }
```

### 细节说明
1. 采集模块代码分别对应关系
Creable ---> services/creable_crawler_service.py
Modash  ---> services/modash_crawler_service.py
TTONE   ---> services/ttone_crawler_service.py

tiktok 数据采集代码：services/tiktok_crawler_service.py
instagram 数据采集代码：services/instagram_service.py
youtube 数据采集代码：暂无/可使用伪代码

2. 采集模块返回的数据格式：
Creable:
```json
[
  {
    "account": {
      "user_profile": {
        "user_id": "6748733276015821829",
        "sec_uid": "MS4wLjABAAAA3_jMMOMVnto1NLRvTHnNzwv1Hcumx_dTBorszwZK2qJQmOMgRG1ZhxTMyo1jwSkS",
        "username": "lukefernandez59",
        "url": "https://www.tiktok.com/share/user/6748733276015821829",
        "picture": "https://imgp.sptds.icu/v2?4e7GkPsCtkrXRAYTZgKXbVo7gyb6lfPjUhIO1dCG%2Fyxs3WYJbRVN0FblCXok06jVHsZgl6YZ%2BzfA9mEAY2MFwKRWY2%2BV6AKioHBw1A%2FuAFgsr3NfwBjPuNT0g%2FLlYTixTqXqAgl%2F6hKGpq0NR%2BEADt8qYMz2XWWBVAYsVJSYSU3Q6amLljmQ%2BuBP4okR8%2Bl5",
        "fullname": "Luke Fernandez",
        "is_verified": false,
        "followers": 245300,
        "engagements": 12128,
        "engagement_rate": 0.*****************,
        "avg_views": 132131
      },
      "audience_source": "any"
    },
    "match": {
      "user_profile": {
        "geo": {
          "country": {
            "id": 148838,
            "name": "United States",
            "code": "US",
            "coords": {
              "lat": 38.89511,
              "lon": -77.03637
            }
          }
        }
      },
      "audience_likers": {
        "data": {
          "audience_geo": {
            "countries": [
              {
                "id": 148838,
                "name": "United States",
                "code": "US",
                "weight": 0.8041910770617395
              }
            ]
          }
        }
      }
    },
    "status": "unlocked",
    "listIDs": []
  }
]
```

Modash:
```json
[
    {
        "_id": "611e9848db304b205c007f98",
        "profileType": "TIKTOK",
        "profileData": {
            "userId": "6746634497682752518",
            "secUid": "MS4wLjABAAAAN68v8xClDt22f9ciSxYzwfJFxXigOCLGo8i7m2qWH8P4FjfjJ3ytBYsaKFxbRZ2c",
            "profile": {
                "engagementRate": 0.03130783466995682,
                "engagements": 296,
                "followers": 297200,
                "fullname": "Victoria Sisco",
                "picture": "https://imgigp.modash.io/v2?4e7GkPsCtkrXRAYTZgKXbVo7gyb6lfPjUhIO1dCG%2Fyxs3WYJbRVN0FblCXok06jVHsZgl6YZ%2BzfA9mEAY2MFwKPrjUOHLDj23deo2bJS6662lEgbJDiQzd6mZr1IWjt6TqXqAgl%2F6hKGpq0NR%2BEADt8qYMz2XWWBVAYsVJSYSU3Q6amLljmQ%2BuBP4okR8%2Bl5",
                "url": "https://www.tiktok.com/@flowwithvictoria",
                "username": "flowwithvictoria",
                "isVerified": false,
                "averageViews": 9536
            }
        },
        "profileId": "611e9848db304b205c007f95"
    }
]
```

TTONE:
```json
[
    {
    "aioCreatorID": 7059223225376440325,
    "contentLabels": [
        {
        "labelID": 11003001,
        "labelLevel": 2
        }
    ],
    "creatorBadgeType": 1,
    "creatorRecommendReason": {
        "placeHolders": [],
        "starlingKey": "aio_explore_creators_v2_very_active"
    },
    "creatorTTInfo": {
        "avatarURL": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/2fdd64336bfb592dfd143b22b2cd5dae~tplv-tiktokx-dy-360p.jpeg?lk3s=d95d8f79&nonce=17976&refresh_token=c316ef3f433c21258af32cdb2b88833d&x-expires=1743080400&x-signature=DV3bToCG7vyA2EeFKn9recOpuz0%3D&shp=d95d8f79&shcp=-",
        "bio": "",
        "handleName": "novita.lam",
        "isBannedInTT": false,
        "nickName": "Novita Lam",
        "riskInfo": {
        "creatorID": 7059223225376440325,
        "disciplineInfoList": [],
        "riskEventInfoList": []
        },
        "storeRegion": "SG"
    },
    "creatorType": 1,
    "esData": {
        "price": {
        "currency": "SGD",
        "startingRate100k": 200000000,
        "storeRegionCurrency": "SGD",
        "storeRegionStartingRate100k": 200000000
        },
        "status": 1
    },
    "industryLabels": [
        {
        "labelID": 21000000000,
        "labelLevel": 1
        }
    ],
    "recentItems": [
        {
        "comment": 30,
        "coverURL": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/oIqMDf2YIEz4Q2eXMBPAFCdzQbjfAs0ukDMN3g~tplv-photomode-zoomcover-tcm:720:720.avif?dr=16670&nonce=45083&refresh_token=ffd692b5736e8cf3cd307ad75249522b&x-expires=1743663600&x-signature=gOjKO2z76%2Bw3bEzOxWZjeV6sV5g%3D&idc=my&ps=933b5bde&shcp=191a13c7&shp=c6b69ba0&t=e8257cf9",
        "coverURLList": [
            {
            "format": "avif",
            "imageUrl": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/oIqMDf2YIEz4Q2eXMBPAFCdzQbjfAs0ukDMN3g~tplv-photomode-zoomcover-tcm:720:720.avif?dr=16670&nonce=45083&refresh_token=ffd692b5736e8cf3cd307ad75249522b&x-expires=1743663600&x-signature=gOjKO2z76%2Bw3bEzOxWZjeV6sV5g%3D&idc=my&ps=933b5bde&shcp=191a13c7&shp=c6b69ba0&t=e8257cf9"
            },
            {
            "format": "jpeg",
            "imageUrl": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/oIqMDf2YIEz4Q2eXMBPAFCdzQbjfAs0ukDMN3g~tplv-photomode-zoomcover-tcm:720:720.jpeg?dr=16670&nonce=26319&refresh_token=be7b42a3fda810010a3971f707fd9229&x-expires=1743663600&x-signature=%2Bppmflo6cDjZVnsfWl0t55gaU2k%3D&idc=my&ps=933b5bde&shcp=191a13c7&shp=c6b69ba0&t=e8257cf9"
            }
        ],
        "createTime": 1738144609,
        "heart": 870,
        "itemID": 7465274230083521810,
        "recommendReason": 1,
        "share": 49,
        "title": "\u521d\u4e00ootd! \ud83c\udf4a\ud83c\udf4a\u2728 happy CNY everybody \u795d\u5927\u5bb6\u86c7\u5e74\u86c7plus #CNY2025",
        "videoURL": "https://v77.tiktokcdn.com/d4367ce0a67e70a4d81af17d509c76cc/67e54d7e/video/tos/alisg/tos-alisg-pve-0037c001/oY7eZfGogMLIbEs3eiAAQeRLvfzxdcvUsRYMg9/?a=2043&bti=NjQ7OGYwLzA6&ch=0&cr=0&dr=0&er=0&lr=default&cd=0%7C0%7C0%7C0&br=1418&bt=709&cs=0&ds=6&ft=.NpOcInz7Th~xKyOXq8Zmo&mime_type=video_mp4&qs=0&rc=aGlpZ2dkOTo3OmU1ODc2NkBpamVpNnU5cjl5eDMzODczNEBfLmAwXzY2NV4xXzI2NjNfYSNvaGswMmQ0aWBgLS1kMWBzcw%3D%3D&vvpl=1&l=20250327070658097909985F70923778D3&btag=e000b0000&cc=13",
        "views": 20765
        },
        {
        "comment": 405,
        "coverURL": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/1dd0fdc6407a4692b04ac710ce0afb29_1612680012~tplv-photomode-zoomcover-tcm:720:720.avif?dr=16670&nonce=55486&refresh_token=6d0443a2ff7b6c59e53498d05b8ee233&x-expires=1743663600&x-signature=9YcGcXi4WTk1RUeGsfecOhpTESA%3D&idc=my&ps=933b5bde&shcp=191a13c7&shp=c6b69ba0&t=e8257cf9",
        "coverURLList": [
            {
            "format": "avif",
            "imageUrl": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/1dd0fdc6407a4692b04ac710ce0afb29_1612680012~tplv-photomode-zoomcover-tcm:720:720.avif?dr=16670&nonce=55486&refresh_token=6d0443a2ff7b6c59e53498d05b8ee233&x-expires=1743663600&x-signature=9YcGcXi4WTk1RUeGsfecOhpTESA%3D&idc=my&ps=933b5bde&shcp=191a13c7&shp=c6b69ba0&t=e8257cf9"
            },
            {
            "format": "jpeg",
            "imageUrl": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/1dd0fdc6407a4692b04ac710ce0afb29_1612680012~tplv-photomode-zoomcover-tcm:720:720.jpeg?dr=16670&nonce=5927&refresh_token=84758db77e73dca25fc8a6590f63eaeb&x-expires=1743663600&x-signature=qljzSMGKztIj%2BpNKd3WJM8c00Q8%3D&idc=my&ps=933b5bde&shcp=191a13c7&shp=c6b69ba0&t=e8257cf9"
            }
        ],
        "createTime": 1612680011,
        "heart": 17022,
        "itemID": 6926407893793115394,
        "recommendReason": 2,
        "share": 668,
        "title": "#fyp",
        "videoURL": "https://v77.tiktokcdn.com/31b11da522d7b4785fe1a8613b58a847/67e54d81/video/tos/alisg/tos-alisg-pve-0037/d4ec7eaf1b264377a0f4b73b55d47339/?a=2043&bti=NjQ7OGYwLzA6&ch=0&cr=0&dr=0&er=0&lr=default&cd=0%7C0%7C0%7C0&cv=1&br=5054&bt=2527&cs=0&ds=6&ft=.NpOcInz7Th~xKyOXq8Zmo&mime_type=video_mp4&qs=0&rc=aGg4aTw5PDs6aDQ3NDVlaUBpam92dGtrb2hyMzMzZjgzM0BhMjQwMjBfX2AxY2NiYS02YSNwanFtbjZvamNgLS1eLzRzcw%3D%3D&vvpl=1&l=20250327070658097909985F70923778D3&btag=e000b8000&cc=13",
        "views": 357843
        },
        {
        "comment": 17,
        "coverURL": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/5f42edd111874363822ac7c8f46f5369_**********~tplv-photomode-zoomcover-tcm:720:720.avif?dr=16670&nonce=92425&refresh_token=af919919f0cdae8ff1f8b2e84ff87465&x-expires=1743663600&x-signature=9gPsiogiQVzBSadlOehDGJpJN94%3D&idc=my&ps=933b5bde&shcp=191a13c7&shp=c6b69ba0&t=e8257cf9",
        "coverURLList": [
            {
            "format": "avif",
            "imageUrl": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/5f42edd111874363822ac7c8f46f5369_**********~tplv-photomode-zoomcover-tcm:720:720.avif?dr=16670&nonce=92425&refresh_token=af919919f0cdae8ff1f8b2e84ff87465&x-expires=1743663600&x-signature=9gPsiogiQVzBSadlOehDGJpJN94%3D&idc=my&ps=933b5bde&shcp=191a13c7&shp=c6b69ba0&t=e8257cf9"
            },
            {
            "format": "jpeg",
            "imageUrl": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/5f42edd111874363822ac7c8f46f5369_**********~tplv-photomode-zoomcover-tcm:720:720.jpeg?dr=16670&nonce=4448&refresh_token=8a7ef9cb548aa9a15458a91f2567dd9e&x-expires=1743663600&x-signature=asb68QvzSW5DeMa%2Brhtpqv%2BNbrg%3D&idc=my&ps=933b5bde&shcp=191a13c7&shp=c6b69ba0&t=e8257cf9"
            }
        ],
        "createTime": **********,
        "heart": 3959,
        "isSponsoredVideo": true,
        "itemID": 7096786261511277825,
        "recommendReason": 3,
        "share": 61,
        "title": "Skip the mundane activities & join me in the Under Armour All Out Mile Challenge!Happening 1 - 5 June. Register now at UAALLOUTMILE.com #UnderArmourSG #UAAllOutMile #TheOnlyWayIsThrough #Fitness #Healthy",
        "videoURL": "https://v77.tiktokcdn.com/f11ae3bc023fe80a9b04d72d0908bd88/67e54d85/video/tos/alisg/tos-alisg-pve-0037/eee23d5606ab47d5be0b6b1ac9203023/?a=2043&bti=NjQ7OGYwLzA6&ch=0&cr=0&dr=0&er=0&lr=default&cd=0%7C0%7C0%7C0&cv=1&br=2368&bt=1184&cs=0&ds=6&ft=.NpOcInz7Th~xKyOXq8Zmo&mime_type=video_mp4&qs=0&rc=NGRlOjs5ZjxpM2YzNjwzNkBpanR0NTU6ZnZnPDMzODgzNEAxXy8wXmNhXzYxX2AzNi9hYSNkbmtscjQwZnJgLS1kLy1zcw%3D%3D&vvpl=1&l=20250327070658097909985F70923778D3&btag=e000b8000&cc=13",
        "views": 393269
        }
    ],
    "riskInfo": {
        "creatorID": 7059223225376440325,
        "disciplineInfoList": [],
        "riskEventInfoList": []
    },
    "statisticData": {
        "overallPerformance": {
            "engagementRate": 0.036443307995796204,
            "followerCount": 116972,
            "medianViews": 26316
        }
    },
    "ttUID": 6622073058956967938
    }
]
```

3. 数据标准化处理说明
统一数据格式：
```json
{
  "sec_uid": "",
  "kol_id": "",
  "kol_name": "",
  "username": "",
  "url": "",
  "engagement_rate": "",
  "averageViews": ""
}
```
Creable 有效字段：
sec_uid         --->  account.user_profile.sec_uid
kol_id          --->  前缀_ + account.user_profile.username   前缀为 TK_ / INS / TY 分别代表 TikTok / Instagram / YouTube
kol_name        --->  account.user_profile.username
username        --->  account.user_profile.fullname
url             --->  account.user_profile.url
engagement_rate --->  account.user_profile.engagement_rate
averageViews    --->  account.user_profile.avg_views

Modash 有效字段：
sec_uid         --->   profileData.secUid
engagement_rate --->   profileData.profile.engagementRate
url             --->   profileData.profile.url
kol_id          --->   前缀_ + profileData.profile.username   前缀为 TK_ / INS / TY 分别代表 TikTok / Instagram / YouTube
kol_name        --->   profileData.profile.username
username        --->   profileData.profile.fullname
averageViews    --->   profileData.profile.averageViews

TTONE 有效字段：(ttone平台比较特殊，需要特殊处理)
engagement_rate   --->    statisticData.overallPerformance.engagementRate
averageViews      --->    statisticData.overallPerformance.medianViews
kol_id            --->    前缀_ + creatorTTInfo.handleName    前缀为 TK_ / INS / TY 分别代表 TikTok / Instagram / YouTube
kol_name          --->    creatorTTInfo.handleName
username          --->    creatorTTInfo.nickName
url               --->    https://www.tiktok.com/@ + creatorTTInfo.handleName
sec_uid           --->    为空值
