# KOL数据采集任务系统

基于Celery + RabbitMQ + Redis实现的KOL数据采集异步任务系统，支持长时间运行的数据采集任务。

## 系统概述

本系统使用Celery作为任务队列，RabbitMQ作为消息代理，Redis作为结果存储后端，实现KOL数据采集的异步处理。支持以下功能：

1. 异步执行长时间运行的KOL数据采集任务
2. 实时监控任务进度和状态
3. 任务重试机制，确保数据采集的可靠性
4. 定时任务自动生成统计报告和清理过期数据
5. 多队列支持，优化资源分配

## 架构设计

系统架构如下：

```
FastAPI应用 <----> RabbitMQ <----> Celery Workers
                      |
                      v
                    Redis <-----> 监控系统(Flower)
```

- **FastAPI应用**：接收用户请求，将任务发送到RabbitMQ队列
- **RabbitMQ**：消息队列，存储待处理的任务
- **Celery Workers**：执行KOL数据采集任务的工作进程
- **Redis**：存储任务执行结果和进度
- **Flower**：监控Celery任务和workers的Web界面

## 主要组件

1. **KOL数据采集任务**：`app/worker/tasks/kol_data.py`
2. **任务API接口**：`app/api/v1/endpoints/tasks.py`
3. **任务数据模型**：`app/schemas/task.py`
4. **Celery应用配置**：`app/worker/celery_app.py`
5. **定时任务配置**：`app/worker/beat_schedule.py`

## 使用方法

### 1. 启动服务

首先确保RabbitMQ和Redis服务已启动：

```bash
# 使用Docker启动依赖服务
docker-compose up -d rabbitmq redis
```

然后启动KOL数据采集专用Worker：

```bash
# 启动专门处理KOL数据采集的Worker
./start_worker_kol.sh
```

启动所有Worker和Beat进程：

```bash
# 启动Worker和Beat进程
./start_celery.sh
```

启动FastAPI应用：

```bash
uvicorn app.main:app --reload
```

### 2. 创建KOL数据采集任务

通过API创建数据采集任务：

```bash
curl -X POST "http://localhost:8000/api/v1/tasks/kol/collect" \
  -H "Content-Type: application/json" \
  -d '{
    "source": "creable",
    "platform": "Tiktok",
    "filter_name": "my-kol-filter",
    "filter_body": {
      "page": 0,
      "platform": "tiktok",
      "filter": {
        "followers": {"left_number": 1000, "right_number": 500000}
      }
    },
    "cookie": "your_cookie_value",
    "project_code": "test001",
    "auto_send": "yes",
    "high_potential": "yes"
  }'
```

### 3. 查询任务状态

通过API查询任务状态：

```bash
curl "http://localhost:8000/api/v1/tasks/kol/{task_id}/status"
```

### 4. 监控任务

使用Flower监控任务执行情况：

```bash
# 启动Flower监控
celery -A app.worker.celery_app:celery_app flower --port=5555
```

然后访问 http://localhost:5555 查看监控界面。

## 定时任务

系统配置了以下定时任务：

1. **数据清理**：每天零点清理30天前的过期任务结果
2. **统计报告**：每周一生成KOL数据采集的统计报告

## 开发指南

### 添加新任务

1. 在 `app/worker/tasks/` 目录下创建新的任务模块
2. 使用 `@celery_app.task` 或 `@shared_task` 装饰器定义任务
3. 在 `app/worker/celery_app.py` 的 `include` 列表中添加新模块
4. 更新 `task_routes` 配置，将任务路由到合适的队列

### 实现新API

1. 在 `app/api/v1/endpoints/tasks.py` 中添加新的API端点
2. 在 `app/schemas/task.py` 中定义请求和响应模型

## 故障排除

1. **任务执行失败**：检查Worker日志，确保RabbitMQ和Redis服务正常运行
2. **定时任务没有执行**：确保Beat服务正在运行，检查时区设置是否正确
3. **Worker无法连接到Broker**：检查RabbitMQ的连接信息，用户名、密码和vhost是否正确 