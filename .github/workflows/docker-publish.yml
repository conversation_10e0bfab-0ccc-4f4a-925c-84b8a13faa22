name: Docker构建与发布

on:
  push:
    branches: [ "main" ]
    tags: [ 'v*.*.*' ]
  pull_request:
    branches: [ "main" ]
  workflow_dispatch:
    inputs:
      tag:
        description: '镜像标签（留空则使用自动生成的标签）'
        required: false
        default: ''

env:
  # 镜像名称
  IMAGE_NAME: linyq1/kol_platform

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: 检出代码
        uses: actions/checkout@v3

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: 登录到Docker Hub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      # 提取元数据（tags、labels）用于Docker
      - name: 提取Docker元数据
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,format=short
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}
            type=raw,value=${{ github.event.inputs.tag }},enable=${{ github.event.inputs.tag != '' }}

      # 构建并推送Docker镜像
      - name: 构建并推送
        id: build-and-push
        uses: docker/build-push-action@v4
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max 