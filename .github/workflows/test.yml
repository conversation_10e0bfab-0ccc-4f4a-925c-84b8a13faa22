name: Test

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements-dev.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
        
    - name: Create test config
      run: |
        cp config.toml.example config.toml
        # 修改配置以使用 GitHub Actions 提供的 PostgreSQL
        sed -i 's/POSTGRES_SERVER = .*/POSTGRES_SERVER = "localhost"/g' config.toml
        sed -i 's/POSTGRES_USER = .*/POSTGRES_USER = "postgres"/g' config.toml
        sed -i 's/POSTGRES_PASSWORD = .*/POSTGRES_PASSWORD = "postgres"/g' config.toml
        sed -i 's/POSTGRES_DB = .*/POSTGRES_DB = "test"/g' config.toml
        sed -i 's/POSTGRES_PORT = .*/POSTGRES_PORT = 5432/g' config.toml
        
    - name: Wait for PostgreSQL
      run: |
        timeout 30 bash -c 'until pg_isready -h localhost -p 5432; do sleep 1; done'
        
    - name: Run database migrations
      run: |
        if [ -f "alembic.ini" ]; then
          pip install alembic
          alembic upgrade head
        fi
        
    - name: Run tests
      run: |
        pytest -xvs tests/

    - name: Run tests with coverage
      if: success()
      run: |
        pytest --cov=app --cov-report=xml --cov-report=term-missing

    - name: Upload coverage to Codecov
      if: success()
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: false