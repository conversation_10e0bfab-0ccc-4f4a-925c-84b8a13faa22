# CI/CD 工作流程说明

本项目使用GitHub Actions自动构建Docker镜像并发布到Docker Hub。

## 工作流程概述

1. 当代码推送到`main`分支或创建带有版本号的标签（如`v1.0.0`）时，触发工作流
2. 工作流会构建Docker镜像
3. 如果不是Pull Request，则将镜像推送到Docker Hub
4. 根据不同的事件类型（分支、标签等）生成不同的镜像标签

## 必要的设置

### 1. Docker Hub 访问令牌

需要在GitHub仓库设置以下密钥（Secrets）:

1. `DOCKERHUB_USERNAME`: Docker Hub用户名
2. `DOCKERHUB_TOKEN`: Docker Hub访问令牌（不是账户密码）

### 如何添加Secrets

1. 在GitHub仓库页面，点击 `Settings`
2. 在左侧菜单中选择 `Secrets and variables` > `Actions`
3. 点击 `New repository secret`
4. 添加上述两个密钥

### 如何创建Docker Hub访问令牌

1. 登录Docker Hub
2. 点击右上角的头像，选择 `Account Settings`
3. 选择 `Security` > `New Access Token`
4. 为令牌起一个名称（如"GitHub Actions"）
5. 选择适当的权限（至少需要`Read & Write`权限）
6. 复制生成的令牌，这将只显示一次！

## 镜像标签规则

- 对于`main`分支的推送：生成`latest`标签和短SHA标签
- 对于版本标签（如`v1.2.3`）：生成`1.2.3`、`1.2`和对应标签名的镜像标签
- 对于Pull Request：生成PR相关的标签，但不会推送到Docker Hub

## 手动触发构建

您也可以在GitHub界面上手动触发工作流：

1. 前往仓库的`Actions`标签页
2. 选择`Docker构建与发布`工作流
3. 点击`Run workflow`
4. 选择分支并触发工作流

## 排查问题

如果工作流失败，请检查：

1. Secrets是否正确设置
2. Docker Hub凭据是否有效
3. Dockerfile是否存在于仓库根目录
4. 构建过程中是否出现错误 