import pytest
from fastapi.testclient import TestClient
import uuid
import random
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from httpx import AsyncClient

from app.main import app
from app import schemas
from app import models
from app import crud

# 创建测试数据的夹具
@pytest.fixture
def test_send_data():
    """生成测试 SendData 数据"""
    # 生成唯一ID
    send_id = f"test_send_{uuid.uuid4()}"
    return {
        "id": send_id,
        "kol_id": f"test_kol_{uuid.uuid4()}",
        "platform": "tiktok",
        "send_status": "pending",
        "app_code": "test_app",
        "template_id": "test_template",
        "read_status": False,
        "success": False,
        "from_email": "<EMAIL>",
        "to_email": "<EMAIL>",
        "send_date": datetime.now().isoformat(),
        "export_date": datetime.now().isoformat()
    }

# 创建测试KOL夹具
@pytest.fixture
def test_kol(db):
    """创建测试用的 KOL"""
    # 生成唯一ID
    kol_id = f"test_kol_{uuid.uuid4()}"
    
    # 创建一个测试KOL
    kol_in = schemas.KOLInfoCreate(
        kol_id=kol_id,
        kol_name=f"Test KOL {kol_id}",
        username=f"test_user_{kol_id}",
        platform="tiktok",
        email=f"test_{kol_id}@example.com",
        followers_k=1.0,
        likes_k=0.5,
        mean_views_k=1.5,
        engagement_rate=5.5,
        average_views_k=1.2,
        average_likes_k=0.5,
        average_comments_k=0.1,
        most_used_hashtags=["trending", "viral"],
        country="中国"
    )
    kol = crud.kol_info.create(db, obj_in=kol_in)
    
    # 测试完成后删除这个KOL
    yield kol
    
    crud.kol_info.remove(db, id=kol.kol_id)

# 创建测试发送数据夹具
@pytest.fixture
def test_send(db, test_kol):
    """创建测试用的发送数据"""
    # 创建一个测试发送数据
    send_id = f"test_send_{uuid.uuid4()}"
    send_in = schemas.SendDataCreate(
        id=send_id,
        kol_id=test_kol.kol_id,  # 使用测试KOL的ID
        platform="tiktok",
        send_status="pending", 
        app_code="test_app",
        template_id="test_template",
        from_email="<EMAIL>",
        to_email="<EMAIL>",
        send_date=datetime.now(),
        export_date=datetime.now()
    )
    send_data = crud.send_data.create(db, obj_in=send_in)
    
    # 测试完成后删除这个发送数据
    yield send_data
    
    # 清理：删除测试数据
    try:
        crud.send_data.remove(db, id=send_data.id)
    except:
        pass  # 如果已经被测试删除了，就忽略错误


class TestSendDataAPI:
    """发送数据管理接口测试"""
    
    API_PATH = "/api/v1/send-data"
    
    def test_read_send_data(self, client, db):
        """测试获取发送数据列表"""
        response = client.get(f"{self.API_PATH}/")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        assert isinstance(data["items"], list)
        
    def test_create_send_data(self, client, db, test_kol, test_send_data):
        """测试创建发送数据"""
        # 修改测试数据，使用已存在的KOL ID
        test_data = test_send_data.copy()
        test_data["kol_id"] = test_kol.kol_id
        
        response = client.post(f"{self.API_PATH}/", json=test_data)
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_data["id"]
        assert data["kol_id"] == test_kol.kol_id
        assert data["platform"] == test_data["platform"]
        assert data["app_code"] == test_data["app_code"]
        
        # 清理：删除测试数据
        crud.send_data.remove(db, id=test_data["id"])
    
    def test_read_send_data_by_id(self, client, test_send):
        """测试根据ID获取发送数据"""
        response = client.get(f"{self.API_PATH}/{test_send.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_send.id
        assert data["kol_id"] == test_send.kol_id
        assert data["platform"] == test_send.platform
        assert data["app_code"] == test_send.app_code
    
    def test_update_send_data(self, client, db, test_send):
        """测试更新发送数据"""
        update_data = {
            "app_code": f"Updated App {test_send.id}",
            "template_id": f"Updated Template {test_send.id}",
            "send_status": "sent"
        }
        response = client.put(f"{self.API_PATH}/{test_send.id}", json=update_data)
        assert response.status_code == 200
        data = response.json()
        assert data["app_code"] == update_data["app_code"]
        assert data["template_id"] == update_data["template_id"]
        assert data["send_status"] == update_data["send_status"]
        # 确保其他字段未被改变
        assert data["kol_id"] == test_send.kol_id
    
    def test_delete_send_data(self, client, db, test_kol):
        """测试删除发送数据"""
        # 首先创建一个发送数据
        send_id = f"test_send_delete_{uuid.uuid4()}"
        send_in = schemas.SendDataCreate(
            id=send_id,
            kol_id=test_kol.kol_id,
            platform="tiktok", 
            send_status="pending",
            app_code="Test Delete App",
            template_id="Test Delete Template"
        )
        send_data = crud.send_data.create(db, obj_in=send_in)
        
        # 然后删除它
        response = client.delete(f"{self.API_PATH}/{send_data.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == send_data.id
        
        # 确认已经被删除
        deleted_send = crud.send_data.get(db, id=send_data.id)
        assert deleted_send is None
    
    def test_get_send_data_by_platform(self, client, db, test_send):
        """测试按平台获取发送数据"""
        response = client.get(f"{self.API_PATH}/?platform={test_send.platform}")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        assert isinstance(data["items"], list)
        # 验证测试发送数据在返回结果中
        found = any(item["id"] == test_send.id for item in data["items"])
        assert found, f"测试发送数据(ID: {test_send.id})未出现在API响应中"
    
    def test_get_send_data_by_kol_id(self, client, db, test_send):
        """测试按KOL ID获取发送数据"""
        response = client.get(f"{self.API_PATH}/?kol_id={test_send.kol_id}")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        assert isinstance(data["items"], list)
        # 验证测试发送数据在返回结果中
        found = any(item["id"] == test_send.id for item in data["items"])
        assert found, f"测试发送数据(ID: {test_send.id})未出现在API响应中"
    
    def test_get_send_data_by_send_status(self, client, db, test_send):
        """测试按发送状态获取发送数据"""
        response = client.get(f"{self.API_PATH}/?send_status={test_send.send_status}")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        assert isinstance(data["items"], list)
        # 验证测试发送数据在返回结果中
        found = any(item["id"] == test_send.id for item in data["items"])
        assert found, f"测试发送数据(ID: {test_send.id})未出现在API响应中"
    
    def test_get_send_data_by_kol(self, client, db, test_send):
        """测试获取指定KOL的所有发送数据"""
        response = client.get(f"{self.API_PATH}/kol/{test_send.kol_id}")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        assert isinstance(data["items"], list)
        # 验证测试发送数据在返回结果中
        found = any(item["id"] == test_send.id for item in data["items"])
        assert found, f"测试发送数据(ID: {test_send.id})未出现在API响应中"