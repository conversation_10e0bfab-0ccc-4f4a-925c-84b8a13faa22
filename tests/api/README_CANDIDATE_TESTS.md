# KOL 候选人数据接口测试指南

这个文档介绍了如何运行和维护 KOL 候选人数据接口的自动化测试。

## 测试内容

测试用例主要覆盖以下功能：

1. 基础 CRUD 操作
   - 创建候选人数据
   - 获取候选人列表
   - 获取单个候选人
   - 更新候选人数据
   - 删除候选人数据

2. 特殊查询功能
   - 按 KOL ID 查询
   - 按标签筛选
   - 按项目筛选

3. 高级搜索功能
   - 使用多个搜索条件
   - 使用不同的连接词（AND / OR）
   - 异步高级搜索（目前已禁用）

4. 边界情况和错误处理
   - 创建重复数据
   - 获取不存在的数据

## 运行测试

### 方法一：使用 pytest 命令

```bash
# 运行所有候选人数据测试
pytest -xvs tests/api/test_candidate_data.py

# 运行特定测试方法
pytest -xvs tests/api/test_candidate_data.py::TestCandidateDataAPI::test_create_candidate_data
```

### 方法二：使用运行脚本

```bash
# 使脚本可执行
chmod +x tests/run_candidate_tests.py

# 运行脚本
./tests/run_candidate_tests.py
```

## 添加新测试

添加新测试时，需要遵循以下步骤：

1. 在 `TestCandidateDataAPI` 类中添加新的测试方法
2. 确保方法名以 `test_` 开头
3. 使用 `client` 和 `db` fixtures 获取测试客户端和数据库会话
4. 使用 `test_candidate` 或 `test_candidate_data` fixtures 获取测试数据

示例：

```python
def test_new_feature(self, client, test_candidate):
    """测试新功能"""
    response = client.get(f"{self.API_PATH}/some-new-endpoint/{test_candidate.id}")
    assert response.status_code == 200
    # 其他断言...
```

## 异步测试

异步测试已使用 `@pytest.mark.skip` 标记为跳过。要启用这些测试，需要：

1. 移除 `@pytest.mark.skip` 装饰器
2. 确保已安装 `pytest-asyncio` 插件
3. 确保测试环境支持异步测试

## 维护测试

在以下情况下需要更新测试用例：

1. API 接口路径变更
2. 数据模型结构变更
3. 新增功能或修改现有功能
4. 发现边界情况或错误 