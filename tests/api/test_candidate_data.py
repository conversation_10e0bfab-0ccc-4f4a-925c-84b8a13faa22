import pytest
import uuid
import random
from datetime import datetime, timezone
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session


from app.main import app
from app import schemas
from app import models
from app import crud


# 创建测试数据的夹具
@pytest.fixture
def test_candidate_data():
    """生成测试候选人数据"""
    # 生成唯一ID
    kol_id = f"test_kol_{uuid.uuid4()}"
    current_time = datetime.now(timezone.utc)
    
    # 确保包含所有 CandidateDataCreate 必需的字段
    return {
        "kol_id": kol_id,
        "kol_name": f"Test KOL {kol_id}",
        "first_contact_date": current_time.isoformat(),
        "last_contact_date": current_time.isoformat(),
        "follow_up": "待跟进",
        "label": "潜在合作",
        "sublabel": "直播带货",
        "thread_id": f"thread_{uuid.uuid4()}",
        "project": "test_project"
    }


# 创建测试候选人的夹具
@pytest.fixture
def test_candidate(db, test_candidate_data):
    """创建测试用的候选人数据"""
    # 创建一个测试候选人
    candidate_in = schemas.CandidateDataCreate(**test_candidate_data)
    candidate = crud.candidate_data.create(db, obj_in=candidate_in)
    
    # 测试完成后删除这个候选人
    yield candidate
    
    # 使用 ID 删除，而不是 kol_id
    crud.candidate_data.remove(db, id=candidate.id)


class TestCandidateDataAPI:
    """候选人数据管理接口测试"""
    
    API_PATH = "/api/v1/candidate-data"
    
    def test_read_candidate_datas(self, client, db):
        """测试获取候选人列表"""
        response = client.get(f"{self.API_PATH}/")
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
    
    def test_create_candidate_data(self, client, db, test_candidate_data):
        """测试创建候选人数据"""
        response = client.post(f"{self.API_PATH}/", json=test_candidate_data)
        assert response.status_code == 200
        data = response.json()
        assert data["kol_id"] == test_candidate_data["kol_id"]
        assert data["kol_name"] == test_candidate_data["kol_name"]
        assert data["follow_up"] == test_candidate_data["follow_up"]
        assert data["label"] == test_candidate_data["label"]
        
        # 清理测试数据
        candidate = crud.candidate_data.get_by_kol_id(db, kol_id=test_candidate_data["kol_id"])
        crud.candidate_data.remove(db, id=candidate.id)
    
    def test_read_candidate_data(self, client, test_candidate):
        """测试根据ID获取候选人信息"""
        response = client.get(f"{self.API_PATH}/{test_candidate.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_candidate.id
        assert data["kol_id"] == test_candidate.kol_id
        assert data["kol_name"] == test_candidate.kol_name
        assert data["label"] == test_candidate.label
    
    def test_read_candidate_data_by_kol_id(self, client, test_candidate):
        """测试根据KOL ID获取候选人信息"""
        response = client.get(f"{self.API_PATH}/by-kol/{test_candidate.kol_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_candidate.id
        assert data["kol_id"] == test_candidate.kol_id
        assert data["kol_name"] == test_candidate.kol_name
    
    def test_update_candidate_data(self, client, db, test_candidate):
        """测试更新候选人信息"""
        update_data = {
            "kol_name": f"Updated KOL {test_candidate.kol_id}",
            "follow_up": "已联系",
            "label": "合作中",
            "sublabel": "短视频拍摄",
            "project": "new_test_project"
        }
        response = client.put(f"{self.API_PATH}/{test_candidate.id}", json=update_data)
        assert response.status_code == 200
        data = response.json()
        assert data["kol_name"] == update_data["kol_name"]
        assert data["follow_up"] == update_data["follow_up"]
        assert data["label"] == update_data["label"]
        assert data["sublabel"] == update_data["sublabel"]
        assert data["project"] == update_data["project"]
        # 确保其他字段未被改变
        assert data["kol_id"] == test_candidate.kol_id
    
    def test_delete_candidate_data(self, client, db, test_candidate_data):
        """测试删除候选人信息"""
        # 首先创建一个候选人
        candidate_in = schemas.CandidateDataCreate(**test_candidate_data)
        candidate = crud.candidate_data.create(db, obj_in=candidate_in)
        
        # 然后删除它
        response = client.delete(f"{self.API_PATH}/{candidate.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == candidate.id
        
        # 确认已经被删除
        deleted_candidate = crud.candidate_data.get(db, id=candidate.id)
        assert deleted_candidate is None
    
    def test_delete_candidate_data_by_thread_id(self, client, db, test_candidate_data):
        """测试通过对话线程ID删除候选人信息"""
        # 首先创建一个候选人
        candidate_in = schemas.CandidateDataCreate(**test_candidate_data)
        candidate = crud.candidate_data.create(db, obj_in=candidate_in)
        
        # 然后通过thread_id删除它
        response = client.delete(f"{self.API_PATH}/by-thread/{candidate.thread_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == candidate.id
        assert data["thread_id"] == candidate.thread_id
        
        # 确认已经被删除
        deleted_candidate = crud.candidate_data.get(db, id=candidate.id)
        assert deleted_candidate is None
    
    def test_delete_nonexistent_candidate_data_by_thread_id(self, client):
        """测试通过不存在的对话线程ID删除候选人数据"""
        nonexistent_thread_id = f"nonexistent_thread_{uuid.uuid4()}"
        response = client.delete(f"{self.API_PATH}/by-thread/{nonexistent_thread_id}")
        assert response.status_code == 404
        assert "找不到对话线程ID" in response.json()["detail"]
        
        # 确认已经被删除
        deleted_candidate = crud.candidate_data.get(db, id=candidate.id)
        assert deleted_candidate is None
    
    def test_filter_by_label(self, client, test_candidate):
        """测试按标签筛选候选人数据"""
        response = client.get(f"{self.API_PATH}/?label={test_candidate.label}")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] > 0
        # 验证筛选结果中包含测试数据
        found = False
        for item in data["items"]:
            if item["id"] == test_candidate.id:
                found = True
                break
        assert found, "筛选结果中应包含测试数据"
    
    def test_filter_by_project(self, client, test_candidate):
        """测试按项目筛选候选人数据"""
        response = client.get(f"{self.API_PATH}/?project={test_candidate.project}")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] > 0
        # 验证筛选结果中包含测试数据
        found = False
        for item in data["items"]:
            if item["id"] == test_candidate.id:
                found = True
                break
        assert found, "筛选结果中应包含测试数据"
    
    def test_advanced_search(self, client, test_candidate):
        """测试高级搜索功能"""
        search_params = {
            "conditions": [
                {"field": "label", "operator": "eq", "value": test_candidate.label},
                {"field": "kol_name", "operator": "contains", "value": test_candidate.kol_id.split("_")[1][:5]}
            ],
            "conjunction": "and",
            "skip": 0,
            "limit": 10
        }
        response = client.post(f"{self.API_PATH}/advanced-search", json=search_params)
        assert response.status_code == 200
        data = response.json()
        assert data["total"] > 0
        # 验证搜索结果中包含测试数据
        found = False
        for item in data["items"]:
            if item["id"] == test_candidate.id:
                found = True
                break
        assert found, "高级搜索结果中应包含测试数据"
    
    def test_advanced_search_with_project(self, client, test_candidate):
        """测试带项目参数的高级搜索功能"""
        search_params = {
            "conditions": [
                {"field": "kol_name", "operator": "contains", "value": "KOL"}
            ],
            "project": test_candidate.project,
            "skip": 0,
            "limit": 10
        }
        response = client.post(f"{self.API_PATH}/advanced-search", json=search_params)
        assert response.status_code == 200
        data = response.json()
        assert data["total"] > 0
        # 验证搜索结果中包含测试数据
        found = False
        for item in data["items"]:
            if item["id"] == test_candidate.id:
                found = True
                break
        assert found, "高级搜索结果中应包含测试数据"
    
    def test_advanced_search_with_or_conjunction(self, client, test_candidate):
        """测试使用OR连接符的高级搜索功能"""
        search_params = {
            "conditions": [
                {"field": "label", "operator": "eq", "value": "不存在的标签"},
                {"field": "kol_id", "operator": "eq", "value": test_candidate.kol_id}
            ],
            "conjunction": "or",
            "skip": 0,
            "limit": 10
        }
        response = client.post(f"{self.API_PATH}/advanced-search", json=search_params)
        assert response.status_code == 200
        data = response.json()
        assert data["total"] > 0
        # 验证搜索结果中包含测试数据
        found = False
        for item in data["items"]:
            if item["id"] == test_candidate.id:
                found = True
                break
        assert found, "使用OR连接符的高级搜索结果中应包含测试数据"
    
    def test_create_duplicate_candidate_data(self, client, test_candidate):
        """测试创建重复的候选人数据（相同的KOL ID）"""
        duplicate_data = {
            "kol_id": test_candidate.kol_id,  # 使用已存在的KOL ID
            "kol_name": "Duplicate KOL",
            "label": "重复测试"
        }
        response = client.post(f"{self.API_PATH}/", json=duplicate_data)
        assert response.status_code == 400  # 应该返回400错误
        assert "该KOL的候选人数据已存在" in response.json()["detail"]
    
    def test_get_nonexistent_candidate_data(self, client):
        """测试获取不存在的候选人数据"""
        nonexistent_id = 999999  # 假设这个ID不存在
        response = client.get(f"{self.API_PATH}/{nonexistent_id}")
        assert response.status_code == 404
        assert "找不到该候选人数据" in response.json()["detail"]
    
    def test_get_nonexistent_candidate_data_by_kol_id(self, client):
        """测试通过不存在的KOL ID获取候选人数据"""
        nonexistent_kol_id = f"nonexistent_{uuid.uuid4()}"
        response = client.get(f"{self.API_PATH}/by-kol/{nonexistent_kol_id}")
        assert response.status_code == 404
        assert "找不到KOL ID" in response.json()["detail"]