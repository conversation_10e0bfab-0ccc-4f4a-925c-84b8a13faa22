import pytest
from fastapi.testclient import TestClient
import uuid
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.main import app
from app import schemas
from app import models
from app import crud


# 创建测试KOL夹具
@pytest.fixture
def test_kol(db):
    """创建测试用的 KOL"""
    # 生成唯一ID
    kol_id = f"test_kol_{uuid.uuid4()}"
    
    # 创建一个测试KOL
    kol_in = schemas.KOLInfoCreate(
        kol_id=kol_id,
        kol_name=f"Test KOL {kol_id}",
        username=f"test_user_{kol_id}",
        platform="tiktok",
        email=f"test_{kol_id}@example.com",
        followers_k=1.0,
        likes_k=0.5,
        mean_views_k=1.5,
        engagement_rate=5.5,
        average_views_k=1.2,
        average_likes_k=0.5,
        average_comments_k=0.1,
        most_used_hashtags=["trending", "viral"],
        country="中国"
    )
    kol = crud.kol_info.create(db, obj_in=kol_in)
    
    # 测试完成后删除这个KOL
    yield kol
    
    crud.kol_info.remove(db, id=kol.kol_id)


# 创建测试合作表现数据夹具
@pytest.fixture
def test_performance(db, test_kol):
    """创建测试用的合作表现数据"""
    # 创建一个测试合作表现数据
    performance_in = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="测试项目A",
        kol_priority="高优先级",
        post_link="https://example.com/post/12345",
        payment="5000元",
        post_date=datetime.now() - timedelta(days=14),
        views_total=10000,
        likes_total=1000,
        comments_total=500,
        shares_total=200,
        views_day1=5000,
        likes_day1=500,
        comments_day1=250,
        shares_day1=100,
        views_day3=7500,
        likes_day3=750,
        comments_day3=350,
        shares_day3=150,
        views_day7=9000,
        likes_day7=900,
        comments_day7=450,
        shares_day7=180,
        engagement_rate=5.5,
        cpm=50.0
    )
    performance = crud.collaboration_performance.create(db, obj_in=performance_in)
    
    # 测试完成后删除这个合作表现数据
    yield performance
    
    # 清理：删除测试数据
    try:
        crud.collaboration_performance.remove(db, id=performance.id)
    except:
        pass  # 如果已经被测试删除了，就忽略错误


# 创建第二个测试合作表现数据夹具（用于测试查询功能）
@pytest.fixture
def test_performance2(db, test_kol):
    """创建第二个测试用的合作表现数据"""
    # 创建另一个测试合作表现数据，不同项目，不同日期
    performance_in = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="测试项目B",
        kol_priority="中优先级",
        post_link="https://example.com/post/67890",
        payment="3000元",
        post_date=datetime.now() - timedelta(days=7),
        views_total=8000,
        likes_total=800,
        comments_total=400,
        shares_total=160,
        views_day1=4000,
        likes_day1=400,
        comments_day1=200,
        shares_day1=80,
        views_day3=6000,
        likes_day3=600,
        comments_day3=300,
        shares_day3=120,
        views_day7=7200,
        likes_day7=720,
        comments_day7=360,
        shares_day7=144,
        engagement_rate=4.5,
        cpm=40.0
    )
    performance = crud.collaboration_performance.create(db, obj_in=performance_in)
    
    # 测试完成后删除这个合作表现数据
    yield performance
    
    # 清理：删除测试数据
    try:
        crud.collaboration_performance.remove(db, id=performance.id)
    except:
        pass  # 如果已经被测试删除了，就忽略错误


class TestCollaborationPerformanceAPI:
    """合作表现数据管理接口测试"""
    
    API_PATH = "/api/v1/collaboration-performance"
    
    def test_read_performances(self, client, db, test_performance):
        """测试获取合作表现数据列表"""
        response = client.get(f"{self.API_PATH}/")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        assert isinstance(data["items"], list)
        # 验证测试数据在返回结果中
        found = any(item["id"] == test_performance.id for item in data["items"])
        assert found, f"测试合作表现数据(ID: {test_performance.id})未出现在API响应中"
    
    def test_create_performance(self, client, db, test_kol):
        """测试创建合作表现数据"""
        performance_data = {
            "kol_id": test_kol.kol_id,
            "project": "创建测试项目",
            "kol_priority": "低优先级",
            "post_link": "https://example.com/post/create_test",
            "payment": "2000元",
            "post_date": datetime.now().isoformat(),
            "views_total": 5000,
            "likes_total": 500,
            "comments_total": 250,
            "shares_total": 100,
            "engagement_rate": 3.5,
            "cpm": 30.0
        }
        
        response = client.post(f"{self.API_PATH}/", json=performance_data)
        assert response.status_code == 200
        data = response.json()
        assert data["kol_id"] == test_kol.kol_id
        assert data["project"] == performance_data["project"]
        assert data["views_total"] == performance_data["views_total"]
        
        # 清理：删除测试数据
        crud.collaboration_performance.remove(db, id=data["id"])
    
    def test_read_performance_by_id(self, client, test_performance):
        """测试根据ID获取合作表现数据"""
        response = client.get(f"{self.API_PATH}/{test_performance.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_performance.id
        assert data["kol_id"] == test_performance.kol_id
        assert data["project"] == test_performance.project
        assert data["views_total"] == test_performance.views_total
    
    def test_read_performance_not_found(self, client):
        """测试获取不存在的合作表现数据"""
        response = client.get(f"{self.API_PATH}/99999")
        assert response.status_code == 404
    
    def test_update_performance(self, client, db, test_performance):
        """测试更新合作表现数据"""
        update_data = {
            "project": f"更新测试项目 {test_performance.id}",
            "views_total": 15000,
            "likes_total": 1500,
            "engagement_rate": 6.0
        }
        response = client.put(f"{self.API_PATH}/{test_performance.id}", json=update_data)
        assert response.status_code == 200
        data = response.json()
        assert data["project"] == update_data["project"]
        assert data["views_total"] == update_data["views_total"]
        assert data["likes_total"] == update_data["likes_total"]
        assert data["engagement_rate"] == update_data["engagement_rate"]
        # 确保其他字段未被改变
        assert data["kol_id"] == test_performance.kol_id
    
    def test_delete_performance(self, client, db, test_kol):
        """测试删除合作表现数据"""
        # 首先创建一个合作表现数据
        performance_in = schemas.CollaborationPerformanceCreate(
            kol_id=test_kol.kol_id,
            project="删除测试项目",
            kol_priority="测试优先级",
            post_link="https://example.com/post/delete_test",
            payment="1000元",
            post_date=datetime.now(),
            views_total=3000,
            likes_total=300,
            comments_total=150,
            shares_total=60
        )
        performance = crud.collaboration_performance.create(db, obj_in=performance_in)
        
        # 然后删除它
        response = client.delete(f"{self.API_PATH}/{performance.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == performance.id
        
        # 确认已经被删除
        deleted_performance = crud.collaboration_performance.get(db, id=performance.id)
        assert deleted_performance is None
    
    def test_get_performances_by_kol_id(self, client, db, test_performance, test_performance2):
        """测试按KOL ID获取合作表现数据"""
        response = client.get(f"{self.API_PATH}/?kol_id={test_performance.kol_id}")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert isinstance(data["items"], list)
        # 验证两个测试数据都在返回结果中
        ids = [item["id"] for item in data["items"]]
        assert test_performance.id in ids, f"测试合作表现数据(ID: {test_performance.id})未出现在API响应中"
        assert test_performance2.id in ids, f"测试合作表现数据(ID: {test_performance2.id})未出现在API响应中"
    
    def test_get_performances_by_project(self, client, db, test_performance):
        """测试按项目获取合作表现数据"""
        response = client.get(f"{self.API_PATH}/?project={test_performance.project}")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert isinstance(data["items"], list)
        # 验证测试数据在返回结果中
        found = any(item["id"] == test_performance.id for item in data["items"])
        assert found, f"测试合作表现数据(ID: {test_performance.id})未出现在API响应中"
    
    def test_get_performances_by_kol_id_and_project(self, client, db, test_performance):
        """测试按KOL ID和项目共同过滤合作表现数据"""
        response = client.get(
            f"{self.API_PATH}/?kol_id={test_performance.kol_id}&project={test_performance.project}"
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert isinstance(data["items"], list)
        # 验证测试数据在返回结果中，且只包含符合条件的数据
        assert len(data["items"]) >= 1, "应至少返回一条数据"
        found = any(item["id"] == test_performance.id for item in data["items"])
        assert found, f"测试合作表现数据(ID: {test_performance.id})未出现在API响应中"
    
    def test_get_performances_by_kol_endpoint(self, client, db, test_performance, test_performance2):
        """测试通过专用的KOL端点获取合作表现数据"""
        response = client.get(f"{self.API_PATH}/by-kol/{test_performance.kol_id}")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert isinstance(data["items"], list)
        # 验证两个测试数据都在返回结果中
        ids = [item["id"] for item in data["items"]]
        assert test_performance.id in ids, f"测试合作表现数据(ID: {test_performance.id})未出现在API响应中"
        assert test_performance2.id in ids, f"测试合作表现数据(ID: {test_performance2.id})未出现在API响应中"
    
    def test_get_performances_by_project_endpoint(self, client, db, test_performance):
        """测试通过专用的项目端点获取合作表现数据"""
        response = client.get(f"{self.API_PATH}/by-project/{test_performance.project}")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert isinstance(data["items"], list)
        # 验证测试数据在返回结果中
        found = any(item["id"] == test_performance.id for item in data["items"])
        assert found, f"测试合作表现数据(ID: {test_performance.id})未出现在API响应中"
    
    def test_get_performances_by_date_range(self, client, db, test_performance, test_performance2):
        """测试按日期范围获取合作表现数据"""
        # 设置一个包含两个测试数据的日期范围
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()
        
        response = client.get(
            f"{self.API_PATH}/by-date-range/?start_date={start_date}&end_date={end_date}"
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert isinstance(data["items"], list)
        # 验证两个测试数据都在返回结果中
        ids = [item["id"] for item in data["items"]]
        assert test_performance.id in ids, f"测试合作表现数据(ID: {test_performance.id})未出现在API响应中"
        assert test_performance2.id in ids, f"测试合作表现数据(ID: {test_performance2.id})未出现在API响应中"
    
    def test_async_get_performances(self, client, db, test_performance):
        """测试异步获取合作表现数据列表"""
        response = client.get(f"{self.API_PATH}/async/")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        assert isinstance(data["items"], list)