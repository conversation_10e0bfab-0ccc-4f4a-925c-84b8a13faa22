import pytest
from datetime import datetime, timedelta
import uuid

from sqlalchemy.orm import Session

from app import crud, schemas
from app.models.collaboration_performance import CollaborationPerformance


# 创建测试KOL夹具
@pytest.fixture
def test_kol(db):
    """创建测试用的 KOL"""
    # 生成唯一ID
    kol_id = f"test_kol_{uuid.uuid4()}"
    
    # 创建一个测试KOL
    kol_in = schemas.KOLInfoCreate(
        kol_id=kol_id,
        kol_name=f"Test KOL {kol_id}",
        username=f"test_user_{kol_id}",
        platform="tiktok",
        email=f"test_{kol_id}@example.com",
        followers_k=1.0,
        likes_k=0.5,
        mean_views_k=1.5,
        engagement_rate=5.5,
        average_views_k=1.2,
        average_likes_k=0.5,
        average_comments_k=0.1,
        most_used_hashtags=["trending", "viral"],
        country="中国"
    )
    kol = crud.kol_info.create(db, obj_in=kol_in)
    
    # 测试完成后删除这个KOL
    yield kol
    
    crud.kol_info.remove(db, id=kol.kol_id)


def test_create_performance(db: Session, test_kol):
    """测试创建合作表现数据"""
    # 准备测试数据
    performance_data = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="测试项目",
        kol_priority="高优先级",
        post_link="https://example.com/post/12345",
        payment="5000元",
        post_date=datetime.now(),
        views_total=10000,
        likes_total=1000,
        comments_total=500,
        shares_total=200,
        engagement_rate=5.0,
        cpm=50.0
    )
    
    # 创建数据
    performance = crud.collaboration_performance.create(db, obj_in=performance_data)
    
    # 验证结果
    assert performance.kol_id == performance_data.kol_id
    assert performance.project == performance_data.project
    assert performance.views_total == performance_data.views_total
    assert performance.id is not None
    
    # 清理
    crud.collaboration_performance.remove(db, id=performance.id)


def test_get_performance(db: Session, test_kol):
    """测试获取单条合作表现数据"""
    # 创建测试数据
    performance_data = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="测试获取项目",
        kol_priority="高优先级",
        post_date=datetime.now()
    )
    performance = crud.collaboration_performance.create(db, obj_in=performance_data)
    
    # 测试获取数据
    stored_performance = crud.collaboration_performance.get(db, id=performance.id)
    assert stored_performance
    assert stored_performance.id == performance.id
    assert stored_performance.kol_id == test_kol.kol_id
    assert stored_performance.project == "测试获取项目"
    
    # 清理
    crud.collaboration_performance.remove(db, id=performance.id)


def test_get_performances(db: Session, test_kol):
    """测试获取多条合作表现数据"""
    # 创建两条测试数据
    performance_data1 = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="测试项目1",
        post_date=datetime.now()
    )
    performance_data2 = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="测试项目2",
        post_date=datetime.now()
    )
    performance1 = crud.collaboration_performance.create(db, obj_in=performance_data1)
    performance2 = crud.collaboration_performance.create(db, obj_in=performance_data2)
    
    # 测试获取数据列表
    performances = crud.collaboration_performance.get_multi(db, skip=0, limit=100)
    assert len(performances) >= 2
    # 验证两条数据都在结果中
    assert any(p.id == performance1.id for p in performances)
    assert any(p.id == performance2.id for p in performances)
    
    # 清理
    crud.collaboration_performance.remove(db, id=performance1.id)
    crud.collaboration_performance.remove(db, id=performance2.id)


def test_update_performance(db: Session, test_kol):
    """测试更新合作表现数据"""
    # 创建测试数据
    performance_data = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="原始项目",
        kol_priority="中优先级",
        views_total=5000,
        likes_total=500
    )
    performance = crud.collaboration_performance.create(db, obj_in=performance_data)
    
    # 准备更新数据
    update_data = schemas.CollaborationPerformanceUpdate(
        project="更新后的项目",
        kol_priority="高优先级",
        views_total=10000,
        likes_total=1000
    )
    
    # 执行更新
    updated_performance = crud.collaboration_performance.update(
        db, db_obj=performance, obj_in=update_data
    )
    
    # 验证结果
    assert updated_performance.id == performance.id
    assert updated_performance.kol_id == test_kol.kol_id
    assert updated_performance.project == update_data.project
    assert updated_performance.kol_priority == update_data.kol_priority
    assert updated_performance.views_total == update_data.views_total
    assert updated_performance.likes_total == update_data.likes_total
    
    # 清理
    crud.collaboration_performance.remove(db, id=performance.id)


def test_delete_performance(db: Session, test_kol):
    """测试删除合作表现数据"""
    # 创建测试数据
    performance_data = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="待删除项目"
    )
    performance = crud.collaboration_performance.create(db, obj_in=performance_data)
    
    # 确认数据已存在
    stored_performance = crud.collaboration_performance.get(db, id=performance.id)
    assert stored_performance
    
    # 执行删除
    deleted_performance = crud.collaboration_performance.remove(db, id=performance.id)
    assert deleted_performance.id == performance.id
    
    # 确认已删除
    stored_performance = crud.collaboration_performance.get(db, id=performance.id)
    assert stored_performance is None


def test_get_by_kol_id(db: Session, test_kol):
    """测试通过KOL ID获取合作表现数据"""
    # 创建两条测试数据
    performance_data1 = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="测试项目1"
    )
    performance_data2 = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="测试项目2"
    )
    performance1 = crud.collaboration_performance.create(db, obj_in=performance_data1)
    performance2 = crud.collaboration_performance.create(db, obj_in=performance_data2)
    
    # 测试按KOL ID查询
    performances = crud.collaboration_performance.get_by_kol_id(db, kol_id=test_kol.kol_id)
    assert len(performances) >= 2
    # 验证两条数据都在结果中
    assert any(p.id == performance1.id for p in performances)
    assert any(p.id == performance2.id for p in performances)
    
    # 清理
    crud.collaboration_performance.remove(db, id=performance1.id)
    crud.collaboration_performance.remove(db, id=performance2.id)


def test_get_by_project(db: Session, test_kol):
    """测试通过项目名称获取合作表现数据"""
    # 创建两条测试数据，项目名称相同
    project_name = f"相同项目_{uuid.uuid4()}"
    performance_data1 = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project=project_name
    )
    performance_data2 = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project=project_name
    )
    performance1 = crud.collaboration_performance.create(db, obj_in=performance_data1)
    performance2 = crud.collaboration_performance.create(db, obj_in=performance_data2)
    
    # 测试按项目名称查询
    performances = crud.collaboration_performance.get_by_project(db, project=project_name)
    assert len(performances) >= 2
    # 验证两条数据都在结果中
    assert any(p.id == performance1.id for p in performances)
    assert any(p.id == performance2.id for p in performances)
    
    # 清理
    crud.collaboration_performance.remove(db, id=performance1.id)
    crud.collaboration_performance.remove(db, id=performance2.id)


def test_get_by_post_date_range(db: Session, test_kol):
    """测试通过发布日期范围获取合作表现数据"""
    # 创建两条测试数据，发布日期不同
    now = datetime.now()
    one_week_ago = now - timedelta(days=7)
    two_weeks_ago = now - timedelta(days=14)
    
    performance_data1 = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="日期测试项目1",
        post_date=one_week_ago
    )
    performance_data2 = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project="日期测试项目2",
        post_date=two_weeks_ago
    )
    performance1 = crud.collaboration_performance.create(db, obj_in=performance_data1)
    performance2 = crud.collaboration_performance.create(db, obj_in=performance_data2)
    
    # 测试按日期范围查询（包含这两周）
    start_date = now - timedelta(days=21)  # 三周前
    end_date = now
    
    performances = crud.collaboration_performance.get_by_post_date_range(
        db, start_date=start_date, end_date=end_date
    )
    assert len(performances) >= 2
    # 验证两条数据都在结果中
    assert any(p.id == performance1.id for p in performances)
    assert any(p.id == performance2.id for p in performances)
    
    # 测试按日期范围查询（只包含一周前的）
    start_date = now - timedelta(days=10)  # 10天前
    end_date = now
    
    performances = crud.collaboration_performance.get_by_post_date_range(
        db, start_date=start_date, end_date=end_date
    )
    # 验证只有一周前的数据在结果中
    assert any(p.id == performance1.id for p in performances)
    assert not any(p.id == performance2.id for p in performances)
    
    # 清理
    crud.collaboration_performance.remove(db, id=performance1.id)
    crud.collaboration_performance.remove(db, id=performance2.id)


def test_get_by_kol_id_and_project(db: Session, test_kol):
    """测试通过KOL ID和项目名称共同过滤获取合作表现数据"""
    # 创建两条测试数据，KOL ID相同但项目名称不同
    project1 = f"项目A_{uuid.uuid4()}"
    project2 = f"项目B_{uuid.uuid4()}"
    
    performance_data1 = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project=project1
    )
    performance_data2 = schemas.CollaborationPerformanceCreate(
        kol_id=test_kol.kol_id,
        project=project2
    )
    performance1 = crud.collaboration_performance.create(db, obj_in=performance_data1)
    performance2 = crud.collaboration_performance.create(db, obj_in=performance_data2)
    
    # 测试按KOL ID和项目名称共同查询
    performances = crud.collaboration_performance.get_by_kol_id_and_project(
        db, kol_id=test_kol.kol_id, project=project1
    )
    # 验证只有项目A的数据在结果中
    assert len(performances) == 1
    assert performances[0].id == performance1.id
    
    # 清理
    crud.collaboration_performance.remove(db, id=performance1.id)
    crud.collaboration_performance.remove(db, id=performance2.id) 