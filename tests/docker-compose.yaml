services:
  postgres:
    image: postgres:17-alpine
    container_name: kol_db_pgsql
    restart: unless-stopped
    environment:
      POSTGRES_DB: auto_dev_db
      POSTGRES_USER: linyq
      POSTGRES_PASSWORD: 12345678linyq
    volumes:
      - ./postgres/data:/var/lib/postgresql/data
      - ./postgres/backups:/backups
    ports:
      - "5432:5432"

  redis:
    image: redis:7.2-alpine
    container_name: kol_db_redis
    restart: unless-stopped
    command: redis-server --save 60 1 --loglevel warning
    volumes:
      - ./redis/data:/data
    ports:
      - "6379:6379"

  rabbitmq:
    image: rabbitmq:3-management
    container_name: kol_db_rabbitmq
    ports:
      - "5672:5672"   # AMQP 协议端口
      - "15672:15672" # 管理界面端口
    volumes:
      - ./rabbitmq:/var/lib/rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=12345678
      - RABBITMQ_DEFAULT_VHOST=/
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 3