from fastapi.testclient import TestClient
import pytest

from app.main import app
from app.core.config import settings

client = TestClient(app)


def test_read_main():
    """测试主应用根路径"""
    response = client.get("/")
    assert response.status_code == 200


def test_read_docs():
    """测试API文档是否可访问"""
    response = client.get("/docs")
    assert response.status_code == 200


def test_openapi_schema():
    """测试OpenAPI模式是否可访问"""
    # 从配置获取OpenAPI URL
    openapi_url = f"{settings.API_V1_STR}/openapi.json"  # 应该是 /api/v1/openapi.json
    
    response = client.get(openapi_url)
    assert response.status_code == 200, f"无法访问OpenAPI架构，URL: {openapi_url}，状态码: {response.status_code}"
    
    # 验证schema包含必要的字段
    schema = response.json()
    assert "paths" in schema, "OpenAPI schema 缺少 'paths' 字段"
    assert "components" in schema, "OpenAPI schema 缺少 'components' 字段"
    assert "schemas" in schema.get("components", {}), "OpenAPI schema 缺少 'components.schemas' 字段" 