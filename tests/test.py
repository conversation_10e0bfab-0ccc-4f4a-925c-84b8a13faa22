# #!/usr/bin/env python
# # -*- coding: UTF-8 -*-
#
# '''
# @Project ：KOL-python
# @File    ：test.py
# <AUTHOR>
# @Date    ：2025/4/17 16:15
# '''
# from app.services.feishu_lark_service import Lark
# lark_client = Lark()
# items = lark_client.search_table_all_records("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya",
#                                                      search_params={
#                                                          "field_names": [
#                                                              "Filter Name",
#
#                                                          ],
#                                                          "filter": {
#                                                              "conjunction": "and",
#                                                              "conditions": [
#                                                                  {
#                                                                      "field_name": "Filter Name",
#                                                                      "operator": "isNotEmpty",
#                                                                      "value": []
#                                                                  }
#
#                                                              ]
#                                                          },
#                                                          "automatic_fields": False
#                                                      })
# import re
#
# filter_num = []
#
# for item in items:
#     text = item['fields']['Filter Name'][0]['text'].strip()
#     match = re.search(r'[-（(]?\s*([0-9]+)\s*$', text)
#
#     if match:
#         last_number = match.group(1)
#         print(last_number)
#         filter_num.append(last_number)
#     else:
#         print(f"No match found: {text}\n")
#
# # 求和
# # 求和过程打印。3月四号到四月17号的数据 85786+12349=98135
# print("\n🧮 求和过程：")
# total = 0
# for i, num in enumerate(filter_num):
#     total += int(num)
#     print(f"Step {i+1}: +{num} ➜ {total}")
#
# print(f"\n✅ 最终总和：{total}")
import uuid


print(str(uuid.uuid4()))