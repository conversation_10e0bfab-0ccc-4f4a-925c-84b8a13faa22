#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试运行脚本
"""

import argparse
import sys
import os
import subprocess

def main():
    parser = argparse.ArgumentParser(description="运行KOL-Dashboard API测试")
    parser.add_argument(
        "-m", "--module", 
        help="指定要测试的模块，例如tests/api/test_kol_info.py"
    )
    parser.add_argument(
        "-f", "--function", 
        help="指定要测试的函数，例如TestKOLInfoAPI::test_read_kol_info"
    )
    parser.add_argument(
        "-v", "--verbose", 
        action="store_true", 
        help="显示详细输出"
    )
    parser.add_argument(
        "-s", "--show-output", 
        action="store_true", 
        help="显示测试中的输出"
    )
    parser.add_argument(
        "-c", "--coverage", 
        action="store_true", 
        help="生成测试覆盖率报告"
    )
    
    args = parser.parse_args()
    
    # 构建测试命令
    cmd = ["python", "-m", "pytest"]
    
    # 添加模块
    if args.module:
        cmd.append(args.module)
        if args.function:
            cmd[-1] = f"{cmd[-1]}::{args.function}"
    
    # 添加选项
    if args.verbose:
        cmd.append("-v")
    if args.show_output:
        cmd.append("-s")
    
    # 覆盖率报告
    if args.coverage:
        cmd.extend(["--cov=app", "--cov-report=term", "--cov-report=html"])
    
    print(f"执行测试命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    
    if args.coverage:
        print("\n覆盖率报告已生成到 htmlcov/ 目录")
        print("可以在浏览器中打开 htmlcov/index.html 查看详细报告")
    
    return result.returncode

if __name__ == "__main__":
    sys.exit(main()) 