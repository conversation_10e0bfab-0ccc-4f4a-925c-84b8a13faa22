import pytest
import asyncio
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.testclient import TestClient

from app.main import app
from app.db.base import Base
from app.db.session import engine, SessionLocal, AsyncSessionLocal
from app.api import deps

# 同步数据库会话夹具
@pytest.fixture(scope="function")
def db() -> Generator[Session, None, None]:
    """提供测试数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 异步数据库会话夹具
@pytest.fixture(scope="function")
async def async_db() -> AsyncGenerator[AsyncSession, None]:
    """提供异步测试数据库会话"""
    async with AsyncSessionLocal() as session:
        yield session

# 测试客户端夹具
@pytest.fixture(scope="module")
def client() -> Generator[TestClient, None, None]:
    """提供 TestClient 实例"""
    with TestClient(app) as c:
        yield c

# 应用程序全局 TestClient
@pytest.fixture(scope="session")
def global_client():
    """提供全局测试客户端实例"""
    with TestClient(app) as c:
        yield c

# 创建一个覆盖依赖的函数，提供测试时使用的会话
def override_get_db():
    """覆盖数据库依赖注入"""
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

# 在全局范围设置依赖覆盖
app.dependency_overrides[deps.get_db] = override_get_db

# 全局数据库会话，用于设置/拆卸
@pytest.fixture(scope="session")
def setup_db():
    """提供会话级别的数据库连接，用于全局设置"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close() 