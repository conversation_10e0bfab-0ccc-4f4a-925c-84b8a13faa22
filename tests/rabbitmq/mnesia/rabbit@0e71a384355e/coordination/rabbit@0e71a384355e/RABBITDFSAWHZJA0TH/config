#{id => {rabbitmq_metadata,rabbit@0e71a384355e},
  machine =>
      {module,khepri_machine,
              #{member => {rabbitmq_metadata,rabbit@0e71a384355e},
                store_id => rabbitmq_metadata}},
  friendly_name => "RabbitMQ metadata store",
  cluster_name => rabbitmq_metadata,uid => <<"RABBITDFSAWHZJA0TH">>,
  initial_members => [],
  log_init_args => #{uid => <<"RABBITDFSAWHZJA0TH">>},
  tick_timeout => 1000,broadcast_time => 100,
  install_snap_rpc_timeout => 120000,await_condition_timeout => 30000}.