# 应用设置
PROJECT_NAME = "KOL 数据管理与分析平台"
API_V1_STR = "/api/v1"
VERSION = "2.2.1"
LOG_LEVEL = "INFO"

# CORS设置
BACKEND_CORS_ORIGINS = ["http://localhost:3000", "http://localhost:8000", "http://localhost:8080"]

# 是否使用SQLite (设置为false使用PostgreSQL)
USE_SQLITE = false

# 数据库设置 - 必须配置
[database]
POSTGRES_SERVER = "*************"
POSTGRES_USER = "admin"
POSTGRES_PASSWORD = "autodb123456!A"
POSTGRES_DB = "kol_db_v2"
POSTGRES_PORT = 35432

# Redis设置 - 必须配置
[redis]
REDIS_HOST = "*************"
REDIS_PORT = 36379
REDIS_PASSWORD = ""
REDIS_DB = 6

# RabbitMQ设置 - 必须配置
[rabbitmq]
RABBITMQ_HOST = "**************"
RABBITMQ_PORT = 5672
RABBITMQ_USER = "admin"
RABBITMQ_PASSWORD = "12345678"
RABBITMQ_VHOST = "/"

# Celery设置
[celery]
CELERY_WORKER_CONCURRENCY = 6
CELERY_MONITORING_ENABLED = true
CELERY_TASK_TRACK_STARTED = true
CELERY_RESULT_EXPIRES = 86400

# RedBeat设置
[redbeat]
REDBEAT_KEY_PREFIX = "redbeat:"
REDBEAT_LOCK_KEY = "redbeat:lock:"
REDBEAT_LOCK_TIMEOUT = 60

# Instagram 凭据
[instagram]
IG_USERNAME = "<EMAIL>"
IG_PASSWORD = "lingyinqvan456"

# 飞书 Lark 配置
[feishu]
LARK_APP_ID = "********************"
LARK_APP_SECRET = "0PStyxmXLUnUKq5KvS5iCbmg8z4z6nkJ"
FEISHU_EMAIL_APP_TOKEN = "KNnaw2wdPiwbIDk1ZlDc2AX6nIc"

# 测试邮箱：tblLjRnZR0d9mjzm  生产邮箱：tblUdyWvC0X5Sg9k
FEISHU_EMAIL_TABLE_ID = "tblUdyWvC0X5Sg9k"

# 测试 webhook: "https://laientech.feishu.cn/base/automation/webhook/event/NAq7aFdEjwELsahHfxoca7Q6nvi"
# 生产 webhook: "https://laientech.feishu.cn/base/automation/webhook/event/ZKYvaErb7wK00khMGqFcs6NsnGd"
FEISHU_WEB_HOOK_URL = "https://laientech.feishu.cn/base/automation/webhook/event/ZKYvaErb7wK00khMGqFcs6NsnGd"

# 是否更新数据库的开关 (省钱开关 true:花更多的钱 / false:拒绝重复花钱)
UPDATE = false
