# 自动创建KOL解决方案

## 问题背景

由于取消了KOL是否存在的判断，当需要往video info存储数据的时候，若KOL不存在就会报错。为了解决这个问题，我们实现了一个自动创建KOL的解决方案。

## 解决方案

### 核心思路

1. 当创建video info时，如果指定的KOL不存在，自动创建一个新的KOL记录
2. 新创建的KOL只填写必填字段（kol_id和kol_name），其余字段为空
3. 保持每个KOL ID的唯一性，避免所有未知KOL关联到同一个默认记录

## 实现细节

### 修改的文件

1. **`/app/api/v1/endpoints/video_info.py`**
   - 修改 `create_video` 函数
   - 增加KOL存在性检查逻辑
   - 当KOL不存在时自动创建新KOL

### 工作流程

1. **接收video创建请求**
   - 提取请求中的 `kol_id`

2. **检查KOL是否存在**
   - 使用 `kol_info.get(db, id=video_in.kol_id)` 查询KOL

3. **处理KOL不存在的情况**
   - 创建新的KOL记录，只填写必填字段：
     - `kol_id`: 使用原始的kol_id
     - `kol_name`: 设置为kol_id的值
     - `source`: 标记为"auto_created"
   - 其余字段保持为空（None）

4. **继续创建video记录**
   - 使用原始的video_in数据创建video记录
   - KOL关联正常建立

## 代码示例

```python
# 在create_video函数中的处理逻辑
if not kol_info.get(db, id=video_in.kol_id):
    # KOL不存在，创建新的KOL
    new_kol_data = KOLInfoCreate(
        kol_id=video_in.kol_id,
        kol_name=video_in.kol_id,  # 使用kol_id作为名称
        source="auto_created"  # 标记为自动创建
        # 其余字段保持默认值（None）
    )
    kol_info.create(db, obj_in=new_kol_data)
```

## 优势

1. **数据完整性**: 确保video info能够正常入库，不会因为KOL不存在而失败
2. **唯一性保证**: 每个KOL ID对应唯一的KOL记录，避免查询混乱
3. **向后兼容**: 不影响现有的KOL和video数据
4. **自动化处理**: 无需手动干预，系统自动处理
5. **可追溯性**: 通过source字段可以识别自动创建的KOL

## 注意事项

1. **数据质量**: 自动创建的KOL信息不完整，后续可能需要人工补充
2. **业务逻辑**: 需要考虑自动创建的KOL在业务流程中的处理方式
3. **查询优化**: 在KOL相关查询中可能需要考虑过滤自动创建的记录

## 监控和维护

1. **监控自动创建的KOL数量**
   ```sql
   SELECT COUNT(*) FROM kol_info WHERE source = 'auto_created';
   ```

2. **定期审查自动创建的KOL**
   - 检查是否有重复或无效的KOL ID
   - 考虑补充完整的KOL信息

3. **数据清理**
   - 定期清理无关联video的自动创建KOL
   - 合并重复的KOL记录

## 使用方法

该解决方案已集成到video创建流程中，无需额外配置。当调用video创建API时，系统会自动处理KOL不存在的情况。

### API调用示例

```python
# 正常调用video创建API
response = requests.post("/api/v1/video-info/", json={
    "kol_id": "some_unknown_kol_id",
    "video_title": "测试视频",
    # ... 其他video字段
})

# 如果kol_id不存在，系统会：
# 1. 自动创建KOL记录
# 2. 创建video记录
# 3. 返回成功响应
```

该方案确保了系统的稳定性和数据完整性，同时保持了良好的可维护性。